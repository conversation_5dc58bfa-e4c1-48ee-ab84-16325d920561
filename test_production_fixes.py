#!/usr/bin/env python3
"""
Integration test for production-ready GCP Inventory Tool.
Tests all the fixes and enhancements implemented.
"""
import os
import sys
import tempfile
import json
import time
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import datetime

# Add the project root to the Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_duration_handling():
    """Test the enhanced duration handling."""
    print("Testing duration handling...")
    
    from gcp_inventory_tool.utils.duration_to_second import duration_to_seconds_str
    from google.protobuf import duration_pb2
    
    # Test datetime.timedelta
    td = datetime.timedelta(seconds=300, microseconds=500000)
    result = duration_to_seconds_str(td)
    assert result == "300", f"Expected '300', got '{result}'"
    
    # Test protobuf Duration
    duration = duration_pb2.Duration()
    duration.seconds = 120
    duration.nanos = 500000000
    result = duration_to_seconds_str(duration)
    assert result == "120", f"Expected '120', got '{result}'"
    
    # Test None
    result = duration_to_seconds_str(None)
    assert result is None, f"Expected None, got '{result}'"
    
    print("✓ Duration handling tests passed")


def test_dynamic_regions():
    """Test the dynamic region management system."""
    print("Testing dynamic region management...")
    
    from gcp_inventory_tool.utils.get_regions import (
        get_available_regions, get_regions_for_service, 
        clear_region_cache, get_cache_stats
    )
    
    # Mock credentials
    mock_credentials = Mock()
    
    # Test fallback regions when API call fails
    with patch('gcp_inventory_tool.utils.get_regions.compute_v1.RegionsClient') as mock_client:
        mock_client.side_effect = Exception("API Error")
        
        regions = get_available_regions("test-project", mock_credentials, "compute")
        assert len(regions) > 0, "Should return fallback regions"
        assert "us-central1" in regions, "Should include us-central1 in fallback"
    
    # Test service-specific regions
    regions = get_regions_for_service("artifact_registry_repo", "test-project", mock_credentials)
    assert len(regions) > 0, "Should return regions for artifact registry"
    
    # Test cache functionality
    clear_region_cache()
    stats = get_cache_stats()
    assert stats['total_entries'] == 0, "Cache should be empty after clear"
    
    print("✓ Dynamic region management tests passed")


def test_security_manager():
    """Test the security and compliance features."""
    print("Testing security manager...")

    from gcp_inventory_tool.core.settings import InventoryConfig, SecurityConfig, ComplianceConfig
    from gcp_inventory_tool.core.security import SecurityManager

    # Create test configuration with temporary audit log path
    with tempfile.TemporaryDirectory() as temp_dir:
        config = InventoryConfig()
        config.security = SecurityConfig(mask_sensitive_data=True)
        config.compliance = ComplianceConfig(
            enable_audit_trail=True,
            audit_log_path=os.path.join(temp_dir, "audit.log")
        )

        security_manager = SecurityManager(config)

        # Test data sanitization
        test_data = {
            "email": "<EMAIL>",
            "password": "secret123",
            "normal_field": "normal_value"
        }

        sanitized = security_manager.sanitize_data(test_data)
        assert "***" in sanitized["email"], "Email should be partially masked"
        assert sanitized["password"] != "secret123", "Password should be masked"
        assert sanitized["normal_field"] == "normal_value", "Normal field should be unchanged"

        # Test audit logging
        security_manager.log_audit_event(
            event_type="test",
            user_id="test-user",
            project_id="test-project",
            service="test-service",
            action="test-action",
            resource_count=10,
            success=True
        )

        # Test security report
        report = security_manager.generate_security_report()
        assert "total_events" in report, "Report should contain total events"
        assert report["total_events"] >= 1, "Should have at least one event"

    print("✓ Security manager tests passed")


def test_performance_monitor():
    """Test the performance monitoring system."""
    print("Testing performance monitor...")

    from gcp_inventory_tool.core.settings import InventoryConfig, PerformanceConfig
    from gcp_inventory_tool.core.metrics import PerformanceMonitor, PerformanceProfiler

    config = InventoryConfig()
    config.performance = PerformanceConfig(enable_memory_monitoring=False)  # Disable to avoid permission issues
    monitor = PerformanceMonitor(config)
    
    # Test operation tracking
    operation_id = "test-op-1"
    service_name = "test-service"
    
    monitor.start_operation(operation_id, service_name)
    time.sleep(0.1)  # Simulate work
    duration = monitor.end_operation(operation_id, service_name, True, 5)
    
    assert duration > 0, "Duration should be positive"
    
    # Test metrics retrieval
    metrics = monitor.get_service_metrics(service_name)
    assert service_name in metrics, "Service should be in metrics"
    assert metrics[service_name].total_requests == 1, "Should have one request"
    assert metrics[service_name].successful_requests == 1, "Should have one successful request"
    
    # Test performance profiler context manager
    with PerformanceProfiler(monitor, "test-op-2", service_name) as profiler:
        profiler.add_resources(10)
        time.sleep(0.05)
    
    # Test performance summary
    summary = monitor.get_performance_summary()
    assert "total_requests" in summary, "Summary should contain total requests"
    assert summary["total_requests"] >= 2, "Should have at least 2 requests"
    
    monitor.stop()
    print("✓ Performance monitor tests passed")


def test_resilience_manager():
    """Test the resilience and error handling system."""
    print("Testing resilience manager...")
    
    from gcp_inventory_tool.core.settings import InventoryConfig
    from gcp_inventory_tool.core.resilience import ResilienceManager, CircuitBreakerOpenError
    from google.api_core import exceptions as api_exceptions
    
    config = InventoryConfig()
    resilience_manager = ResilienceManager(config)
    
    # Test successful execution
    def successful_func():
        return "success"
    
    result = resilience_manager.execute_with_resilience(
        successful_func, "test-service", "test-operation"
    )
    assert result == "success", "Should return success"
    
    # Test retry on retryable exception
    call_count = 0
    def failing_func():
        nonlocal call_count
        call_count += 1
        if call_count < 3:
            raise api_exceptions.DeadlineExceeded("Timeout")
        return "success_after_retry"
    
    result = resilience_manager.execute_with_resilience(
        failing_func, "test-service", "test-operation"
    )
    assert result == "success_after_retry", "Should succeed after retries"
    assert call_count == 3, "Should have retried 3 times"
    
    # Test circuit breaker stats
    stats = resilience_manager.get_resilience_stats()
    assert "circuit_breakers" in stats, "Stats should contain circuit breakers"
    assert "retry_configs" in stats, "Stats should contain retry configs"
    
    print("✓ Resilience manager tests passed")


def test_health_checker():
    """Test the health monitoring system."""
    print("Testing health checker...")
    
    from gcp_inventory_tool.core.settings import InventoryConfig
    from gcp_inventory_tool.core.health import HealthChecker, HealthStatus
    
    config = InventoryConfig()
    health_checker = HealthChecker(config)
    
    # Test system resources check
    result = health_checker.check_system_resources()
    assert result.name == "system_resources", "Should check system resources"
    assert result.status in [HealthStatus.HEALTHY, HealthStatus.DEGRADED, HealthStatus.UNHEALTHY], "Should have valid status"
    
    # Test configuration check
    result = health_checker.check_configuration()
    assert result.name == "configuration", "Should check configuration"
    
    # Test dependencies check
    result = health_checker.check_dependencies()
    assert result.name == "dependencies", "Should check dependencies"
    
    # Test overall health
    results = health_checker.run_all_checks()
    assert len(results) >= 3, "Should have at least 3 health checks"
    
    overall_health = health_checker.get_overall_health()
    assert isinstance(overall_health, HealthStatus), "Should return HealthStatus"
    
    # Test health summary
    summary = health_checker.get_health_summary()
    assert "overall_status" in summary, "Summary should contain overall status"
    assert "checks" in summary, "Summary should contain individual checks"
    
    print("✓ Health checker tests passed")


def test_configuration_system():
    """Test the enhanced configuration management."""
    print("Testing configuration system...")
    
    from gcp_inventory_tool.core.settings import (
        InventoryConfig, SecurityConfig, PerformanceConfig,
        create_config_from_dict, load_config_from_environment
    )
    
    # Test configuration creation
    config_dict = {
        "projects": ["test-project-1", "test-project-2"],
        "environment": "testing",
        "security": {
            "max_concurrent_requests": 5,
            "enable_audit_logging": True
        },
        "performance": {
            "max_workers": 3,
            "enable_caching": False
        }
    }
    
    config = create_config_from_dict(config_dict)
    assert len(config.projects) == 2, "Should have 2 projects"
    assert config.environment == "testing", "Should be testing environment"
    assert config.security.max_concurrent_requests == 5, "Should have 5 max concurrent requests"
    assert config.performance.max_workers == 3, "Should have 3 max workers"
    
    # Test validation
    errors = config.validate()
    assert len(errors) == 0, f"Configuration should be valid, got errors: {errors}"
    
    # Test environment overrides
    config.apply_environment_overrides()
    assert config.monitoring.log_level == "INFO", "Should apply testing environment overrides"
    
    # Test environment variable loading
    with patch.dict(os.environ, {
        'GCP_INVENTORY_PROJECTS': 'env-project-1,env-project-2',
        'GCP_INVENTORY_MAX_WORKERS': '4'
    }):
        env_config = load_config_from_environment()
        assert "projects" in env_config, "Should load projects from environment"
        assert len(env_config["projects"]) == 2, "Should have 2 projects from environment"
    
    print("✓ Configuration system tests passed")


def test_excel_formatter_sanitization():
    """Test the Excel formatter with character sanitization."""
    print("Testing Excel formatter sanitization...")
    
    from gcp_inventory_tool.formatters.excel_formatter import ExcelFormatter
    
    formatter = ExcelFormatter()
    
    # Test sanitization of illegal characters
    test_string = "Normal text\x07\x08\x0B\x0C\x0E\x1F with illegal chars"
    sanitized = formatter._sanitize_for_excel(test_string)
    
    # Should remove illegal characters but keep normal text
    assert "Normal text" in sanitized, "Should keep normal text"
    assert "\x07" not in sanitized, "Should remove illegal character \\x07"
    assert "\x08" not in sanitized, "Should remove illegal character \\x08"
    assert "with illegal chars" in sanitized, "Should keep normal text at end"
    
    # Test flattening with sanitization
    test_data = {
        "normal_field": "normal value",
        "illegal_field": "text\x07\x08with\x0Billegal\x0Cchars",
        "list_field": ["item1", "item2\x1F"],
        "dict_field": {"key": "value\x0E"}
    }
    
    flattened = formatter._flatten_value_for_excel(test_data)
    assert isinstance(flattened, dict), "Should return a dictionary"
    
    print("✓ Excel formatter sanitization tests passed")


def test_integration():
    """Test integration of all components."""
    print("Testing integration...")
    
    from gcp_inventory_tool.core.application import create_application
    
    # Create a temporary config file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
        config_content = """
environment: "testing"
projects:
  - "test-project"
security:
  enable_audit_logging: false
  mask_sensitive_data: true
performance:
  max_workers: 2
  enable_caching: false
monitoring:
  log_level: "INFO"
  enable_health_checks: true
"""
        f.write(config_content)
        config_path = f.name
    
    try:
        # Create application
        app = create_application(config_path)
        
        # Test application status
        status = app.get_application_status()
        assert "application" in status, "Status should contain application info"
        assert "health" in status, "Status should contain health info"
        assert "performance" in status, "Status should contain performance info"
        
        # Test startup and shutdown
        app.startup()
        assert app.is_running, "Application should be running after startup"
        
        app.shutdown()
        assert not app.is_running, "Application should not be running after shutdown"
        
    finally:
        # Clean up
        os.unlink(config_path)
    
    print("✓ Integration tests passed")


def main():
    """Run all tests."""
    print("🧪 Running production-ready GCP Inventory Tool tests...\n")
    
    try:
        test_duration_handling()
        test_dynamic_regions()
        test_security_manager()
        test_performance_monitor()
        test_resilience_manager()
        test_health_checker()
        test_configuration_system()
        test_excel_formatter_sanitization()
        test_integration()
        
        print("\n🎉 All tests passed! The production-ready features are working correctly.")
        print("\n📋 Summary of fixes and enhancements:")
        print("  ✓ Fixed duration handling for both protobuf and datetime objects")
        print("  ✓ Fixed alert policy boolean attribute access")
        print("  ✓ Implemented dynamic region management with caching")
        print("  ✓ Fixed artifact registry and container registry region issues")
        print("  ✓ Added Excel character sanitization")
        print("  ✓ Implemented comprehensive security and compliance features")
        print("  ✓ Added performance monitoring and metrics")
        print("  ✓ Implemented resilience with circuit breakers and retry logic")
        print("  ✓ Added health monitoring and alerting")
        print("  ✓ Created production-ready configuration management")
        print("  ✓ Integrated all components with proper lifecycle management")
        
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    sys.exit(main())
