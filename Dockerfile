# Use Python 3.11 slim image as base
FROM python:3.11-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# Install system dependencies including gcloud CLI
RUN apt-get update && apt-get install -y \
    curl \
    git \
    gnupg \
    lsb-release \
    && echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list \
    && curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key --keyring /usr/share/keyrings/cloud.google.gpg add - \
    && apt-get update && apt-get install -y google-cloud-cli \
    && rm -rf /var/lib/apt/lists/*

# Create app directory
WORKDIR /app

# Create directories for output and logs
RUN mkdir -p /app/output /app/logs

# Copy requirements first for better Docker layer caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy the entire project
COPY . .

# Create a non-root user for security but keep it simple for interactive use
RUN useradd --create-home --shell /bin/bash --uid 1000 appuser && \
    chown -R appuser:appuser /app && \
    chown -R appuser:appuser /home/<USER>

# Switch to non-root user
USER appuser

# Set up gcloud config directory
RUN mkdir -p /home/<USER>/.config/gcloud

# Default to bash shell for interactive use
CMD ["/bin/bash"]

# Labels for metadata
LABEL maintainer="GCP Inventory Tool"
LABEL description="Interactive containerized GCP resource inventory tool with gcloud CLI"
LABEL version="1.0"
