# Docker-optimized configuration for GCP Inventory Tool
# This configuration is specifically tuned for containerized execution

# GCP Projects to scan
projects:
  # Add your project IDs here
  # - "your-project-id-1"
  # - "your-project-id-2"
  # - "your-project-id-3"

# Services to fetch (all enabled by default)
# Comment out services you don't want to scan
services:
  - compute_instance
  - compute_disk
  - gke_cluster
  - cloud_sql_instance
  - cloud_storage_bucket
  - pubsub_topic
  - pubsub_subscription
  - cloud_function
  - app_engine_service
  - cloud_run_service
  - bigtable
  - dataproc_cluster
  - composer_environment
  - data_fusion
  - memcached
  - redis
  - artifact_registry_repo
  - container_registry_image
  - vpc_network
  - vpc_subnet
  - vpc_firewall
  - vpc_connector
  - cloud_router
  - cloud_nat
  - load_balancer
  - ssl_certificate
  - dns_zone
  - dns_policy
  - project_iam
  - kms_key
  - secret_manager_secret
  - cloud_scheduler_job
  - log_sink
  - alert_policy

# Output configuration
output:
  # Output file path (relative to container's /app/output directory)
  file: "/app/output/gcp_inventory.json"
  
  # Output format: json, csv, or both
  format: "json"
  
  # Include empty services in output
  include_empty: false

# Execution control - optimized for Docker container
execution:
  # Reduced workers for stable DNS resolution in containers
  max_workers: 3
  
  # Timeout settings (in seconds)
  timeout: 300  # 5 minutes per service
  
  # Retry settings
  max_retries: 3
  retry_delay: 2

# Logging configuration
logging:
  # Log level: DEBUG, INFO, WARNING, ERROR
  level: "INFO"
  
  # Log file path (relative to container's /app/logs directory)
  file: "/app/logs/inventory.log"
  
  # Log format
  format: "%(asctime)s - %(levelname)s - %(name)s:%(filename)s - %(message)s"
  
  # Enable console output
  console: true

# Optional: Regions to focus on (if not specified, all regions are scanned)
# regions:
#   - "us-central1"
#   - "us-east1"
#   - "europe-west1"

# Optional: Service-specific configurations
service_configs:
  # GKE specific settings
  gke_cluster:
    # Include node pool details
    include_node_pools: true
    
  # Cloud SQL specific settings
  cloud_sql_instance:
    # Include database details
    include_databases: true
    
  # Compute specific settings
  compute_instance:
    # Include disk attachments
    include_disks: true
