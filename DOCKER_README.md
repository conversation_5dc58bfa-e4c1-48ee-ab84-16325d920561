# 🐳 Docker Setup for GCP Inventory Tool

This Docker setup provides a clean, isolated environment for testing the GCP Inventory Tool without interference from your local gcloud installation.

## 🚀 Quick Start

### 1. Build and Start the Container

```bash
# Make the setup script executable
chmod +x docker-setup.sh

# Run the setup script
./docker-setup.sh

# Start the container
docker-compose up -d
```

### 2. Enter the Container

```bash
docker exec -it gcp-inventory-tool bash
```

### 3. Authenticate with Google Cloud

Inside the container:

```bash
# Login to Google Cloud (this will open a browser)
gcloud auth login

# Set your default project
gcloud config set project YOUR_PROJECT_ID

# Verify authentication
gcloud auth list
gcloud config list
```

### 4. Test DNS Resolution (Optional)

```bash
# Test if DNS resolution works properly
python test_dns_resolution.py
```

### 5. Run the Inventory Tool

```bash
# Simple run with one project
python -m gcp_inventory_tool --projects YOUR_PROJECT_ID

# Multiple projects
python -m gcp_inventory_tool --projects project1,project2,project3

# Using a configuration file
python -m gcp_inventory_tool --config examples/config.yaml

# With specific services only
python -m gcp_inventory_tool --projects YOUR_PROJECT_ID --services compute_instance,gke_cluster,cloud_storage_bucket
```

## 📁 Directory Structure

```
.
├── Dockerfile                 # Container definition
├── docker-compose.yml        # Docker Compose configuration
├── docker-setup.sh          # Setup script
├── output/                   # Results will be saved here (host machine)
├── logs/                     # Logs will be saved here (host machine)
└── examples/config.yaml      # Sample configuration file
```

## 🔧 Container Features

- **Python 3.11** with all required dependencies
- **Google Cloud CLI** pre-installed
- **Persistent authentication** - your gcloud login persists between container restarts
- **Volume mounts** for output and logs
- **DNS resolution fixes** applied
- **Connection pooling** optimized for containers

## 📊 Testing the DNS Fixes

The container includes several test scripts:

```bash
# Test DNS resolution under parallel load
python test_dns_resolution.py

# Test the fixes implementation
python test_fixes.py
```

## 🐛 Troubleshooting

### Authentication Issues

```bash
# Check current authentication
gcloud auth list

# Re-authenticate if needed
gcloud auth login

# Check project configuration
gcloud config list
```

### DNS Resolution Issues

```bash
# Test DNS resolution
python test_dns_resolution.py

# Check container DNS settings
cat /etc/resolv.conf

# Test specific endpoints
nslookup compute.googleapis.com
```

### Permission Issues

```bash
# Check if you have the required permissions
gcloud projects get-iam-policy YOUR_PROJECT_ID

# Test API access
gcloud compute instances list --project=YOUR_PROJECT_ID
```

## 📝 Configuration

The tool uses the configuration from `examples/config.yaml` which is optimized for Docker:

- `max_workers: 3` - Reduced for stable DNS resolution
- Enhanced timeout and retry settings
- Connection pooling enabled

## 🔄 Container Management

```bash
# Start the container
docker-compose up -d

# Stop the container
docker-compose down

# View logs
docker-compose logs

# Rebuild the container (after code changes)
docker-compose build --no-cache

# Remove everything (including volumes)
docker-compose down -v
```

## 📤 Getting Results

Results are automatically saved to your host machine:

- **Output files**: `./output/` directory
- **Log files**: `./logs/` directory

## 🎯 Benefits of This Setup

1. **Isolation**: No interference from local gcloud installation
2. **Clean Environment**: Fresh Python environment with exact dependencies
3. **Reproducible**: Same environment every time
4. **Easy Testing**: Quick to rebuild and test changes
5. **Persistent Auth**: gcloud login persists between sessions

## 🔍 Monitoring

Inside the container, you can monitor the tool's progress:

```bash
# Watch logs in real-time
tail -f /app/logs/inventory.log

# Check output files
ls -la /app/output/

# Monitor system resources
top
```

This Docker setup provides the perfect environment for testing the DNS resolution fixes without any local environment interference! 🎉
