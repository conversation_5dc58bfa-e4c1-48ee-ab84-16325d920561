# Core dependencies
google-auth >= 2.17.0 # For handling GCP authentication (ADC)
PyYAML >= 6.0 # For parsing YAML configuration files
google-api-python-client >= 2.80.0 # For IAM Service Accounts, SQL Admin API etc.

# GCP Service Client Libraries (Using recent stable versions)
google-cloud-compute >= 1.14.0 # VMs, Disks, Networks, Subnets, Routes, Firewalls, VPNs, LB Components, Images, Templates, Health Checks, SSL Certs, Regions
google-cloud-storage >= 2.7.0 # Cloud Storage Buckets
google-cloud-pubsub >= 2.13.0 # Pub/Sub Topics & Subscriptions
google-cloud-container >= 2.19.0 # GKE Clusters
google-cloud-functions >= 1.10.0 # Cloud Functions (V1 & V2)
google-cloud-appengine-admin >= 1.0.0 # App Engine Services & Firewall
google-cloud-dns >= 0.34.0 # DNS Managed Zones & Policies
google-cloud-kms >= 2.16.0 # KMS Keys
google-cloud-secret-manager >= 2.16.0 # Secret Manager Secrets
google-cloud-data-fusion >= 1.5.0 # Data Fusion Instances
google-cloud-redis >= 2.12.0 # Memorystore for Redis
google-cloud-memcache >= 1.6.0 # Memorystore for Memcached
google-cloud-scheduler >= 2.8.0 # Cloud Scheduler Jobs
google-cloud-run >= 0.10.0 # Cloud Run Services
google-cloud-bigtable >= 2.17.0 # Bigtable client
google-cloud-spanner >= 2.17.0 # Cloud Spanner Instances
google-cloud-orchestration-airflow >= 1.11.1 # Cloud Composer Environments
google-cloud-dataproc >= 5.5.0 # Dataproc Clusters
google-cloud-artifact-registry >= 1.10.0 # Artifact Registry Repositories (incl. GCR via AR)
google-cloud-dataflow-client >= 0.8.8 # Dataflow Jobs (Updated to avoid apache-beam build issue)
google-cloud-resource-manager >= 1.10.0 # Project IAM Policies
google-cloud-vpc-access >= 1.4.0 # Serverless VPC Access Connectors
google-cloud-monitoring >= 2.14.0 # Monitoring Alert Policies & Notification Channels
google-cloud-logging >= 3.5.0 # Logging Sinks
google-cloud-build >= 3.15.0 # Cloud Build Triggers

# Dependencies for specific formatters
pandas >= 1.5.0 # Required for Excel output
openpyxl >= 3.0.0 # Required by pandas for writing .xlsx files
tabulate >= 0.9.0 # If creating a console table formatter
click >= 8.0.0 # Alternative CLI argument parser