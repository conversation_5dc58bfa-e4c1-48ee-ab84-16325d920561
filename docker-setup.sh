#!/bin/bash

# Docker Setup Script for GCP Inventory Tool
# Interactive setup with gcloud auth login

set -e

echo "🐳 Setting up Docker environment for GCP Inventory Tool (Interactive Mode)"
echo "========================================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_warning "Docker Compose not found. Trying docker compose..."
    if ! docker compose version &> /dev/null; then
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    DOCKER_COMPOSE="docker compose"
else
    DOCKER_COMPOSE="docker-compose"
fi

print_status "Docker and Docker Compose are available"

# Create necessary directories
print_info "Creating necessary directories..."
mkdir -p output logs

print_status "Directories created"

print_info "This setup uses gcloud auth login for authentication (no service account key needed)"

# Build the Docker image
print_info "Building Docker image..."
if docker build -t gcp-inventory-tool .; then
    print_status "Docker image built successfully"
else
    print_error "Failed to build Docker image"
    exit 1
fi

# Test DNS resolution in container
print_info "Testing DNS resolution in container..."
if docker run --rm gcp-inventory-tool python /app/test_dns_resolution.py; then
    print_status "DNS resolution test passed"
else
    print_warning "DNS resolution test had issues, but continuing..."
fi

echo ""
print_status "Setup complete! 🎉"
echo ""
echo "Next steps:"
echo ""
echo "1. Start the container:"
echo "   $DOCKER_COMPOSE up -d"
echo ""
echo "2. Enter the container:"
echo "   docker exec -it gcp-inventory-tool bash"
echo ""
echo "3. Inside the container, authenticate with Google Cloud:"
echo "   gcloud auth login"
echo "   gcloud config set project YOUR_PROJECT_ID"
echo ""
echo "4. Test DNS resolution (optional):"
echo "   python test_dns_resolution.py"
echo ""
echo "5. Run the inventory tool:"
echo "   python -m gcp_inventory_tool --projects YOUR_PROJECT_ID"
echo ""
echo "   # Or with multiple projects:"
echo "   python -m gcp_inventory_tool --projects project1,project2,project3"
echo ""
echo "   # Or with a config file (create one first):"
echo "   python -m gcp_inventory_tool --config examples/config.yaml"
echo ""
echo "6. Check results in the host machine:"
echo "   - Output: ./output/ directory"
echo "   - Logs: ./logs/ directory"
echo ""
print_info "The gcloud authentication will persist between container restarts!"
echo ""
print_info "For testing DNS fixes specifically, run:"
echo "   $DOCKER_COMPOSE --profile testing up dns-test"
