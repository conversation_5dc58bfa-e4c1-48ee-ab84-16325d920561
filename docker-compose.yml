version: '3.8'

services:
  gcp-inventory:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: gcp-inventory-tool
    volumes:
      # Mount output directory to persist results
      - ./output:/app/output
      # Mount logs directory
      - ./logs:/app/logs
      # Mount gcloud config to persist authentication
      - gcloud-config:/home/<USER>/.config/gcloud
    environment:
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=INFO

    # Interactive mode for manual login and execution
    stdin_open: true
    tty: true

    # Resource limits (optional)
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Service for testing DNS resolution
  dns-test:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: gcp-dns-test
    command: ["python", "/app/test_dns_resolution.py"]
    profiles:
      - testing

# Named volume for gcloud configuration persistence
volumes:
  gcloud-config:
