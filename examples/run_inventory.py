import sys
import os
import logging

# --- Setup Python Path ---
script_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(script_dir)
sys.path.insert(0, project_root)

# --- Import necessary components ---
try:
    from gcp_inventory_tool.core.inventory import InventoryGenerator
    # Formatters are no longer needed directly by this script
    # from gcp_inventory_tool.formatters import get_formatter
    from gcp_inventory_tool.utils.logging_config import setup_logging
    from gcp_inventory_tool.core import config as cfg_manager # Import config loader
    import google.auth.exceptions
except ImportError as e:
    print(f"Error importing package components: {e}")
    print("Ensure the package is installed (e.g., 'pip install .') or the project root is in PYTHONPATH.")
    sys.exit(1)

# --- Configuration ---
CONFIG_FILE_PATH = os.path.join(script_dir, "config.yaml")

# --- Basic Logging Setup ---
# Setup basic logging; InventoryGenerator will use settings passed to its init
setup_logging(level=logging.INFO)
logger = logging.getLogger(__name__)

def run(config_path: str):
    """Runs the inventory generation and formatting using config file."""
    logger.info(f"Starting inventory generation using config: {config_path}")

    try:
        # Load config explicitly for the script
        config_data = cfg_manager.load_config(config_path)
        if not config_data: # Handle case where config file not found or empty
             print(f"Error: Configuration file '{config_path}' not found or is empty.")
             sys.exit(1)

        # Extract settings from config for InventoryGenerator initialization
        projects = config_data.get('projects', [])
        services = config_data.get('services', [])
        max_workers_cfg = config_data.get('execution', {}).get('max_workers')
        cpu_cores = os.cpu_count()
        default_workers = cpu_cores + 4 if cpu_cores else 10
        max_workers = max_workers_cfg if max_workers_cfg is not None else default_workers

        output_config = config_data.get('output', {})
        output_format = output_config.get('format', 'console') # Default to console for script
        format_options = output_config.get('format_options', {})
        output_file = output_config.get('file')

        # Extract logging settings to pass to setup_logging (as InventoryGenerator no longer handles it)
        log_config = config_data.get('logging', {})
        log_level_str = log_config.get('level', 'INFO').upper()
        log_to_file = log_config.get('log_to_file', False)
        log_file_path = log_config.get('log_file', 'logs/app.log')

        # Setup logging based on config *before* initializing InventoryGenerator
        setup_logging(
            level=getattr(logging, log_level_str, logging.INFO),
            log_to_file=log_to_file,
            log_file=log_file_path
        )

        # Validate required inputs for the script's logic
        if not projects:
            logger.error("No projects defined in config file.")
            return
        if not services:
            logger.warning("No services defined in config file.")
            # Allow proceeding without services

        # Resolve output file path relative to script if needed
        if output_file:
             if not os.path.isabs(output_file):
                  output_file = os.path.join(script_dir, output_file)
             # Note: format_options['file'] is passed to the formatter inside InventoryGenerator

        # Initialize Inventory Generator with values derived from config
        inventory_gen = InventoryGenerator(
            projects=projects,
            services=services,
            max_workers=max_workers,
            output_format=output_format,
            output_file=output_file, # Pass resolved path
            format_options=format_options
        )

        # Run the inventory process (which now includes formatting)
        inventory_gen.run_inventory()

        # No need to call formatting here, it's done inside run_inventory

        logger.info("Example script finished successfully.")

    except FileNotFoundError as e:
        logger.error(f"Configuration Error: {e}")
    except (ValueError, TypeError) as e:
         logger.error(f"Configuration or Input Error: {e}")
    except google.auth.exceptions.DefaultCredentialsError as e:
         logger.error(f"Authentication Error: {e}")
         logger.error("Ensure Application Default Credentials (ADC) are configured correctly.")
    except RuntimeError as e:
         logger.error(f"Initialization Error: {e}")
    except ImportError as e: # Catch missing formatter dependencies during init
         logger.critical(f"Import Error: Required library might be missing for the configured format. Details: {e}")
    except Exception as e:
        logger.critical(f"An unexpected error occurred in the example script: {e}", exc_info=True)

# --- Script Entry Point ---
if __name__ == "__main__":
    if not os.path.exists(CONFIG_FILE_PATH):
         print(f"Error: Default configuration file not found at '{CONFIG_FILE_PATH}'")
         sys.exit(1)
    run(CONFIG_FILE_PATH)

