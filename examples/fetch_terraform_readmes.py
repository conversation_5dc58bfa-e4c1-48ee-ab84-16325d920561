#!/usr/bin/env python3
import os
import requests
import base64
import time
from git import Repo
import shutil
import re
from tqdm import tqdm

# GitHub API configuration
GITHUB_API_URL = "https://api.github.com"
ORG_NAME = "terraform-google-modules"
# Replace with your GitHub token if you have one to avoid rate limits
# GITHUB_TOKEN = "your_github_token"
GITHUB_TOKEN = None

HEADERS = {
    "Accept": "application/vnd.github.v3+json"
}
if GITHUB_TOKEN:
    HEADERS["Authorization"] = f"token {GITHUB_TOKEN}"

# Local directories
TEMP_DIR = "terraform_modules_temp"
OUTPUT_FILE = "terraform-google-modules-combined-readme.md"

def get_all_repositories():
    """Fetch all repositories from the terraform-google-modules organization."""
    print(f"Fetching repositories from {ORG_NAME}...")
    repositories = []
    page = 1
    per_page = 100
    
    while True:
        response = requests.get(
            f"{GITHUB_API_URL}/orgs/{ORG_NAME}/repos",
            headers=HEADERS,
            params={"page": page, "per_page": per_page}
        )
        
        if response.status_code != 200:
            print(f"Error fetching repositories: {response.status_code}")
            print(response.text)
            return repositories
        
        repos_page = response.json()
        if not repos_page:
            break
            
        repositories.extend(repos_page)
        page += 1
        
        # Respect GitHub API rate limits
        if not GITHUB_TOKEN and len(repositories) % per_page == 0:
            print("Pausing to avoid rate limits...")
            time.sleep(10)
    
    print(f"Found {len(repositories)} repositories.")
    return repositories

def clone_repository(repo_url, repo_name):
    """Clone a repository to a local directory."""
    local_path = os.path.join(TEMP_DIR, repo_name)
    
    # Skip if already cloned
    if os.path.exists(local_path):
        return local_path
    
    try:
        Repo.clone_from(repo_url, local_path)
        return local_path
    except Exception as e:
        print(f"Error cloning {repo_name}: {e}")
        return None

def find_readme(repo_path):
    """Find README.md file in a repository (case insensitive)."""
    if not repo_path or not os.path.exists(repo_path):
        return None
    
    for file in os.listdir(repo_path):
        if file.lower() == "readme.md":
            return os.path.join(repo_path, file)
    
    return None

def process_readme_content(content, repo_name):
    """Process README content to adjust links and add repository info."""
    # Add repository header
    header = f"# Repository: {repo_name}\n\n"
    
    # Fix relative links to point to GitHub
    github_base_url = f"https://github.com/{ORG_NAME}/{repo_name}"
    
    # Replace relative image links
    content = re.sub(
        r'!\[(.*?)\]\((?!http)(.*?)\)',
        f'![\g<1>]({github_base_url}/blob/main/\g<2>?raw=true)',
        content
    )
    
    # Replace relative markdown links
    content = re.sub(
        r'\[(.*?)\]\((?!http)(.*?)\)',
        f'[\g<1>]({github_base_url}/blob/main/\g<2>)',
        content
    )
    
    # Add a horizontal rule at the end
    footer = "\n\n---\n\n"
    
    return header + content + footer

def main():
    # Create temp directory if it doesn't exist
    if not os.path.exists(TEMP_DIR):
        os.makedirs(TEMP_DIR)
    
    # Get all repositories
    repositories = get_all_repositories()
    
    # Create output file
    with open(OUTPUT_FILE, "w", encoding="utf-8") as outfile:
        outfile.write(f"# Terraform Google Modules Documentation\n\n")
        outfile.write(f"This document contains the combined README files from all repositories in the [terraform-google-modules](https://github.com/terraform-google-modules) GitHub organization.\n\n")
        outfile.write(f"Generated on: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        outfile.write(f"## Table of Contents\n\n")
        
        # Create table of contents
        for repo in repositories:
            repo_name = repo["name"]
            outfile.write(f"- [{repo_name}](#{repo_name.lower().replace('-', '-')})\n")
        
        outfile.write("\n---\n\n")
        
        # Process each repository
        for repo in tqdm(repositories, desc="Processing repositories"):
            repo_name = repo["name"]
            repo_url = repo["clone_url"]
            
            print(f"\nProcessing {repo_name}...")
            
            # Clone repository
            repo_path = clone_repository(repo_url, repo_name)
            if not repo_path:
                continue
            
            # Find README file
            readme_path = find_readme(repo_path)
            if not readme_path:
                print(f"No README found in {repo_name}")
                continue
            
            # Read README content
            try:
                with open(readme_path, "r", encoding="utf-8") as file:
                    content = file.read()
                
                # Process content
                processed_content = process_readme_content(content, repo_name)
                
                # Add an anchor for table of contents
                anchor = f'<a id="{repo_name.lower().replace("-", "-")}"></a>\n\n'
                
                # Write to output file
                outfile.write(anchor + processed_content)
                
            except Exception as e:
                print(f"Error processing README for {repo_name}: {e}")
    
    print(f"\nCleaning up temporary files...")
    # Clean up temporary directory
    shutil.rmtree(TEMP_DIR)
    
    print(f"\nDone! Combined README file created at: {OUTPUT_FILE}")
    print(f"Total repositories processed: {len(repositories)}")

if __name__ == "__main__":
    main()