# GCP Inventory Tool - Production Ready

A comprehensive, production-ready Python tool for inventorying Google Cloud Platform (GCP) resources across multiple projects and services. This enterprise-grade tool provides detailed information about your GCP infrastructure with advanced security, monitoring, and compliance features.

## 🚀 Features

### Core Capabilities
- **Multi-Project Support**: Scan multiple GCP projects in a single run
- **Comprehensive Service Coverage**: Supports 40+ GCP services including:
  - Compute Engine (VMs, disks, networks, load balancers)
  - Cloud Storage buckets
  - GKE clusters and node pools
  - Cloud Functions (V1 & V2)
  - Pub/Sub topics and subscriptions
  - Cloud SQL instances and databases
  - Artifact Registry and Container Registry
  - And many more...
- **Multiple Output Formats**: JSON, CSV, Excel, and formatted console tables
- **Dynamic Region Detection**: Automatically discovers available regions per service
- **Parallel Processing**: Efficient concurrent fetching with configurable workers

### Production Features
- **🔒 Security & Compliance**
  - Data sanitization and PII detection
  - Audit logging and trail
  - Sensitive data masking
  - Access validation and credential management
  
- **📊 Performance & Monitoring**
  - Real-time performance metrics
  - System resource monitoring
  - Health checks and status reporting
  - Prometheus metrics integration
  
- **🛡️ Resilience & Reliability**
  - Circuit breakers for service protection
  - Exponential backoff and retry logic
  - Graceful degradation
  - Error recovery mechanisms
  
- **🔧 Observability**
  - Structured logging (JSON format)
  - Distributed tracing support
  - Health monitoring and alerting
  - Performance profiling

## 🏗️ Architecture

The tool is built with a modular, production-ready architecture:

```
gcp_inventory_tool/
├── core/                   # Core application components
│   ├── application.py      # Main application orchestrator
│   ├── settings.py         # Configuration management
│   ├── security.py         # Security and compliance
│   ├── metrics.py          # Performance monitoring
│   ├── resilience.py       # Error handling and circuit breakers
│   └── health.py           # Health checks and monitoring
├── fetchers/               # Service-specific resource fetchers
├── formatters/             # Output format handlers
└── utils/                  # Utility modules
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8 or higher
- Google Cloud SDK (gcloud) installed and configured
- Appropriate GCP permissions for the resources you want to inventory

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd gcp-inventory-tool

# Install dependencies (including production dependencies)
pip install -r requirements.txt

# Install the package
pip install -e .
```

## ⚙️ Configuration

The tool supports multiple configuration methods with environment-specific overrides.

### 1. Configuration File

Use the production configuration template:

```bash
cp config.production.yaml config.yaml
# Edit config.yaml with your settings
```

### 2. Environment Variables

Set configuration via environment variables:

```bash
export GCP_INVENTORY_PROJECTS="project1,project2,project3"
export GCP_INVENTORY_ENVIRONMENT="production"
export GCP_INVENTORY_MAX_WORKERS="5"
export GCP_INVENTORY_OUTPUT_FORMAT="json"
```

### 3. Configuration Hierarchy

Configuration is loaded in this order (later sources override earlier ones):
1. Configuration file
2. Environment variables
3. Command line arguments

## 🔧 Usage

### Basic Usage

```bash
# Run with configuration file
gcp-inventory --config config.yaml

# Run with CLI arguments
gcp-inventory --projects project1,project2 --services compute,storage --format json

# Run with specific output file
gcp-inventory --projects myproject --output-file inventory.xlsx --format excel
```

### Production Usage

```bash
# Run with production configuration
gcp-inventory --config config.production.yaml --environment production

# Run with health monitoring
gcp-inventory --config config.yaml --enable-health-monitoring

# Run with performance profiling
gcp-inventory --config config.yaml --enable-metrics --metrics-port 8080
```

### Docker Usage

```bash
# Build the container
docker build -t gcp-inventory .

# Run with mounted configuration
docker run -v $(pwd)/config.yaml:/app/config.yaml \
           -v ~/.config/gcloud:/root/.config/gcloud:ro \
           gcp-inventory

# Run with environment variables
docker run -e GCP_INVENTORY_PROJECTS="project1,project2" \
           -e GCP_INVENTORY_OUTPUT_FORMAT="json" \
           gcp-inventory
```

## 🔒 Security Best Practices

### Authentication
- Use service accounts with minimal required permissions
- Enable credential rotation where possible
- Validate project access before scanning

### Data Protection
- Enable sensitive data masking in production
- Configure audit logging for compliance
- Use encrypted storage for output files

### Network Security
- Run in private networks when possible
- Use VPC endpoints for GCP API access
- Implement rate limiting to prevent API abuse

## 📊 Monitoring and Observability

### Health Checks

The tool provides comprehensive health monitoring:

```bash
# Check application health
curl http://localhost:8080/health

# Get detailed health status
curl http://localhost:8080/health/detailed
```

### Metrics

Prometheus metrics are available at `/metrics`:

```bash
# View metrics
curl http://localhost:8080/metrics
```

Key metrics include:
- `gcp_inventory_requests_total` - Total API requests
- `gcp_inventory_request_duration_seconds` - Request duration
- `gcp_inventory_resources_discovered_total` - Resources found
- `gcp_inventory_errors_total` - Error count by type

### Logging

Structured logging provides detailed operational insights:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "level": "INFO",
  "logger": "gcp_inventory.fetchers.compute",
  "message": "Fetched 150 compute instances",
  "project_id": "my-project",
  "service": "compute",
  "duration_ms": 2500,
  "resource_count": 150
}
```

## 🛠️ Development

### Setting Up Development Environment

```bash
# Install development dependencies
pip install -r requirements.txt

# Install pre-commit hooks
pre-commit install

# Run tests
pytest tests/ -v --cov=gcp_inventory_tool

# Run linting
flake8 gcp_inventory_tool/
black gcp_inventory_tool/
mypy gcp_inventory_tool/
```

### Adding New Services

1. Create a new fetcher in `gcp_inventory_tool/fetchers/`
2. Implement the `ServiceFetcher` interface
3. Add service configuration to settings
4. Update tests and documentation

## 🐳 Container Deployment

### Production Dockerfile

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY gcp_inventory_tool/ ./gcp_inventory_tool/
COPY config.production.yaml ./config.yaml

EXPOSE 8080
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

CMD ["python", "-m", "gcp_inventory_tool.cli", "--config", "config.yaml"]
```

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: gcp-inventory
spec:
  replicas: 1
  selector:
    matchLabels:
      app: gcp-inventory
  template:
    metadata:
      labels:
        app: gcp-inventory
    spec:
      containers:
      - name: gcp-inventory
        image: gcp-inventory:latest
        ports:
        - containerPort: 8080
        env:
        - name: GCP_INVENTORY_ENVIRONMENT
          value: "production"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

## 📋 Compliance and Governance

### Audit Logging

All operations are logged for compliance:

```json
{
  "timestamp": "2024-01-15T10:30:00Z",
  "event_type": "inventory_execution",
  "user_id": "<EMAIL>",
  "project_id": "my-project",
  "service": "compute",
  "action": "list_instances",
  "resource_count": 150,
  "success": true,
  "duration_ms": 2500
}
```

### Data Classification

Automatic data classification and PII detection:
- **PUBLIC**: General resource metadata
- **SENSITIVE**: Configuration data, keys
- **PII**: Email addresses, IP addresses

### Retention Policies

Configure data retention based on compliance requirements:

```yaml
compliance:
  data_retention_days: 90
  enable_audit_trail: true
  redact_sensitive_fields: true
```

## 🚨 Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure service account has required IAM roles
2. **API Not Enabled**: Enable required GCP APIs in target projects
3. **Rate Limiting**: Reduce concurrent workers or enable rate limiting
4. **Memory Issues**: Increase memory limits or reduce batch sizes

### Debug Mode

Enable debug logging for troubleshooting:

```bash
gcp-inventory --config config.yaml --debug --log-level DEBUG
```

## 📞 Support

- **Documentation**: See `/docs` directory for detailed documentation
- **Issues**: Report issues on GitHub
- **Security**: Report security issues <NAME_EMAIL>

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
