from setuptools import setup, find_packages
import os

# Function to read the requirements.txt file
def read_requirements():
    """Reads requirements from requirements.txt"""
    req_path = os.path.join(os.path.dirname(__file__), 'requirements.txt')
    if os.path.exists(req_path):
        with open(req_path, 'r') as f:
            return [line.strip() for line in f if line.strip() and not line.startswith('#')]
    return []

# Function to read the README.md file
def read_readme():
    """Reads README.md for long description"""
    readme_path = os.path.join(os.path.dirname(__file__), 'README.md')
    if os.path.exists(readme_path):
        with open(readme_path, 'r', encoding='utf-8') as f:
            return f.read()
    return "GCP Inventory Tool - See repository for details."

setup(
    name="gcp_inventory_tool",
    version="0.1.0", # Initial version
    packages=find_packages(exclude=["tests*", "examples*"]), # Find packages automatically
    install_requires=read_requirements(), # Read dependencies from requirements.txt
    entry_points={
        'console_scripts': [
            # Define the command-line script entry point using click
            'gcp-inventory=gcp_inventory_tool.cli:cli', # Changed to cli function
        ],
        # Optional: Define entry points for discovering fetchers dynamically
        # 'gcp_inventory.fetchers': [
        #     'compute = gcp_inventory_tool.fetchers.compute_engine:ComputeEngineFetcher',
        #     'storage = gcp_inventory_tool.fetchers.cloud_storage:CloudStorageFetcher',
        # ],
    },
    author="Your Name / Team Name",
    author_email="<EMAIL>",
    description="A Python tool to generate GCP resource inventory.",
    long_description=read_readme(), # Use README for long description
    long_description_content_type='text/markdown', # Specify content type
    url="https://github.com/yourusername/gcp-inventory-tool", # Link to your repository
    classifiers=[ # Standard classifiers
        "Development Status :: 3 - Alpha", # Initial development stage
        "Intended Audience :: Developers",
        "Intended Audience :: System Administrators",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "License :: OSI Approved :: Apache Software License", # Choose your license (e.g., MIT, Apache 2.0)
        "Operating System :: OS Independent",
        "Topic :: System :: Systems Administration",
        "Topic :: Utilities",
    ],
    python_requires='>=3.8', # Minimum Python version requirement
    include_package_data=True, # Include non-code files specified in MANIFEST.in (if any)
)