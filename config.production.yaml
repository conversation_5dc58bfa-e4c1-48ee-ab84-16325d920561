# Production Configuration for GCP Inventory Tool
# This configuration is optimized for production environments with security,
# performance, and compliance best practices.

# Environment configuration
environment: "production"
debug: false

# GCP Projects to scan
# Add your project IDs here
projects:
  # - "your-production-project-1"
  # - "your-production-project-2"
  # - "your-staging-project"

# Regions configuration
# Leave empty to use dynamic region detection
# Or specify regions to limit scope
regions: []
  # - "us-central1"
  # - "us-east1"
  # - "europe-west1"

# Security Configuration
security:
  # Enable comprehensive audit logging
  enable_audit_logging: true
  
  # Request limits and timeouts
  max_concurrent_requests: 8
  request_timeout: 300  # 5 minutes
  retry_max_attempts: 3
  retry_backoff_factor: 2.0
  
  # Rate limiting
  enable_rate_limiting: true
  rate_limit_requests_per_minute: 100
  
  # Credential security
  credential_cache_ttl: 3600  # 1 hour
  enable_credential_rotation: false
  
  # Data security
  mask_sensitive_data: true
  sensitive_fields:
    - "password"
    - "secret"
    - "key"
    - "token"
    - "credential"
    - "private"
    - "auth"

# Performance Configuration
performance:
  # Worker configuration
  max_workers: 5
  
  # Caching
  enable_caching: true
  cache_ttl: 3600  # 1 hour
  batch_size: 100
  
  # Memory management
  max_memory_usage_mb: 4096  # 4GB
  enable_memory_monitoring: true
  
  # Network optimization
  connection_pool_size: 10
  keep_alive_timeout: 30
  
  # Region optimization
  enable_dynamic_regions: true
  region_cache_ttl: 3600
  max_regions_per_service: 50

# Monitoring and Observability
monitoring:
  # Enable comprehensive monitoring
  enable_metrics: true
  enable_tracing: false
  enable_health_checks: true
  
  # Logging configuration
  log_level: "INFO"
  enable_structured_logging: true
  log_format: "json"
  
  # Metrics endpoint
  metrics_port: 8080
  metrics_path: "/metrics"
  
  # Health checks
  health_check_interval: 60
  health_check_timeout: 10

# Compliance and Governance
compliance:
  # Data classification and privacy
  enable_data_classification: true
  enable_pii_detection: true
  data_retention_days: 90
  
  # Audit trail
  enable_audit_trail: true
  audit_log_path: "/var/log/gcp-inventory/audit.log"
  
  # Privacy controls
  anonymize_user_data: false
  redact_sensitive_fields: true

# Output Configuration
output:
  # Output format: json, csv, excel, console
  format: "json"
  
  # Output file path (optional)
  # file: "/app/output/gcp_inventory.json"
  
  # Include empty services in output
  include_empty: false
  
  # Compression
  compress_output: true
  
  # Excel specific settings
  excel_max_rows_per_sheet: 1000000
  excel_enable_formatting: true
  
  # JSON specific settings
  json_indent: 2
  json_sort_keys: true
  
  # CSV specific settings
  csv_delimiter: ","
  csv_quote_char: '"'

# Service Configuration
services:
  # Enabled services (empty list means all services enabled)
  enabled_services: []
    # - "compute"
    # - "storage"
    # - "gke_cluster"
    # - "cloud_function"
    # - "pubsub"
    # - "cloud_sql"
  
  # Disabled services
  disabled_services: []
    # - "bigtable"  # Disable if not used
    # - "dataflow_job"  # Disable if not used
  
  # Service-specific timeouts (in seconds)
  service_timeouts:
    compute: 600  # 10 minutes for large compute inventories
    storage: 300  # 5 minutes for storage
    gke_cluster: 450  # 7.5 minutes for GKE
    cloud_sql: 300  # 5 minutes for Cloud SQL
    default: 300  # Default timeout
  
  # Service-specific retry configurations
  service_retry_configs:
    compute:
      max_attempts: 5
      base_delay: 2.0
      max_delay: 60.0
    storage:
      max_attempts: 3
      base_delay: 1.0
      max_delay: 30.0
    artifact_registry_repo:
      max_attempts: 4
      base_delay: 1.5
      max_delay: 45.0
  
  # Service-specific region overrides
  service_regions:
    # Override regions for specific services if needed
    # artifact_registry_repo:
    #   - "us-central1"
    #   - "europe-west1"
    # cloud_functions:
    #   - "us-central1"
    #   - "us-east1"

# Execution Control
execution:
  # Timeout settings
  global_timeout: 3600  # 1 hour total execution timeout
  service_timeout: 600   # 10 minutes per service timeout
  
  # Parallel execution
  enable_parallel_execution: true
  max_parallel_projects: 3
  max_parallel_services: 2
  
  # Error handling
  continue_on_error: true
  fail_fast: false
  
  # Progress reporting
  enable_progress_reporting: true
  progress_report_interval: 30  # seconds

# Logging Configuration
logging:
  # Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
  level: "INFO"
  
  # Log format: text, json
  format: "json"
  
  # File logging
  enable_file_logging: true
  log_file: "/var/log/gcp-inventory/inventory.log"
  max_log_file_size_mb: 100
  max_log_files: 10
  
  # Console logging
  enable_console_logging: true
  
  # Structured logging fields
  include_timestamp: true
  include_level: true
  include_logger_name: true
  include_thread_id: false
  include_process_id: true

# Advanced Configuration
advanced:
  # Circuit breaker settings
  circuit_breaker:
    failure_threshold: 5
    recovery_timeout: 60.0
    success_threshold: 3
  
  # Connection pooling
  connection_pool:
    max_connections: 20
    max_idle_connections: 5
    connection_timeout: 30
  
  # Resource limits
  resource_limits:
    max_open_files: 1024
    max_memory_per_worker_mb: 512
    max_cpu_percent: 80
  
  # Feature flags
  feature_flags:
    enable_experimental_features: false
    enable_beta_apis: false
    enable_advanced_metrics: true
    enable_cost_estimation: false

# Environment-specific overrides
# These settings will override the above based on the environment
environment_overrides:
  development:
    debug: true
    monitoring:
      log_level: "DEBUG"
    security:
      enable_audit_logging: false
    performance:
      enable_caching: false
      max_workers: 2
  
  testing:
    monitoring:
      log_level: "INFO"
    security:
      max_concurrent_requests: 3
    performance:
      max_workers: 2
      max_memory_usage_mb: 2048
  
  staging:
    monitoring:
      log_level: "INFO"
    security:
      enable_audit_logging: true
    performance:
      max_workers: 4
      max_memory_usage_mb: 3072

# Notification Configuration (for alerts and reports)
notifications:
  # Email notifications
  email:
    enabled: false
    smtp_server: "smtp.example.com"
    smtp_port: 587
    username: "<EMAIL>"
    # password should be set via environment variable
    recipients:
      - "<EMAIL>"
      - "<EMAIL>"
  
  # Webhook notifications
  webhook:
    enabled: false
    url: "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"
    timeout: 10
  
  # Alert conditions
  alert_conditions:
    health_status_change: true
    high_error_rate: true
    performance_degradation: true
    security_events: true

# Integration Configuration
integrations:
  # Prometheus metrics
  prometheus:
    enabled: false
    port: 9090
    path: "/metrics"
  
  # Grafana dashboards
  grafana:
    enabled: false
    dashboard_url: "http://grafana.example.com"
  
  # External storage
  external_storage:
    enabled: false
    type: "gcs"  # gcs, s3, azure
    bucket: "gcp-inventory-storage"
    prefix: "inventories/"
