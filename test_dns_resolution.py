#!/usr/bin/env python3
"""
Test script to diagnose DNS resolution issues with GCP APIs during parallel execution.
This helps identify if the DNS timeout issues are due to local DNS resolver problems.
"""

import socket
import time
import threading
import concurrent.futures
from typing import List, Tuple

# GCP API endpoints that commonly fail with DNS resolution errors
GCP_ENDPOINTS = [
    'appengine.googleapis.com',
    'vpcaccess.googleapis.com',
    'datafusion.googleapis.com',
    'redis.googleapis.com',
    'memcache.googleapis.com',
    'run.googleapis.com',
    'composer.googleapis.com',
    'container.googleapis.com',
    'pubsub.googleapis.com',
    'cloudresourcemanager.googleapis.com',
    'compute.googleapis.com',
    'storage.googleapis.com'
]

def test_dns_resolution(hostname: str, timeout: float = 5.0) -> Tuple[str, bool, float, str]:
    """
    Test DNS resolution for a single hostname.
    
    Args:
        hostname: The hostname to resolve
        timeout: DNS resolution timeout in seconds
        
    Returns:
        Tuple of (hostname, success, duration, error_message)
    """
    start_time = time.time()
    try:
        # Attempt DNS resolution
        socket.getaddrinfo(hostname, 443, socket.AF_UNSPEC, socket.SOCK_STREAM)
        duration = time.time() - start_time
        return (hostname, True, duration, "")
    except socket.gaierror as e:
        duration = time.time() - start_time
        return (hostname, False, duration, str(e))
    except Exception as e:
        duration = time.time() - start_time
        return (hostname, False, duration, str(e))

def test_sequential_dns():
    """Test DNS resolution sequentially (one at a time)."""
    print("🔍 Testing DNS resolution sequentially...")
    results = []
    
    for hostname in GCP_ENDPOINTS:
        result = test_dns_resolution(hostname)
        results.append(result)
        status = "✅ SUCCESS" if result[1] else "❌ FAILED"
        print(f"  {hostname}: {status} ({result[2]:.2f}s)")
        if not result[1]:
            print(f"    Error: {result[3]}")
    
    return results

def test_parallel_dns(max_workers: int = 10):
    """Test DNS resolution in parallel (simulating the inventory tool)."""
    print(f"\n🚀 Testing DNS resolution with {max_workers} parallel workers...")
    results = []
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all DNS resolution tasks
        future_to_hostname = {
            executor.submit(test_dns_resolution, hostname): hostname 
            for hostname in GCP_ENDPOINTS
        }
        
        # Collect results
        for future in concurrent.futures.as_completed(future_to_hostname):
            result = future.result()
            results.append(result)
            status = "✅ SUCCESS" if result[1] else "❌ FAILED"
            print(f"  {result[0]}: {status} ({result[2]:.2f}s)")
            if not result[1]:
                print(f"    Error: {result[3]}")
    
    return results

def analyze_results(sequential_results: List, parallel_results: List):
    """Analyze and compare the results."""
    print("\n📊 ANALYSIS:")
    
    seq_failures = [r for r in sequential_results if not r[1]]
    par_failures = [r for r in parallel_results if not r[1]]
    
    print(f"Sequential failures: {len(seq_failures)}/{len(sequential_results)}")
    print(f"Parallel failures: {len(par_failures)}/{len(parallel_results)}")
    
    if len(par_failures) > len(seq_failures):
        print("\n⚠️  DIAGNOSIS: DNS resolution degrades with parallel execution!")
        print("   This confirms the issue is DNS resolver overload, not network connectivity.")
        
        print("\n💡 RECOMMENDATIONS:")
        print("   1. Reduce max_workers from 10 to 3-5 in your config.yaml")
        print("   2. Consider using a different DNS resolver (e.g., 8.8.8.8, 1.1.1.1)")
        print("   3. Increase DNS cache TTL on your system")
        print("   4. Run the inventory tool during off-peak hours")
        print("   5. Consider running problematic services sequentially")
        
        if any("Temporary failure in name resolution" in r[3] for r in par_failures):
            print("   6. Your DNS resolver is overwhelmed - consider switching DNS servers")
        
    elif len(seq_failures) > 0:
        print("\n⚠️  DIAGNOSIS: DNS resolution issues even with sequential execution!")
        print("   This suggests network connectivity or DNS server problems.")
        
        print("\n💡 RECOMMENDATIONS:")
        print("   1. Check your internet connection")
        print("   2. Try different DNS servers (8.8.8.8, 1.1.1.1)")
        print("   3. Check if you're behind a corporate firewall")
        print("   4. Verify VPN settings if applicable")
        
    else:
        print("\n✅ DIAGNOSIS: DNS resolution works fine!")
        print("   The issue might be in the gRPC client configuration or connection pooling.")
        
        print("\n💡 RECOMMENDATIONS:")
        print("   1. The fixes I implemented should resolve the issue")
        print("   2. Try running with max_workers: 3 first")
        print("   3. Monitor the logs for improved error handling")

def main():
    """Main test function."""
    print("🧪 GCP DNS Resolution Test")
    print("=" * 50)
    print("This test helps diagnose DNS resolution issues during parallel execution.")
    print("It simulates the conditions that cause 'DNS resolution failed' errors.\n")
    
    # Test sequential DNS resolution
    sequential_results = test_sequential_dns()
    
    # Test parallel DNS resolution
    parallel_results = test_parallel_dns(max_workers=10)
    
    # Analyze results
    analyze_results(sequential_results, parallel_results)
    
    print("\n" + "=" * 50)
    print("Test completed. Use the recommendations above to fix DNS issues.")

if __name__ == "__main__":
    main()
