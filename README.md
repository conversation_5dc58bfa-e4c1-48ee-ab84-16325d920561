# GCP Inventory Tool

A Python package to generate inventory reports for Google Cloud Platform (GCP) resources.

## Features

* **Modular:** Easily extendable to support new GCP services.
* **Configurable:** Define target projects and services via a YAML configuration file.
* **CLI Overrides:** Specify projects and services directly via command line.
* **Authentication:** Uses Application Default Credentials (ADC) for seamless authentication.
* **Concurrent:** Fetches resources concurrently for faster execution (optional).
* **Multiple Output Formats:** Supports JSON, CSV, Excel, and Console Table output.

## Installation

```bash
# Option 1: Install from source (after cloning the repository)
pip install .

# Option 2: Install using pip once published (replace with actual package name if different)
# pip install gcp-inventory-tool
```

## Prerequisites

1.  **Python:** Python 3.8 or higher.
2.  **GCP Authentication:** Configure Application Default Credentials (ADC):
    ```bash
    gcloud auth application-default login
    ```
    Ensure the authenticated principal (user or service account) has the necessary IAM permissions (e.g., `roles/viewer`, `roles/compute.viewer`, `roles/storage.objectViewer`, etc.) on the target projects for the services you intend to scan.
3.  **Enable APIs:** Ensure the necessary GCP service APIs (e.g., Compute Engine API, Cloud Storage API, Cloud Resource Manager API, Cloud SQL Admin API, etc.) are enabled in the target projects for the services you intend to scan.

## Configuration

Create a `config.yaml` file (or specify a different path via the `--config` option). CLI arguments for projects and services will override values in this file.

```yaml
# Example config.yaml
projects:
  - "your-gcp-project-id-1"
  # - "your-gcp-project-id-2"

services:
  - "compute"   # Compute Engine VMs
  - "storage"   # Cloud Storage Buckets
  # Add more service keys corresponding to fetcher SERVICE_NAME attributes
  # e.g., "gke_cluster", "pubsub", "pubsub_topic", "redis", "cloud_run_service", etc.

# Optional settings
output:
  format: "console" # Specify desired format (e.g., json, csv, excel, console)
  file: "gcp_inventory_output.xlsx" # File path used by json, csv, excel formats

logging:
  level: "INFO" # DEBUG, INFO, WARNING, ERROR
  log_to_file: true # Optional: Enable file logging
  log_file: "inventory_run.log" # Optional: Log file path
```

## Usage (CLI)

The tool uses the `click` library for its command-line interface.

```bash
# Basic usage (uses config.yaml for projects/services, outputs to console)
gcp-inventory

# Specify projects and services (overrides config.yaml)
gcp-inventory --projects proj-a,proj-b --services compute,storage,subnet

# Specify output format and file
gcp-inventory --format csv --output-file my_inventory.csv
gcp-inventory --format excel --output-file my_inventory.xlsx
gcp-inventory --format json --output-file my_inventory.json

# Specify config file path
gcp-inventory --config path/to/your/custom_config.yaml

# Change logging level and log to file
gcp-inventory --log-level DEBUG --log-to-file --log-file debug.log

# Get help
gcp-inventory --help
```

## Extending the Tool

To add support for a new GCP service (e.g., BigQuery Datasets):

1.  Install the required client library: `pip install google-cloud-bigquery`.
2.  Create a new fetcher class in `gcp_inventory_tool/fetchers/` (e.g., `bigquery_dataset.py`) inheriting from `ServiceFetcher`.
    ```python
    # gcp_inventory_tool/fetchers/bigquery_dataset.py
    from ..core.base_fetcher import ServiceFetcher
    from google.cloud import bigquery
    # ... other imports ...

    class BigQueryDatasetFetcher(ServiceFetcher):
        SERVICE_NAME = "bigquery_dataset" # Choose a unique key

        def fetch_resources(self, project_id, credentials):
            # Implementation using google-cloud-bigquery client library
            # ...
            return list_of_dataset_dicts
    ```
3.  The dynamic loader in `gcp_inventory_tool/fetchers/__init__.py` should automatically discover the new fetcher if placed correctly.
4.  Add the service key (`bigquery_dataset` in this example) to your `config.yaml` under the `services` list or provide it via the `--services` CLI option.
5.  Add the client library (`google-cloud-bigquery`) to `requirements.txt`.

## Development

(Add instructions for setting up a development environment, running tests, etc.)

```bash
# Example: Setup virtual environment
python -m venv .venv
source .venv/bin/activate # or .venv\Scripts\activate on Windows
pip install -r requirements.txt
pip install -e . # Install in editable mode
```

## License

[Specify Your License Here - e.g., Apache License 2.0]

---
