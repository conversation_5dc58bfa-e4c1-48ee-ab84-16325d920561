# Git and version control
.git
.gitignore
.gitattributes

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs and output (will be mounted as volumes)
*.log
logs/
output/
inventory_run.log

# Credentials (will be mounted as volumes)
credentials/
*.json
service-account*.json

# Docker
Dockerfile
docker-compose.yml
.dockerignore

# Documentation
README.md
*.md
docs/

# Test files
test_*.py

# Temporary files
tmp/
temp/
.tmp/
