# --- File: gcp_inventory_tool/fetchers/instance_template.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class InstanceTemplateFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Instance Template details for a project.
    Instance Templates are global resources.
    """
    SERVICE_NAME = "instance_template" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Instance Templates in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Instance Template fetch...")
        inventory = []
        client = compute_v1.InstanceTemplatesClient(credentials=credentials)

        try:
            # List all instance templates in the project (global resource)
            templates = client.list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing instance templates...")

            for template in templates:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": template.name,
                    "Description": template.description,
                    "Location": "global", # Instance templates are global
                    "CreationDate": template.creation_timestamp,
                    # Properties fields
                    "CanIPForward": None,
                    "MachineType": None,
                    "Disks": [], # List of disk info dicts
                    "NetworkInterfaces": [], # List of NIC info dicts
                    "ServiceAccount": None, # Email of the first service account
                    "NetworkTags": [],
                    "Labels": {}, # Template labels (not instance labels)
                    "Id": str(template.id), # Added ID
                    "service": self.SERVICE_NAME
                }

                # Process Properties if they exist
                if template.properties:
                    props = template.properties
                    info["CanIPForward"] = props.can_ip_forward
                    info["MachineType"] = props.machine_type
                    info["Labels"] = dict(props.labels) if props.labels else {} # Instance labels from template

                    # Process Disks
                    if props.disks:
                        for disk in props.disks:
                            disk_info = {
                                "DeviceName": disk.device_name,
                                "Type": str(disk.type_), # PERSISTENT or SCRATCH
                                "Mode": str(disk.mode), # READ_WRITE or READ_ONLY
                                "AutoDelete": disk.auto_delete,
                                "Boot": disk.boot,
                                "DiskType": None,
                                "SourceImage": None,
                                "DiskSizeGb": None,
                            }
                            if disk.initialize_params:
                                params = disk.initialize_params
                                disk_info["DiskType"] = get_resource_name(params.disk_type)
                                disk_info["SourceImage"] = get_resource_name(params.source_image)
                                disk_info["DiskSizeGb"] = params.disk_size_gb
                                # Add other initializeParams if needed (e.g., labels, description)
                            info["Disks"].append(disk_info)

                    # Process Network Interfaces
                    if props.network_interfaces:
                        for interface in props.network_interfaces:
                            nic_info = {
                                "Name": interface.name,
                                "Network": get_resource_name(interface.network),
                                "Subnet": get_resource_name(interface.subnetwork),
                                "NetworkIP": interface.network_ip, # If static IP assigned in template
                                "AccessConfigs": [],
                                "AliasIPRanges": [],
                                "NetworkTier": None, # Extracted from access config
                            }
                            if interface.access_configs:
                                for ac in interface.access_configs:
                                    ac_info = {
                                        "Name": ac.name,
                                        "Type": str(ac.type_), # ONE_TO_ONE_NAT
                                        "NatIP": ac.nat_ip, # If static external IP assigned
                                        "NetworkTier": str(ac.network_tier)
                                    }
                                    nic_info["AccessConfigs"].append(ac_info)
                                    # Use the tier from the first access config for the main field
                                    if info["NetworkTier"] is None:
                                        info["NetworkTier"] = str(ac.network_tier)
                            if interface.alias_ip_ranges:
                                nic_info["AliasIPRanges"] = [
                                    {"IpCidrRange": r.ip_cidr_range, "SubnetworkRangeName": r.subnetwork_range_name}
                                    for r in interface.alias_ip_ranges
                                ]
                            info["NetworkInterfaces"].append(nic_info)

                    # Process Service Accounts
                    if props.service_accounts:
                        # Typically only one is configured in a template
                        info["ServiceAccount"] = props.service_accounts[0].email
                        # Could also add scopes: list(props.service_accounts[0].scopes)

                    # Process Network Tags
                    if props.tags and props.tags.items:
                        info["NetworkTags"] = list(props.tags.items)

                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing instance templates or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Viewer). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Instance Templates: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Instance Template fetch. Found {len(inventory)} templates.")
        return inventory
