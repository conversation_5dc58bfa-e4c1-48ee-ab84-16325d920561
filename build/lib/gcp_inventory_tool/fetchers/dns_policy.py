# --- File: gcp_inventory_tool/fetchers/dns_policy.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import dns
from google.api_core import exceptions as api_exceptions

from gcp_inventory_tool.utils.resource_name import get_resource_name

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class DnsPolicyFetcher(ServiceFetcher):
    """
    Fetches Google Cloud DNS Policy details for a project.
    """
    SERVICE_NAME = "dns_policy" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud DNS Policies in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud DNS Policy fetch...")
        inventory = []
        client = dns.PoliciesClient(credentials=credentials)

        try:
            policies = client.list_policies(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing DNS policies...")

            for policy in policies:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "Project": project_id,
                    "Name": policy.name,
                    "Description": policy.description,
                    "InboundForwarding": policy.enable_inbound_forwarding,
                    "Logging": policy.enable_logging,
                    "Networks": [], # Populated below
                    "NameServers": [], # Populated below (alternative name servers)
                    "service": self.SERVICE_NAME
                }

                # Networks associated with the policy
                if policy.networks:
                    info["Networks"] = [
                        get_resource_name(net.network_url)
                        for net in policy.networks if net.network_url
                    ]

                # Alternative Name Servers Config
                if policy.alternative_name_server_config and policy.alternative_name_server_config.target_name_servers:
                    info["NameServers"] = [
                        ns.ipv4_address
                        for ns in policy.alternative_name_server_config.target_name_servers if ns.ipv4_address
                        # Add ipv6_address if needed: ns.ipv6_address
                    ]

                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing DNS policies or accessing DNS API. Ensure API is enabled and necessary roles granted (e.g., 'DNS Reader'). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled or project has no policies
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud DNS API might not be enabled or project not found / no policies exist. Details: {e}")
             return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process DNS Policies: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud DNS Policy fetch. Found {len(inventory)} policies.")
        return inventory
