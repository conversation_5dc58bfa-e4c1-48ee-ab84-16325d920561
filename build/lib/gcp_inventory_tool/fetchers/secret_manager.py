# --- File: gcp_inventory_tool/fetchers/secret_manager.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import secretmanager_v1
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger(__name__)

# Helper function to parse project, location, secret name from a full secret resource name
def _parse_secret_name(name: str) -> Optional[Tuple[str, Optional[str], str]]:
    """Parses project, location (optional), and name from a secret resource name."""
    # Format: projects/{p}/secrets/{s} OR projects/{p}/locations/{l}/secrets/{s}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 4 and parts[0] == 'projects' and parts[2] == 'secrets':
            project_id = parts[1]
            secret_name = parts[3]
            location_id = "global" # Secrets listed at project level are often global unless location specified
            return project_id, location_id, secret_name
        elif len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'secrets':
             project_id = parts[1]
             location_id = parts[3]
             secret_name = parts[5]
             return project_id, location_id, secret_name
        else:
            logger.warning(f"Unrecognized secret name format for parsing: {name}")
            # Attempt fallback parsing if possible
            if 'projects/' in name and '/secrets/' in name:
                 project_id = name.split('/')[1]
                 secret_name = name.split('/')[-1]
                 # Cannot reliably determine location from this format alone
                 return project_id, "Unknown", secret_name
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing secret name '{name}': {e}")
        return None


class SecretManagerFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Secret Manager secret details for a project.
    Note: This lists secret metadata, not the secret versions or values.
    """
    SERVICE_NAME = "secret_manager" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Secret Manager secrets in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Secret Manager secret fetch...")
        inventory = []
        client = secretmanager_v1.SecretManagerServiceClient(credentials=credentials)
        parent = f"projects/{project_id}" # List secrets at the project level

        try:
            secrets = client.list_secrets(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing secrets...")

            for secret in secrets:
                parsed_name_parts = _parse_secret_name(secret.name)
                location = parsed_name_parts[1] if parsed_name_parts else "Unknown"
                short_name = parsed_name_parts[2] if parsed_name_parts else secret.name # Fallback

                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": short_name,
                    "CreationDate": timestamp_to_iso(secret.create_time),
                    "Replication": None, # Populated below
                    "Labels": dict(secret.labels) if secret.labels else {}, # Added field
                    "ExpireTime": timestamp_to_iso(secret.expire_time), # Added field
                    "Ttl": str(secret.ttl) if secret.ttl else None, # Added field (Duration proto)
                    "Location": location, # Determined from name parsing
                    "Etag": secret.etag, # Added field
                    "service": self.SERVICE_NAME
                }

                # Process Replication policy
                if secret.replication:
                    replication_policy = secret.replication
                    if replication_policy.automatic:
                        info["Replication"] = {"type": "AUTOMATIC"}
                        # Could add customer managed encryption key name if present:
                        # replication_policy.automatic.customer_managed_encryption.kms_key_name
                    elif replication_policy.user_managed:
                        info["Replication"] = {
                            "type": "USER_MANAGED",
                            "replica_count": replication_policy.user_managed.replica_count,
                            "locations": [rep.location for rep in replication_policy.user_managed.replicas]
                        }
                    else:
                         info["Replication"] = {"type": "UNKNOWN"}


                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing secrets or accessing Secret Manager API. Ensure API is enabled and necessary roles granted (e.g., 'Secret Manager Secret Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled or project has no secrets
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Secret Manager API might not be enabled or project not found / no secrets exist. Details: {e}")
             return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Secrets: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            # Ensure the client is closed
            if client:
                client.transport.close()

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Secret Manager secret fetch. Found {len(inventory)} secrets.")
        return inventory
