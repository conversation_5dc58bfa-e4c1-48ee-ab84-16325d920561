# --- File: gcp_inventory_tool/fetchers/backend_service.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class BackendServiceFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Backend Service details for a project.
    Covers both global and regional backend services.
    """
    SERVICE_NAME = "backend_service" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Backend Services in the specified project across all regions and global.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Backend Service fetch...")
        inventory = []
        client = compute_v1.BackendServicesClient(credentials=credentials)

        try:
            # Use aggregated list to get backend services from all regions and global
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing backend services across regions/global...")

            # The response is an iterator of tuples (scope, response_object)
            for scope, response in agg_list:
                if response.backend_services:
                    scope_name = get_resource_name(scope) # Region name or 'global'
                    is_global = "global" in scope.lower()
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.backend_services)} backend services in scope {scope_name}...")

                    for bs in response.backend_services:
                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": bs.name,
                            "Description": bs.description,
                            "CreationTimestamp": bs.creation_timestamp,
                            "LoadBalancingScheme": str(bs.load_balancing_scheme),
                            "Location": scope_name if scope_name else ("global" if is_global else "Unknown"),
                            "Port": bs.port,
                            "PortName": bs.port_name,
                            "Protocol": str(bs.protocol),
                            "HealthChecks": [get_resource_name(hc) for hc in bs.health_checks] if bs.health_checks else [],
                            "Backends": [], # Populated below
                            "Logging": bs.log_config.enable if bs.log_config else None,
                            "LogSampleRate": bs.log_config.sample_rate if bs.log_config else None,
                            "AffinityCookieTtlSec": bs.affinity_cookie_ttl_sec,
                            "ConnectionDrainingTime": bs.connection_draining.draining_timeout_sec if bs.connection_draining else None,
                            "EnableCDN": bs.enable_cdn,
                            "SecurityPolicy": get_resource_name(bs.security_policy),
                            "SessionAffinity": str(bs.session_affinity),
                            "Timeout": bs.timeout_sec,
                            "Id": str(bs.id), # Added ID
                            "service": self.SERVICE_NAME
                        }

                        # Process Backends list
                        if bs.backends:
                            for backend in bs.backends:
                                backend_info = {
                                    "BalancingMode": str(backend.balancing_mode),
                                    "CapacityScaler": backend.capacity_scaler,
                                    "Group": get_resource_name(backend.group),
                                    "Description": backend.description,
                                    "MaxRatePerInstance": backend.max_rate_per_instance,
                                    "MaxConnectionsPerInstance": backend.max_connections_per_instance,
                                    # Add other backend fields if needed (e.g., max_utilization)
                                }
                                info["Backends"].append(backend_info)

                        inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing backend services or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Backend Services: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Backend Service fetch. Found {len(inventory)} services.")
        return inventory
