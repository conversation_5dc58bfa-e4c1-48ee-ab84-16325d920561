# --- File: gcp_inventory_tool/fetchers/health_check.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class HealthCheckFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Compute Health Check details for a project.
    Covers both global and regional health checks.
    """
    SERVICE_NAME = "health_check" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Health Checks in the specified project across all regions and global.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Health Check fetch...")
        inventory = []
        client = compute_v1.HealthChecksClient(credentials=credentials)

        try:
            # Use aggregated list to get health checks from all regions and global
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing health checks across regions/global...")

            # The response is an iterator of tuples (scope, response_object)
            for scope, response in agg_list:
                 # Handle cases where response might be None or lack the health_checks attribute
                if response is None or not hasattr(response, 'health_checks') or not response.health_checks:
                    continue

                scope_name = get_resource_name(scope) # Region name or 'global'
                is_global = "global" in scope.lower()
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.health_checks)} health checks in scope {scope_name}...")

                for hc in response.health_checks:
                    # Prepare base info dictionary
                    info = {
                        "ProjectID": project_id,
                        "Name": hc.name,
                        "Description": hc.description,
                        "CreationDate": hc.creation_timestamp,
                        "CheckIntervalSec": hc.check_interval_sec,
                        "HealthyThreshold": hc.healthy_threshold,
                        "UnhealthyThreshold": hc.unhealthy_threshold,
                        "TimeoutSec": hc.timeout_sec,
                        "Location": scope_name if scope_name else ("global" if is_global else "Unknown"),
                        "Type": str(hc.type_), # TCP, SSL, HTTP, HTTPS, HTTP2, GRPC
                        "LogConfigEnabled": hc.log_config.enable if hc.log_config else None, # Added field
                        "Id": str(hc.id), # Added ID
                        # Type specific fields added below
                        "HTTPHealthCheck": None,
                        "HTTPSHealthCheck": None,
                        "HTTP2HealthCheck": None,
                        "TCPHealthCheck": None,
                        "SSLHealthCheck": None,
                        "GRPCHealthCheck": None,
                        "service": self.SERVICE_NAME
                    }

                    # Populate type-specific details
                    if hc.type_ == compute_v1.HealthCheck.Type.HTTP and hc.http_health_check:
                        http_hc = hc.http_health_check
                        info["HTTPHealthCheck"] = {
                             "host": http_hc.host,
                             "port": http_hc.port,
                             "proxy_header": str(http_hc.proxy_header),
                             "request_path": http_hc.request_path,
                             "response": http_hc.response,
                        }
                    elif hc.type_ == compute_v1.HealthCheck.Type.HTTPS and hc.https_health_check:
                         https_hc = hc.https_health_check
                         info["HTTPSHealthCheck"] = {
                             "host": https_hc.host,
                             "port": https_hc.port,
                             "proxy_header": str(https_hc.proxy_header),
                             "request_path": https_hc.request_path,
                             "response": https_hc.response,
                         }
                    elif hc.type_ == compute_v1.HealthCheck.Type.HTTP2 and hc.http2_health_check:
                         http2_hc = hc.http2_health_check
                         info["HTTP2HealthCheck"] = {
                             "host": http2_hc.host,
                             "port": http2_hc.port,
                             "proxy_header": str(http2_hc.proxy_header),
                             "request_path": http2_hc.request_path,
                             "response": http2_hc.response,
                         }
                    elif hc.type_ == compute_v1.HealthCheck.Type.TCP and hc.tcp_health_check:
                         tcp_hc = hc.tcp_health_check
                         info["TCPHealthCheck"] = {
                             "port": tcp_hc.port,
                             "proxy_header": str(tcp_hc.proxy_header),
                             "request": tcp_hc.request,
                             "response": tcp_hc.response,
                         }
                    elif hc.type_ == compute_v1.HealthCheck.Type.SSL and hc.ssl_health_check:
                         ssl_hc = hc.ssl_health_check
                         info["SSLHealthCheck"] = {
                             "port": ssl_hc.port,
                             "proxy_header": str(ssl_hc.proxy_header),
                             "request": ssl_hc.request,
                             "response": ssl_hc.response,
                         }
                    elif hc.type_ == compute_v1.HealthCheck.Type.GRPC and hc.grpc_health_check:
                         grpc_hc = hc.grpc_health_check
                         info["GRPCHealthCheck"] = {
                             "port": grpc_hc.port,
                             "grpc_service_name": grpc_hc.grpc_service_name,
                         }

                    inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing health checks or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Viewer). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Health Checks: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Health Check fetch. Found {len(inventory)} health checks.")
        return inventory
