import logging
import math
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from gcp_inventory_tool.utils.resource_name import get_resource_name
# from google.api_core.extended_operation import ExtendedOperation # Not currently needed

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, zone, name from URL
def _parse_resource_url(url: str) -> Optional[Tuple[str, Optional[str], str]]:
    """Parses project, zone (optional), and name from a GCP resource URL."""
    if not isinstance(url, str):
        logger.warning(f"Invalid URL type for parsing: {type(url)}")
        return None
    parts = url.split('/')
    try:
        project_index = parts.index('projects') + 1
        resource_name = parts[-1]
        if 'zones' in parts:
            # Zonal resource (e.g., disks, instances, machineTypes)
            zone_index = parts.index('zones') + 1
            return parts[project_index], parts[zone_index], resource_name
        elif 'regions' in parts:
             # Regional resource (e.g., regional disks, subnets)
             region_index = parts.index('regions') + 1
             return parts[project_index], parts[region_index], resource_name # Return region instead of zone
        elif 'global' in parts:
             # Global resource (e.g., global addresses, networks)
             return parts[project_index], None, resource_name # No zone/region for global
        else:
            # Handle other potential formats or return None if unrecognized
            # Try finding known resource types if standard parsing fails
            known_types = ['networks', 'subnetworks', 'machineTypes', 'disks', 'instances']
            for res_type in known_types:
                if res_type in parts:
                    type_index = parts.index(res_type)
                    # Assume project is before type, name is after
                    if project_index < type_index and type_index + 1 < len(parts):
                         # This is a guess, might need refinement based on actual URLs encountered
                         return parts[project_index], None, parts[type_index + 1]
            logger.warning(f"Unrecognized resource URL format for parsing: {url}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing resource URL '{url}': {e}")
        return None


class ComputeEngineFetcher(ServiceFetcher):
    """
    Fetches detailed Compute Engine VM instance inventory, including information
    about attached disks, machine types, network interfaces, and metadata,
    similar to the provided PowerShell script logic.
    """
    SERVICE_NAME = "compute"

    def __init__(self):
        super().__init__()
        # Caches to avoid redundant API calls within a single run for a project
        # Keyed by full resource URL
        self._machine_type_cache: Dict[str, Optional[compute_v1.MachineType]] = {}
        self._disk_cache: Dict[str, Optional[compute_v1.Disk]] = {}

    def _get_machine_type_details(self, client: compute_v1.MachineTypesClient, machine_type_url: str, project_id: str) -> Tuple[Optional[int], Optional[int]]:
        """Gets CPU and Memory (in GB) for a given machine type URL, using cache."""
        if not machine_type_url:
            return None, None

        # Check cache first
        if machine_type_url in self._machine_type_cache:
            cached_details = self._machine_type_cache[machine_type_url]
            if cached_details:
                # Use math.ceil for memory calculation
                memory_gb = math.ceil(cached_details.memory_mb / 1024) if cached_details.memory_mb else None
                return cached_details.guest_cpus, memory_gb
            else:
                return None, None # Cache hit indicates previous failure

        cpu = None
        memory_gb = None
        parsed_url = _parse_resource_url(machine_type_url)
        if not parsed_url:
            logger.warning(f"[{project_id}] Could not parse machine type URL: {machine_type_url}")
            self._machine_type_cache[machine_type_url] = None # Cache failure
            return None, None

        mt_project, mt_zone, mt_name = parsed_url

        # Handle custom machine types by parsing the name
        if "custom-" in mt_name:
            try:
                parts = mt_name.split('-')
                cpu = int(parts[2])
                # Memory is often specified in MB for custom types
                memory_mb = int(parts[3])
                memory_gb = math.ceil(memory_mb / 1024)
                logger.debug(f"[{project_id}] Parsed custom machine type '{mt_name}': CPU={cpu}, Memory={memory_gb}GB")
                # Cache a mock object or specific values for custom types if needed elsewhere,
                # but for CPU/Mem, we already have the values. Caching None here prevents refetch attempt.
                self._machine_type_cache[machine_type_url] = None # Don't store full object for custom
            except (IndexError, ValueError) as e:
                logger.warning(f"[{project_id}] Failed to parse custom machine type name '{mt_name}': {e}")
                self._machine_type_cache[machine_type_url] = None # Cache failure
        # Handle standard machine types by fetching details
        elif mt_zone: # Standard machine types are zonal
            try:
                logger.debug(f"[{project_id}] Fetching details for machine type '{mt_name}' in zone '{mt_zone}'")
                request = compute_v1.GetMachineTypeRequest(
                    project=mt_project, # Use project from URL
                    zone=mt_zone,
                    machine_type=mt_name,
                )
                response = client.get(request=request)
                cpu = response.guest_cpus
                memory_gb = math.ceil(response.memory_mb / 1024) if response.memory_mb else None
                # Cache the full response object
                self._machine_type_cache[machine_type_url] = response
                logger.debug(f"[{project_id}] Fetched standard machine type '{mt_name}': CPU={cpu}, Memory={memory_gb}GB")
            except api_exceptions.NotFound:
                logger.warning(f"[{project_id}] Machine type not found: {machine_type_url}")
                self._machine_type_cache[machine_type_url] = None # Cache failure (not found)
            except api_exceptions.Forbidden as e:
                logger.error(f"[{project_id}] Permission denied fetching machine type {machine_type_url}: {e}")
                self._machine_type_cache[machine_type_url] = None # Cache failure (permission)
            except Exception as e:
                logger.error(f"[{project_id}] Error fetching machine type {machine_type_url}: {e}", exc_info=True)
                self._machine_type_cache[machine_type_url] = None # Cache failure (other error)
        else:
             logger.warning(f"[{project_id}] Machine type URL does not appear to be custom or standard zonal: {machine_type_url}")
             self._machine_type_cache[machine_type_url] = None # Cache failure


        return cpu, memory_gb

    def _get_disk_details(self, client: compute_v1.DisksClient, disk_url: str, project_id: str) -> Optional[compute_v1.Disk]:
        """Gets full disk details for a given disk URL, using a cache."""
        if not disk_url:
            return None

        # Check cache first
        if disk_url in self._disk_cache:
            return self._disk_cache[disk_url] # Return cached object or None if previous fetch failed

        parsed_url = _parse_resource_url(disk_url)
        if not parsed_url:
            logger.warning(f"[{project_id}] Could not parse disk URL: {disk_url}")
            self._disk_cache[disk_url] = None # Cache failure
            return None

        disk_project, disk_zone_or_region, disk_name = parsed_url

        # Determine if it's zonal or regional
        is_zonal = 'zones' in disk_url
        is_regional = 'regions' in disk_url

        if not (is_zonal or is_regional):
            logger.warning(f"[{project_id}] Disk URL is neither zonal nor regional: {disk_url}")
            self._disk_cache[disk_url] = None # Cache failure
            return None

        try:
            if is_zonal:
                logger.debug(f"[{project_id}] Fetching details for zonal disk '{disk_name}' in zone '{disk_zone_or_region}'")
                request = compute_v1.GetDiskRequest(
                    project=disk_project, # Use project from URL
                    zone=disk_zone_or_region,
                    disk=disk_name,
                )
                disk_info = client.get(request=request)
            else: # is_regional
                 logger.debug(f"[{project_id}] Fetching details for regional disk '{disk_name}' in region '{disk_zone_or_region}'")
                 # Note: Need RegionalDisksClient if dealing with regional disks
                 # Using DisksClient might work for get, but list is different.
                 # Assuming DisksClient.get works for regional for now, adjust if needed.
                 regional_client = compute_v1.RegionDisksClient(credentials=client._transport.credentials) # Use same creds
                 request = compute_v1.GetRegionDiskRequest(
                     project=disk_project,
                     region=disk_zone_or_region,
                     disk=disk_name,
                 )
                 disk_info = regional_client.get(request=request)


            self._disk_cache[disk_url] = disk_info # Cache the result
            return disk_info
        except api_exceptions.NotFound:
            logger.warning(f"[{project_id}] Disk not found: {disk_url}")
            self._disk_cache[disk_url] = None # Cache failure
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}] Permission denied fetching disk {disk_url}: {e}")
            self._disk_cache[disk_url] = None # Cache failure
        except Exception as e:
            logger.error(f"[{project_id}] Error fetching disk {disk_url}: {e}", exc_info=True)
            self._disk_cache[disk_url] = None # Cache failure

        return None

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches detailed Compute Engine VM inventory for the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting detailed VM fetch...")
        inventory = []
        instance_client = compute_v1.InstancesClient(credentials=credentials)
        disk_client = compute_v1.DisksClient(credentials=credentials)
        machine_type_client = compute_v1.MachineTypesClient(credentials=credentials)

        # Clear caches for each new project fetch
        self._machine_type_cache = {}
        self._disk_cache = {}

        request = compute_v1.AggregatedListInstancesRequest(
            project=project_id,
            include_all_scopes=True, # Ensure we get instances from all zones
            # return_partial_success=True # Consider this for large projects
        )

        try:
            agg_list = instance_client.aggregated_list(request=request)
            logger.info(f"[{project_id}][{self.SERVICE_NAME}] Iterating through zones/instances...")

            # Process instances zone by zone
            for zone, response in agg_list:
                if not response or not response.instances:
                    continue

                zone_name = get_resource_name(zone)
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.instances)} instances in zone {zone_name}...")

                # --- Optimization: Pre-fetch disks for the current zone ---
                if zone_name:
                    try:
                        logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Pre-fetching disks for zone {zone_name}...")
                        disk_list_request = compute_v1.ListDisksRequest(project=project_id, zone=zone_name)
                        # Iterate through pages if necessary
                        for disk in disk_client.list(request=disk_list_request):
                            if disk.self_link not in self._disk_cache:
                                self._disk_cache[disk.self_link] = disk
                        logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Disk cache updated for zone {zone_name}. Total cached disks: {len(self._disk_cache)}")
                    except api_exceptions.Forbidden as e:
                         logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing disks in zone {zone_name}: {e}")
                    except Exception as e:
                         logger.error(f"[{project_id}][{self.SERVICE_NAME}] Error listing disks in zone {zone_name}: {e}", exc_info=True)

                # Process each instance in the current zone response
                for instance in response.instances:
                    # Dictionary to hold results for this VM, matching PowerShell fields where possible
                    info = {
                        "ProjectID": project_id,
                        "Name": instance.name,
                        "ID": str(instance.id), # Ensure ID is string
                        "CreationDate": instance.creation_timestamp,
                        "Status": instance.status,
                        "Location": get_resource_name(instance.zone),
                        "VmSize": None,
                        "CPU": None,
                        "Memory": None, # In GB
                        "Storage": 0, # Sum of disk sizes in GB
                        "IP": [], # List of internal IPs
                        "AliasIpRanges": [], # List of alias ranges
                        "DiskIndex": [],
                        "DiskName": [],
                        "DiskType": [],
                        "DiskSizeGB": [],
                        "DiskSource": None, # OS Source from boot disk
                        "DiskCount": 0,
                        # "DiskDetails": "", # Omitted - Requires WMI
                        "ServiceAccounts": None,
                        "NicName": [],
                        "VPC": [],
                        "Subnet": [],
                        "PublicIP": [], # List of external IPs
                        "Tags": [],
                        "NumberNics": 0,
                        "CanIpForward": instance.can_ip_forward,
                        "Labels": {},
                        "deletionProtection": instance.deletion_protection,
                        "serialPortLoggingEnable": "False", # Default
                        "instanceTemplate": None,
                        "enableOSLogin": "False", # Default
                        "Snapshot_Policy": None, # From boot disk resource policy
                        "service": self.SERVICE_NAME
                    }

                    # --- Machine Type & CPU/Memory ---
                    info["VmSize"] = get_resource_name(instance.machine_type)
                    cpu, memory_gb = self._get_machine_type_details(machine_type_client, instance.machine_type, project_id)
                    info["CPU"] = cpu
                    info["Memory"] = memory_gb

                    # --- Metadata ---
                    if instance.metadata and instance.metadata.items:
                        metadata_items = {item.key: item.value for item in instance.metadata.items}
                        info["serialPortLoggingEnable"] = metadata_items.get("serial-port-logging-enable", "False")
                        info["instanceTemplate"] = get_resource_name(metadata_items.get("instance-template"))
                        info["enableOSLogin"] = metadata_items.get("enable-oslogin", "False")

                    # --- Disks ---
                    if instance.disks:
                        info["DiskCount"] = len(instance.disks)
                        total_storage_gb = 0
                        for attached_disk in instance.disks:
                            info["DiskIndex"].append(str(attached_disk.index))
                            info["DiskName"].append(attached_disk.device_name)

                            # Get full disk details from cache or fetch
                            disk_details = self._get_disk_details(disk_client, attached_disk.source, project_id)

                            if disk_details:
                                disk_type_name = get_resource_name(disk_details.type)
                                info["DiskType"].append(disk_type_name)
                                info["DiskSizeGB"].append(str(disk_details.size_gb))
                                total_storage_gb += disk_details.size_gb

                                # Check boot disk for OS source and snapshot policy
                                if attached_disk.boot:
                                    # Get OS from licenses or source image
                                    if disk_details.licenses:
                                        info["DiskSource"] = "; ".join([get_resource_name(lic) for lic in disk_details.licenses])
                                    elif disk_details.source_image:
                                        info["DiskSource"] = get_resource_name(disk_details.source_image)
                                    else:
                                         info["DiskSource"] = "Unknown" # Or None

                                    # Get snapshot schedule policy (Resource Policy)
                                    if disk_details.resource_policies:
                                        # Assuming the first policy is the relevant snapshot schedule
                                        # This might need refinement if multiple policies can exist
                                        info["Snapshot_Policy"] = get_resource_name(disk_details.resource_policies[0])

                            else:
                                # Append placeholders if disk details couldn't be fetched
                                info["DiskType"].append("Unknown")
                                info["DiskSizeGB"].append("Unknown")
                                if attached_disk.boot:
                                    info["DiskSource"] = "Unknown"
                                    info["Snapshot_Policy"] = "Unknown"


                        info["Storage"] = total_storage_gb

                    # --- Service Accounts ---
                    if instance.service_accounts:
                        # Typically only one service account is primary
                        info["ServiceAccounts"] = instance.service_accounts[0].email

                    # --- Network Interfaces ---
                    if instance.network_interfaces:
                        info["NumberNics"] = len(instance.network_interfaces)
                        for nic in instance.network_interfaces:
                            info["NicName"].append(nic.name)
                            info["VPC"].append(get_resource_name(nic.network))
                            info["Subnet"].append(get_resource_name(nic.subnetwork))
                            if nic.network_i_p:
                                info["IP"].append(nic.network_i_p)
                            if nic.alias_ip_ranges:
                                info["AliasIpRanges"].extend([alias.ip_cidr_range for alias in nic.alias_ip_ranges if alias.ip_cidr_range])
                            if nic.access_configs:
                                for access_config in nic.access_configs:
                                    if access_config.nat_i_p:
                                        info["PublicIP"].append(access_config.nat_i_p)

                    # --- Tags ---
                    if instance.tags and instance.tags.items:
                        info["Tags"] = list(instance.tags.items) # Store as a list

                    # --- Labels ---
                    if instance.labels:
                         info["Labels"] = dict(instance.labels) # Store as dictionary

                    # --- Format lists into strings like PowerShell script ---
                    # (Optional, depends if you prefer structured lists or semicolon strings)
                    # Example: info["IP_str"] = " ; ".join(info["IP"])
                    # For simplicity, returning lists/dicts for now. Can be formatted later.

                    inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing instances. Ensure 'Compute Engine API' is enabled and the service account has 'Compute Viewer' role. Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute Engine API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to fetch Compute Engine instances: {e}", exc_info=True)
            # Depending on policy, you might return [] or raise the exception
            return [] # Fail gracefully for this service

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished detailed VM fetch. Found {len(inventory)} instances.")
        return inventory
