# --- File: gcp_inventory_tool/fetchers/classic_vpn_gateway.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class ClassicVpnGatewayFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Classic Target VPN Gateway details for a project.
    Attempts to find the associated external IP via forwarding rules.
    """
    SERVICE_NAME = "classic_vpn_gateway" # Unique key for this service type

    def _get_forwarding_rule_ip(self, client: compute_v1.ForwardingRulesClient, rule_url: str, project_id: str, region: str) -> Optional[str]:
        """Fetches a forwarding rule and returns its IP address."""
        if not rule_url:
            return None
        rule_name = get_resource_name(rule_url)
        if not rule_name:
            return None

        try:
            request = compute_v1.GetForwardingRuleRequest(
                project=project_id,
                region=region,
                forwarding_rule=rule_name
            )
            rule = client.get(request=request)
            return rule.ip_address
        except api_exceptions.NotFound:
            logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Forwarding rule '{rule_name}' in region '{region}' not found for Classic VPN Gateway IP lookup.")
            return None
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Error fetching forwarding rule '{rule_name}' in region '{region}': {e}", exc_info=False) # Log less verbosely
            return None


    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Classic Target VPN Gateways in the specified project across all regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Classic VPN Gateway fetch...")
        inventory = []
        gateway_client = compute_v1.TargetVpnGatewaysClient(credentials=credentials)
        # Need forwarding rules client to look up IP addresses
        fwd_rule_client = compute_v1.ForwardingRulesClient(credentials=credentials)

        try:
            # Use aggregated list to get gateways from all regions
            agg_list = gateway_client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing classic VPN gateways across regions...")

            # The response is an iterator of tuples (region, response_object)
            for region, response in agg_list:
                if response.target_vpn_gateways:
                    region_name = get_resource_name(region)
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.target_vpn_gateways)} gateways in region {region_name}...")

                    for gateway in response.target_vpn_gateways:
                        # Find External IP(s) by looking up associated forwarding rules
                        external_ips = []
                        if gateway.forwarding_rules:
                            for rule_url in gateway.forwarding_rules:
                                ip = self._get_forwarding_rule_ip(fwd_rule_client, rule_url, project_id, region_name)
                                if ip:
                                    external_ips.append(ip)

                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectId": project_id,
                            "Name": gateway.name,
                            "IP": external_ips, # List of IPs found via forwarding rules
                            "Description": gateway.description,
                            "Status": str(gateway.status),
                            "Tunnels": [get_resource_name(t) for t in gateway.tunnels] if gateway.tunnels else [],
                            "ForwardingRules": [get_resource_name(fr) for fr in gateway.forwarding_rules] if gateway.forwarding_rules else [],
                            "Region": region_name,
                            "Network": get_resource_name(gateway.network),
                            "CreationTimestamp": gateway.creation_timestamp,
                            "Id": str(gateway.id), # Added ID
                            "service": self.SERVICE_NAME
                        }
                        inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing classic VPN gateways or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Classic VPN Gateways: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            # Ensure clients are closed
            if gateway_client:
                gateway_client.transport.close()
            if fwd_rule_client:
                fwd_rule_client.transport.close()

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Classic VPN Gateway fetch. Found {len(inventory)} gateways.")
        return inventory
