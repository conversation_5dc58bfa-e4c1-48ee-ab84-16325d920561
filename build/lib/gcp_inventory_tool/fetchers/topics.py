# --- File: gcp_inventory_tool/fetchers/pubsub_topic.py ---
import logging
from typing import List, Dict, Any
from google.oauth2.credentials import Credentials
from google.cloud import pubsub_v1
from google.api_core import exceptions as api_exceptions

from ..utils.duration_to_second import duration_to_seconds_str
from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class PubSubTopicFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Pub/Sub topic details for a project.
    Maps details similarly to the provided PowerShell script's output.
    """
    SERVICE_NAME = "pubsub_topic" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Pub/Sub topics in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Pub/Sub topic fetch...")
        inventory = []
        publisher_client = pubsub_v1.PublisherClient(credentials=credentials)
        project_path = f"projects/{project_id}"

        try:
            # List all topics in the project. This returns Topic objects directly.
            topics = publisher_client.list_topics(request={"project": project_path})
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing topics...")

            for topic in topics:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": get_resource_name(topic.name),
                    "MessageRetentionDuration": None, # Store as string e.g., "604800s"
                    "Schema": None,
                    "Encoding": None,
                    "Labels": {}, # Store as dict
                    "service": self.SERVICE_NAME
                    # Add other relevant Topic fields if needed in the future
                    # e.g., topic.kms_key_name
                }

                # Populate fields from the Topic object
                info["MessageRetentionDuration"] = duration_to_seconds_str(topic.message_retention_duration)

                # Schema Settings
                if topic.schema_settings:
                    # Extract only the schema name if needed, or keep the full path
                    info["Schema"] = topic.schema_settings.schema
                    # Get the enum value name (e.g., 'ENCODING_UNSPECIFIED', 'JSON', 'BINARY')
                    info["Encoding"] = pubsub_v1.types.Encoding(topic.schema_settings.encoding).name

                # Labels
                if topic.labels:
                    info["Labels"] = dict(topic.labels) # Store as dictionary

                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing topics or accessing Pub/Sub API. Ensure 'Pub/Sub API' is enabled and the service account has necessary roles (e.g., 'Pub/Sub Viewer'). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Pub/Sub API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Pub/Sub topics: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            # Ensure the client is closed
            try:
                if publisher_client:
                    publisher_client.close()
            except:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Pub/Sub topic fetch. Found {len(inventory)} topics.")
        return inventory
