# --- File: gcp_inventory_tool/fetchers/data_fusion.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import data_fusion_v1
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, instance name from a full Data Fusion resource name
def _parse_datafusion_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and instance name from a Data Fusion resource name."""
    # Format: projects/{project}/locations/{location}/instances/{instance_name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'instances':
            project_id = parts[1]
            location_id = parts[3]
            instance_name = parts[5]
            return project_id, location_id, instance_name
        else:
            logger.warning(f"Unrecognized Data Fusion instance name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing Data Fusion instance name '{name}': {e}")
        return None


class DataFusionFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Data Fusion instance details for a project.
    Lists instances across all locations.
    """
    SERVICE_NAME = "data_fusion" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Data Fusion instances in the specified project across all locations.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Data Fusion instance fetch...")
        inventory = []
        # Use the v1beta1 client as per the gcloud command
        client = data_fusion_v1.DataFusionClient(credentials=credentials)
        # Parent path to list instances across all locations
        parent = f"projects/{project_id}/locations/-"

        try:
            instances = client.list_instances(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Data Fusion instances across locations...")

            for instance in instances:
                parsed_name_parts = _parse_datafusion_name(instance.name)
                location = parsed_name_parts[1] if parsed_name_parts else "Unknown"
                short_name = parsed_name_parts[2] if parsed_name_parts else instance.name # Fallback

                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": short_name,
                    "CreationDate": timestamp_to_iso(instance.create_time),
                    "UpdationDate": timestamp_to_iso(instance.update_time),
                    "State": str(instance.state), # Get enum name (e.g., RUNNING, CREATING)
                    "Location": location, # Parsed from name
                    "Type": str(instance.type_), # BASIC, ENTERPRISE, DEVELOPER
                    "APIEndpoint": instance.api_endpoint,
                    "ServiceEndpoint": instance.service_endpoint,
                    "GCSBucket": instance.gcs_bucket,
                    "Network": None, # Populated below
                    "NetworkIpAllocation": None, # Added field
                    "ServiceAccount": instance.p4_service_account, # Note: p4sa might be deprecated/internal
                    "TenantprojectID": instance.tenant_project_id,
                    "Labels": dict(instance.labels) if instance.labels else {}, # Added field
                    "Version": instance.version, # Added field
                    "service": self.SERVICE_NAME
                }

                # Network Config
                if instance.network_config:
                    nc = instance.network_config
                    info["Network"] = nc.network
                    info["NetworkIpAllocation"] = nc.ip_allocation

                inventory.append(info)

        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Data Fusion API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Data Fusion instances. Ensure API is enabled and necessary roles granted (e.g., 'Data Fusion Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Data Fusion instances: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Data Fusion instance fetch. Found {len(inventory)} instances.")
        return inventory
    
