# --- File: gcp_inventory_tool/fetchers/cloud_router.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class CloudRouterFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Router details for a project, including BGP, Peers, and NAT info.
    """
    SERVICE_NAME = "cloud_router" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud Routers in the specified project across all regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud Router fetch...")
        inventory = []
        client = compute_v1.RoutersClient(credentials=credentials)

        try: # Outer try block starts here
            # Use aggregated list to get routers from all regions
            # Note: PowerShell script skipped global, this fetches regional only implicitly
            # as aggregated_list for routers is regional.
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing routers across regions...")

            # The response is an iterator of tuples (region, response_object)
            for region, response in agg_list:
                # Indentation level 1 inside the try block
                if response.routers:
                    region_name = get_resource_name(region)
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.routers)} routers in region {region_name}...")

                    for router in response.routers:
                        # Indentation level 2 inside the try block
                        try: # Inner try for processing individual router (optional but good practice)
                            # Prepare base info dictionary for the router
                            info = {
                                "ProjectID": project_id,
                                "Name": router.name,
                                "Description": router.description,
                                "Region": region_name,
                                "Network": get_resource_name(router.network),
                                "CreationTimestamp": router.creation_timestamp,
                                "Id": str(router.id), # Added ID
                                # BGP Info
                                "AdvertiseMode": None,
                                "ASN": None,
                                "AdvertisedGroups": [],
                                "AdvertisedRanges": [], # List of {"range": "...", "description": "..."}
                                "BGPPeers": [], # List of BGP Peer details
                                "Interfaces": [], # List of Interface details
                                "InterfaceCount": len(router.interfaces) if router.interfaces else 0,
                                "NATs": [], # List of NAT details
                                "service": self.SERVICE_NAME
                            }

                            # Process BGP Info
                            if router.bgp:
                                bgp = router.bgp
                                info["AdvertiseMode"] = str(bgp.advertise_mode) if bgp.advertise_mode else None
                                info["ASN"] = bgp.asn
                                info["AdvertisedGroups"] = list(bgp.advertised_groups) if bgp.advertised_groups else []
                                if bgp.advertised_ip_ranges:
                                    info["AdvertisedRanges"] = [
                                        {"range": r.range, "description": r.description}
                                        for r in bgp.advertised_ip_ranges
                                    ]

                            # Process BGP Peers
                            if router.bgp_peers:
                                for peer in router.bgp_peers:
                                    peer_info = {
                                        "Name": peer.name,
                                        "InterfaceName": peer.interface_name,
                                        "IpAddress": peer.ip_address,
                                        "PeerIpAddress": peer.peer_ip_address,
                                        "PeerAsn": peer.peer_asn,
                                        "AdvertiseMode": str(peer.advertise_mode) if peer.advertise_mode else None,
                                        "AdvertisedGroups": list(peer.advertised_groups) if peer.advertised_groups else [],
                                        "AdvertisedRoutePriority": peer.advertised_route_priority,
                                        "Enable": str(peer.enable) if peer.enable else None, # BGPPeer.Enable enum
                                        "ManagementType": str(peer.management_type) if peer.management_type else None,
                                    }
                                    info["BGPPeers"].append(peer_info)

                            # Process Interfaces
                            if router.interfaces:
                                 for interface in router.interfaces:
                                     if_info = {
                                         "Name": interface.name,
                                         "IpAddress": interface.ip_address, # Added in newer API versions
                                         "LinkedVpnTunnel": get_resource_name(interface.linked_vpn_tunnel),
                                         "LinkedInterconnectAttachment": get_resource_name(interface.linked_interconnect_attachment),
                                         "ManagementType": str(interface.management_type) if interface.management_type else None,
                                     }
                                     info["Interfaces"].append(if_info)


                            # Process NATs
                            if router.nats:
                                for nat in router.nats:
                                    nat_info = {
                                        "Name": nat.name,
                                        "LoggingEnabled": nat.log_config.enable if nat.log_config else None,
                                        "LogFilter": str(nat.log_config.filter) if nat.log_config and nat.log_config.filter else None,
                                        "SourceSubnetworkIpRangesToNat": str(nat.source_subnetwork_ip_ranges_to_nat),
                                        "NatIpAllocateOption": str(nat.nat_ip_allocate_option) if nat.nat_ip_allocate_option else None,
                                        "NatIps": [get_resource_name(ip) for ip in nat.nat_ips] if nat.nat_ips else [],
                                        "MinPortsPerVm": nat.min_ports_per_vm,
                                        "IcmpIdleTimeoutSec": nat.icmp_idle_timeout_sec,
                                        "TcpEstablishedIdleTimeoutSec": nat.tcp_established_idle_timeout_sec,
                                        "TcpTimeWaitTimeoutSec": nat.tcp_time_wait_timeout_sec,
                                        "UdpIdleTimeoutSec": nat.udp_idle_timeout_sec,
                                        "DrainNatIps": [get_resource_name(ip) for ip in nat.drain_nat_ips] if nat.drain_nat_ips else [], # Added drain_nat_ips
                                        "SubnetsToNat": [] # Populated below
                                    }
                                    # Handle different ways subnets can be specified
                                    if nat.subnetworks:
                                         nat_info["SubnetsToNat"] = [
                                             {"name": get_resource_name(sub.name),
                                              "source_ip_ranges": list(sub.source_ip_ranges_to_nat)}
                                             for sub in nat.subnetworks
                                         ]

                                    info["NATs"].append(nat_info) # Append NAT info nested within router

                            inventory.append(info) # Append the main router info

                        except Exception as inner_e:
                             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed processing router '{router.name}' in region {region_name}: {inner_e}", exc_info=True)
                             # Decide if you want to append partial info or skip

        # Correctly indented except blocks for the outer try
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing routers or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Cloud Routers: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        # Correctly indented finally block for the outer try
        finally:
            # Ensure the client is closed
            if client:
                try:
                    client.transport.close()
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Router client transport closed.")
                except Exception as close_e:
                     logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error closing router client transport: {close_e}")


        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud Router fetch. Found {len(inventory)} routers.")
        return inventory
