# --- File: gcp_inventory_tool/fetchers/pubsub.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import pubsub_v1
from google.api_core import exceptions as api_exceptions

from ..utils.duration_to_second import duration_to_seconds_str
from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


# Helper function to parse project and name from a resource path
def _parse_pubsub_resource_path(path: str) -> Optional[Tuple[str, str]]:
    """Parses project and name from a Pub/Sub resource path."""
    # Format: projects/{project}/resource_type/{name}
    if not isinstance(path, str):
        logger.warning(f"Invalid path type for parsing: {type(path)}")
        return None
    parts = path.split('/')
    try:
        if len(parts) == 4 and parts[0] == 'projects' and parts[2] in ['topics', 'subscriptions', 'schemas', 'snapshots']:
            project_id = parts[1]
            resource_name = parts[3]
            return project_id, resource_name
        else:
            logger.warning(f"Unrecognized resource path format for parsing: {path}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing resource path '{path}': {e}")
        return None



class PubSubFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Pub/Sub subscription details for a project.
    Maps details similarly to the provided PowerShell script's output.
    """
    SERVICE_NAME = "pubsub" # Must match the key in config.yaml

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Pub/Sub subscriptions in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Pub/Sub subscription fetch...")
        inventory = []
        subscriber_client = pubsub_v1.SubscriberClient(credentials=credentials)
        project_path = f"projects/{project_id}"

        try:
            # List all subscription paths in the project
            subscription_paths = subscriber_client.list_subscriptions(request={"project": project_path})
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Found subscription paths, fetching details...")

            # Need to get details for each subscription individually
            for sub_path_obj in subscription_paths:
                sub_path = sub_path_obj.name # The full path string
                try:
                    # Get the full subscription object
                    subscription = subscriber_client.get_subscription(request={"subscription": sub_path})

                    # Prepare info dictionary based on PowerShell script's fields
                    info = {
                        "ProjectID": project_id,
                        "Name": get_resource_name(subscription.name),
                        "State": None, # Requires get_subscription
                        "AckDeadlineSeconds": None,
                        "MessageRetentionDuration": None, # Store as string e.g., "604800s"
                        "Attributes": None, # Push config attributes
                        "Endpoint": None, # Push config endpoint
                        "Topic": None,
                        "TopicProject": None,
                        "ExactlyOnceDelivery": None,
                        "DeadLetterTopic": None,
                        "DeadLetterTopicProject": None,
                        "DeliveryAttempts": None, # Max delivery attempts for dead lettering
                        "MaxBackoff": None, # Retry policy max backoff string
                        "MinBackoff": None, # Retry policy min backoff string
                        "TTL": None, # Expiration policy TTL string
                        "MessageOrdering": None,
                        "service": self.SERVICE_NAME
                    }

                    # Populate fields from the full subscription object
                    info["State"] = str(subscription.state) if subscription.state else None # Get state enum string value
                    info["AckDeadlineSeconds"] = subscription.ack_deadline_seconds
                    info["MessageRetentionDuration"] = duration_to_seconds_str(subscription.message_retention_duration)
                    info["ExactlyOnceDelivery"] = subscription.enable_exactly_once_delivery
                    info["MessageOrdering"] = subscription.enable_message_ordering

                    # Push Configuration
                    if subscription.push_config:
                        info["Endpoint"] = subscription.push_config.push_endpoint
                        # Store attributes as a dictionary
                        info["Attributes"] = dict(subscription.push_config.attributes) if subscription.push_config.attributes else None

                    # Topic Info
                    if subscription.topic:
                        topic_project, topic_name = _parse_pubsub_resource_path(subscription.topic) or (None, None)
                        info["Topic"] = topic_name
                        info["TopicProject"] = topic_project

                    # Dead Letter Policy
                    if subscription.dead_letter_policy:
                        info["DeliveryAttempts"] = subscription.dead_letter_policy.max_delivery_attempts
                        if subscription.dead_letter_policy.dead_letter_topic:
                            dl_topic_project, dl_topic_name = _parse_pubsub_resource_path(subscription.dead_letter_policy.dead_letter_topic) or (None, None)
                            info["DeadLetterTopic"] = dl_topic_name
                            info["DeadLetterTopicProject"] = dl_topic_project # Store project ID

                    # Retry Policy
                    if subscription.retry_policy:
                        info["MinBackoff"] = duration_to_seconds_str(subscription.retry_policy.minimum_backoff)
                        info["MaxBackoff"] = duration_to_seconds_str(subscription.retry_policy.maximum_backoff)

                    # Expiration Policy
                    if subscription.expiration_policy:
                        info["TTL"] = duration_to_seconds_str(subscription.expiration_policy.ttl)

                    inventory.append(info)

                except api_exceptions.NotFound:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Subscription not found during get details: {sub_path}. Might have been deleted after listing.")
                except api_exceptions.Forbidden as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied getting details for subscription {sub_path}: {e}")
                    # Optionally add a placeholder entry indicating permission issue
                except Exception as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to get details for subscription {sub_path}: {e}", exc_info=True)
                    # Optionally add a placeholder entry indicating error


        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing subscriptions or accessing Pub/Sub API. Ensure 'Pub/Sub API' is enabled and the service account has necessary roles (e.g., 'Pub/Sub Viewer'). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Pub/Sub API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Pub/Sub subscriptions: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            # Ensure the client is closed
            if subscriber_client:
                subscriber_client.close()


        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Pub/Sub subscription fetch. Found details for {len(inventory)} subscriptions.")
        return inventory
