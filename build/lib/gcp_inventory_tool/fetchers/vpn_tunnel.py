# --- File: gcp_inventory_tool/fetchers/vpn_tunnel.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class VpnTunnelFetcher(ServiceFetcher):
    """
    Fetches Google Cloud VPN Tunnel details for a project.
    """
    SERVICE_NAME = "vpn_tunnel" # Unique key for this service type

    def _determine_tunnel_type(self, local_selectors: List[str], remote_selectors: List[str]) -> str:
        """Determines if a tunnel is Route-Based or Policy-Based."""
        # Route-based typically uses 0.0.0.0/0 for both selectors
        is_local_any = any(s == "0.0.0.0/0" for s in local_selectors)
        is_remote_any = any(s == "0.0.0.0/0" for s in remote_selectors)
        # Consider it route-based if *both* sides have the any selector (0.0.0.0/0)
        # This matches the PowerShell logic. Adjust if different definition needed.
        if is_local_any and is_remote_any and len(local_selectors) == 1 and len(remote_selectors) == 1:
            return "Route Based"
        else:
            return "Policy Based"

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all VPN Tunnels in the specified project across all regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting VPN Tunnel fetch...")
        inventory = []
        client = compute_v1.VpnTunnelsClient(credentials=credentials)

        try:
            # Use aggregated list to get tunnels from all regions
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing VPN tunnels across regions...")

            # The response is an iterator of tuples (region, response_object)
            for region, response in agg_list:
                if response.vpn_tunnels:
                    region_name = get_resource_name(region)
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.vpn_tunnels)} tunnels in region {region_name}...")

                    for tunnel in response.vpn_tunnels:
                        # Determine gateway type and name
                        gateway_name = None
                        if tunnel.vpn_gateway: # HA VPN Gateway
                            gateway_name = get_resource_name(tunnel.vpn_gateway)
                        elif tunnel.target_vpn_gateway: # Classic VPN Gateway
                             gateway_name = get_resource_name(tunnel.target_vpn_gateway)

                        # Determine tunnel type
                        tunnel_type = self._determine_tunnel_type(
                            tunnel.local_traffic_selector,
                            tunnel.remote_traffic_selector
                        )

                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectId": project_id,
                            "Name": tunnel.name,
                            "Description": tunnel.description,
                            "Type": tunnel_type, # Route Based or Policy Based
                            "PeerIP": tunnel.peer_ip,
                            "Region": region_name,
                            "Router": get_resource_name(tunnel.router),
                            "VPNGateway": gateway_name, # Name of HA or Classic gateway
                            "LocalTrafficSelector": list(tunnel.local_traffic_selector) if tunnel.local_traffic_selector else [],
                            "RemoteTrafficSelector": list(tunnel.remote_traffic_selector) if tunnel.remote_traffic_selector else [],
                            "Status": str(tunnel.status), # e.g., ESTABLISHED, FIRST_HANDSHAKE
                            "CreationTimestamp": tunnel.creation_timestamp,
                            "Id": str(tunnel.id), # Added ID
                            "SharedSecretHash": tunnel.shared_secret_hash, # Added field (hash only)
                            "IkeVersion": tunnel.ike_version, # Added field
                            "service": self.SERVICE_NAME
                        }
                        inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing VPN tunnels or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process VPN Tunnels: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished VPN Tunnel fetch. Found {len(inventory)} tunnels.")
        return inventory
