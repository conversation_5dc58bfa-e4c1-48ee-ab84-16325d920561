# --- File: gcp_inventory_tool/fetchers/cloud_run_service.py ---
import logging
import json # For parsing annotations
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import run_v2
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')
# Assuming the masking utility is available, adjust the import path as needed
try:
    from ..utils.mask_password import mask_value_based_on_name
except ImportError:
    logger.warning("Masking utility not found. Environment variables will not be masked.")
    # Define a dummy function if masking is unavailable
    def mask_value_based_on_name(name: str, value: Optional[str], **kwargs) -> Optional[str]:
        return value


class CloudRunServiceFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Run service details for a project.
    Lists services across all locations using the v2 API.
    """
    SERVICE_NAME = "cloud_run_service" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud Run services in the specified project across all locations.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud Run service fetch...")
        inventory = []
        client = run_v2.ServicesClient(credentials=credentials)
        parent = f"projects/{project_id}/locations/-" # List across all locations

        try:
            services = client.list_services(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Cloud Run services across locations...")

            for service in services:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": service.name.split('/')[-1], # Extract short name
                    "Location": service.name.split('/')[3], # Extract location from full name
                    "Creator": None,
                    "MinInstances": None,
                    "MaxInstances": None,
                    "Cpu": None,
                    "Memory": None,
                    "Image": None,
                    "Port": None,
                    "ConcurrentRequests": None,
                    "LastModifier": None,
                    "APIVersion": service.api_version, # Should be v2
                    "CreationDate": timestamp_to_iso(service.create_time),
                    "Generation": service.generation,
                    "URL": service.uri,
                    "LastDeployed": timestamp_to_iso(service.update_time), # Use update_time as proxy
                    "Ingress": str(service.ingress) if service.ingress else None,
                    "IngressStatus": None, # Not directly available in v2 Service object like annotation
                    "Egress": None,
                    "VpcConnector": None,
                    "DirectVPCEgress": None,
                    "DirectVPCEgressSubnet": None,
                    "DirectVPCEgressTags": [],
                    "ServiceAccount": None,
                    "EnvironmentVariables": {}, # Dict of non-secret env vars
                    "EnvironmentSecrets": [], # List of dicts for secret refs
                    "Labels": dict(service.labels) if service.labels else {}, # Service labels
                    "TemplateLabels": {}, # Template labels
                    "TemplateAnnotations": {}, # Template annotations
                    "service": self.SERVICE_NAME
                }

                # Extract info from annotations (metadata)
                if service.annotations:
                    info["Creator"] = service.annotations.get("serving.knative.dev/creator")
                    info["LastModifier"] = service.annotations.get("serving.knative.dev/lastModifier")
                    # Ingress settings might also be in annotations for v1 compatibility, but v2 has a dedicated field
                    # info["Ingress"] = service.annotations.get("run.googleapis.com/ingress", info["Ingress"]) # Use dedicated field first
                    info["IngressStatus"] = service.annotations.get("run.googleapis.com/ingress-status")

                # Extract info from the latest ready revision template if available
                # Note: This assumes the latest ready revision reflects the desired state.
                # Could also use `service.template` directly for the defined template.
                if service.latest_ready_revision:
                    # To get full template details, might need get_revision call,
                    # but list_services includes the template used by the latest ready revision
                    template = service.template # Use the service's defined template
                    if template:
                         info["TemplateLabels"] = dict(template.labels) if template.labels else {}
                         info["TemplateAnnotations"] = dict(template.annotations) if template.annotations else {}
                         info["ServiceAccount"] = template.service_account

                         # Scaling annotations
                         if template.annotations:
                             info["MinInstances"] = template.annotations.get("autoscaling.knative.dev/minScale")
                             info["MaxInstances"] = template.annotations.get("autoscaling.knative.dev/maxScale")
                             info["VpcConnector"] = template.annotations.get("run.googleapis.com/vpc-access-connector")
                             info["Egress"] = template.annotations.get("run.googleapis.com/vpc-access-egress")
                             # Parse network interfaces annotation
                             ni_json = template.annotations.get("run.googleapis.com/network-interfaces")
                             if ni_json:
                                 try:
                                     ni_list = json.loads(ni_json)
                                     if ni_list and isinstance(ni_list, list):
                                         # Assuming only one network interface definition in the list
                                         ni_data = ni_list[0]
                                         info["DirectVPCEgress"] = ni_data.get("network")
                                         info["DirectVPCEgressSubnet"] = ni_data.get("subnetwork")
                                         info["DirectVPCEgressTags"] = ni_data.get("tags", [])
                                 except json.JSONDecodeError as e:
                                     logger.warning(f"[{project_id}][{info['Name']}] Failed to parse network-interfaces annotation: {e}")


                         # Container details (assuming one container)
                         if template.containers:
                             container = template.containers[0]
                             info["Image"] = container.image
                             if container.ports:
                                 info["Port"] = container.ports[0].container_port
                             if container.resources and container.resources.limits:
                                 info["Cpu"] = container.resources.limits.get("cpu")
                                 info["Memory"] = container.resources.limits.get("memory")

                             # Environment Variables & Secrets
                             if container.env:
                                 env_vars = {}
                                 secrets = []
                                 for env_var in container.env:
                                     if env_var.value:
                                         # Mask potentially sensitive values based on name
                                         masked_value = mask_value_based_on_name(env_var.name, env_var.value)
                                         env_vars[env_var.name] = masked_value
                                     elif env_var.value_source and env_var.value_source.secret_key_ref:
                                         ref = env_var.value_source.secret_key_ref
                                         secrets.append({
                                             "name": env_var.name,
                                             "secretName": ref.secret,
                                             "secretVersion": ref.version
                                         })
                                 info["EnvironmentVariables"] = env_vars
                                 info["EnvironmentSecrets"] = secrets

                         # Concurrency
                         info["ConcurrentRequests"] = template.container_concurrency


                inventory.append(info)

        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud Run API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Cloud Run services. Ensure API is enabled and necessary roles granted (e.g., 'Cloud Run Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Cloud Run services: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud Run service fetch. Found {len(inventory)} services.")
        return inventory
