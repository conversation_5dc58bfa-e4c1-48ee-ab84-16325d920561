# --- File: gcp_inventory_tool/fetchers/ssl_certificate.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class SslCertificateFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Compute SSL Certificate details for a project.
    Covers both global and regional certificates.
    """
    SERVICE_NAME = "ssl_certificate" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all SSL Certificates in the specified project across all regions and global.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting SSL Certificate fetch...")
        inventory = []
        client = compute_v1.SslCertificatesClient(credentials=credentials)

        try:
            # Use aggregated list to get certificates from all regions and global
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing SSL certificates across regions/global...")

            # The response is an iterator of tuples (scope, response_object)
            for scope, response in agg_list:
                 # Handle cases where response might be None or lack the ssl_certificates attribute
                if response is None or not hasattr(response, 'ssl_certificates') or not response.ssl_certificates:
                    continue

                scope_name = get_resource_name(scope) # Region name or 'global'
                is_global = "global" in scope.lower()
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.ssl_certificates)} certificates in scope {scope_name}...")

                for cert in response.ssl_certificates:
                    # Prepare base info dictionary
                    info = {
                        "ProjectID": project_id,
                        "Name": cert.name,
                        "CreationDate": cert.creation_timestamp,
                        "Location": scope_name if scope_name else ("global" if is_global else "Unknown"),
                        "Type": str(cert.type_), # MANAGED or SELF_MANAGED
                        "Description": cert.description, # Added field
                        "ExpireTime": cert.expire_time, # Added field
                        "SubjectAlternativeNames": list(cert.subject_alternative_names) if cert.subject_alternative_names else [], # Added field
                        "Id": str(cert.id), # Added ID
                        # Managed Cert fields
                        "ManagedDomains": [],
                        "ManagedStatus": None,
                        "ManagedDomainStatus": {}, # Dict mapping domain to status
                        # SelfManaged fields (URLs only, not content)
                        "SelfManagedCertificate": cert.certificate if cert.self_managed else None, # Added field (usually large string)
                        "SelfManagedPrivateKey": "[REDACTED]" if cert.self_managed and cert.private_key else None, # Added field (never expose private key)
                        "service": self.SERVICE_NAME
                    }

                    # Populate managed certificate details
                    if cert.type_ == compute_v1.SslCertificate.Type.MANAGED and cert.managed:
                        managed = cert.managed
                        info["ManagedDomains"] = list(managed.domains) if managed.domains else []
                        info["ManagedStatus"] = str(managed.status) if managed.status else None # ACTIVE, PROVISIONING etc.
                        if managed.domain_status:
                             info["ManagedDomainStatus"] = {domain: str(status) for domain, status in managed.domain_status.items()}


                    inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing SSL certificates or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Viewer). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process SSL Certificates: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished SSL Certificate fetch. Found {len(inventory)} certificates.")
        return inventory
