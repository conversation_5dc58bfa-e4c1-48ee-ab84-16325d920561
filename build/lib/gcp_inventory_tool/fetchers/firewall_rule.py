# --- File: gcp_inventory_tool/fetchers/firewall_rule.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger(__name__)


class FirewallRuleFetcher(ServiceFetcher):
    """
    Fetches Google Cloud VPC Firewall Rule details for a project.
    """
    SERVICE_NAME = "firewall_rule" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all VPC Firewall Rules in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting VPC Firewall Rule fetch...")
        inventory = []
        client = compute_v1.FirewallsClient(credentials=credentials)

        try:
            # List all firewall rules in the project
            rules = client.list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing firewall rules...")

            for rule in rules:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": rule.name,
                    "CreationDate": rule.creation_timestamp,
                    "Description": rule.description,
                    "DestinationRanges": list(rule.destination_ranges) if rule.destination_ranges else [],
                    "Direction": str(rule.direction), # E.g., "INGRESS", "EGRESS"
                    "Network": get_resource_name(rule.network),
                    "Priority": rule.priority,
                    "SourceRanges": list(rule.source_ranges) if rule.source_ranges else [],
                    "SourceTags": list(rule.source_tags) if rule.source_tags else [],
                    "TargetTags": list(rule.target_tags) if rule.target_tags else [],
                    "Action": None, # Determined below
                    "TargetProtocol": [], # Determined below
                    "TargetPorts": [], # Determined below
                    "Disabled": rule.disabled, # Added field
                    "LogConfigEnabled": rule.log_config.enable if rule.log_config else None, # Added field
                    "service": self.SERVICE_NAME
                }

                # Determine Action, Protocol, Ports based on Allowed/Denied rules
                # Mimics PowerShell logic where Denied might overwrite Allowed if both present
                action = None
                protocols = []
                ports = []

                if rule.allowed:
                    action = "Allow"
                    for allow_rule in rule.allowed:
                        if allow_rule.ip_protocol:
                            protocols.append(allow_rule.ip_protocol)
                        if allow_rule.ports:
                            ports.extend(allow_rule.ports)

                if rule.denied:
                    action = "Deny" # Overwrites if both exist
                    protocols = []  # Reset protocols/ports if denied exists
                    ports = []
                    for deny_rule in rule.denied:
                        if deny_rule.ip_protocol:
                            protocols.append(deny_rule.ip_protocol)
                        if deny_rule.ports:
                            ports.extend(deny_rule.ports)

                info["Action"] = action
                # Remove duplicates and store unique values
                info["TargetProtocol"] = sorted(list(set(protocols)))
                info["TargetPorts"] = sorted(list(set(ports)))

                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing firewall rules or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process VPC Firewall Rules: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            # Ensure the client is closed
            if client:
                client.transport.close()

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished VPC Firewall Rule fetch. Found {len(inventory)} rules.")
        return inventory
