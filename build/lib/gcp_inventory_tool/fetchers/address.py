# --- File: gcp_inventory_tool/fetchers/address.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')



class AddressFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Compute Address details (External/Internal IPs) for a project.
    """
    SERVICE_NAME = "address" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Compute Addresses in the specified project across all regions and global.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Compute Address (PIP) fetch...")
        inventory = []
        client = compute_v1.AddressesClient(credentials=credentials)

        try:
            # Use aggregated list to get addresses from all regions and global
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing addresses across regions/global...")

            # The response is an iterator of tuples (scope, response_object)
            # Scope is like "regions/us-central1" or "global"
            for scope, response in agg_list:
                if response.addresses:
                    scope_name = get_resource_name(scope) # Region name or 'global'
                    is_global = "global" in scope.lower()
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.addresses)} addresses in scope {scope_name}...")

                    for address in response.addresses:
                        # Determine location
                        location = scope_name if scope_name else ("global" if is_global else "Unknown")

                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": address.name,
                            "Description": address.description,
                            "AddressType": str(address.address_type), # EXTERNAL, INTERNAL, etc.
                            "NetworkTier": str(address.network_tier), # PREMIUM, STANDARD
                            "Location": location,
                            "Users": [get_resource_name(user) for user in address.users] if address.users else [], # List of user resource names
                            "Address": address.address,
                            "Status": str(address.status), # RESERVED, IN_USE
                            "CreationTimestamp": address.creation_timestamp,
                            "Id": str(address.id), # Added ID
                            "Network": get_resource_name(address.network), # Added field
                            "Subnetwork": get_resource_name(address.subnetwork), # Added field
                            "Purpose": str(address.purpose), # Added field (e.g., GCE_ENDPOINT, VPC_PEERING)
                            "PrefixLength": address.prefix_length, # Added field (for internal IPs)
                            "service": self.SERVICE_NAME
                        }

                        # Note: Filtering based on Status == "IN_USE" (like in the PowerShell script)
                        # is NOT applied here. The full list is returned.
                        # Filtering can be done downstream using the 'Status' field.

                        inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing addresses or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Compute Addresses: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Compute Address fetch. Found {len(inventory)} addresses.")
        return inventory
