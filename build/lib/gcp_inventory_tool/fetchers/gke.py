# --- File: gcp_inventory_tool/fetchers/gke_cluster.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import container_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class GKEClusterFetcher(ServiceFetcher):
    """
    Fetches Google Kubernetes Engine (GKE) cluster details for a project.
    Maps details similarly to the provided PowerShell script's output,
    excluding the external dependency for persistent disk mapping.
    """
    SERVICE_NAME = "gke_cluster" # Unique key for this service type

    def _format_maintenance_window(self, window: Optional[container_v1.types.MaintenanceWindow]) -> Optional[str]:
        """Formats the maintenance window into a string."""
        if not window or not window.recurring_window:
            return None
        rw = window.recurring_window
        start = rw.window.start_time if rw.window else "N/A"
        end = rw.window.end_time if rw.window else "N/A"
        recurrence = rw.recurrence or "N/A"
        return f"{recurrence} ; StartTime: {start} ; EndTime: {end}"

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all GKE clusters in the specified project across all locations.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting GKE cluster fetch...")
        inventory = []
        client = container_v1.ClusterManagerClient(credentials=credentials)
        # Parent path to list clusters across all locations for the project
        parent = f"projects/{project_id}/locations/-"

        try:
            response = client.list_clusters(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing listed clusters...")

            for cluster in response.clusters:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": cluster.name,
                    "Status": container_v1.types.Cluster.Status(cluster.status).name, # Get enum name
                    "Network": get_resource_name(cluster.network),
                    "Subnetwork": get_resource_name(cluster.subnetwork),
                    "Labels": dict(cluster.resource_labels) if cluster.resource_labels else {},
                    "Location": cluster.location, # Zone or region
                    "EnablePrivateNodes": None,
                    "ClusterIpv4Cidr": cluster.cluster_ipv4_cidr,
                    "MasterIpv4CidrBlock": None,
                    "ServicesIpv4Cidr": cluster.services_ipv4_cidr,
                    "PrivateEndpoint": None,
                    "PublicEndpoint": cluster.endpoint,
                    "CreationDate": cluster.create_time,
                    "CurrentMasterVersion": cluster.current_master_version,
                    "CurrentNodeVersion": cluster.current_node_version,
                    "ReleaseChannel": None,
                    "MaintenanceWindow": None,
                    "CurrentNodeCount": cluster.current_node_count,
                    "DefaultMaxPodsConstraint": None,
                    "AddonsConfig": {}, # Store addons config as dict
                    "InstanceGroupUrls": [get_resource_name(url) for url in cluster.instance_group_urls],
                    "BinaryAuthorization": None,
                    "ShieldedNodes": None,
                    "NodePools": [], # Store node pool details as a list of dicts
                    # "PersistentDisks": None, # Omitted due to external dependency in PS script
                    "service": self.SERVICE_NAME
                }

                # Private Cluster Config
                if cluster.private_cluster_config:
                    pcc = cluster.private_cluster_config
                    info["EnablePrivateNodes"] = pcc.enable_private_nodes
                    info["MasterIpv4CidrBlock"] = pcc.master_ipv4_cidr_block
                    info["PrivateEndpoint"] = pcc.private_endpoint

                # Release Channel
                if cluster.release_channel:
                    info["ReleaseChannel"] = container_v1.types.ReleaseChannel.Channel(cluster.release_channel.channel).name

                # Maintenance Window
                if cluster.maintenance_policy and cluster.maintenance_policy.window:
                     info["MaintenanceWindow"] = self._format_maintenance_window(cluster.maintenance_policy.window)

                # Default Max Pods Constraint
                if cluster.default_max_pods_constraint:
                    info["DefaultMaxPodsConstraint"] = cluster.default_max_pods_constraint.max_pods_per_node

                # Addons Config - Store enabled/disabled status
                if cluster.addons_config:
                    ac = cluster.addons_config
                    # Iterate through known addon configs and check if they exist/are disabled
                    # This provides a structured way instead of just joining names/values
                    info["AddonsConfig"] = {
                        "http_load_balancing_disabled": ac.http_load_balancing.disabled if ac.http_load_balancing else None,
                        "horizontal_pod_autoscaling_disabled": ac.horizontal_pod_autoscaling.disabled if ac.horizontal_pod_autoscaling else None,
                        "network_policy_config_disabled": ac.network_policy_config.disabled if ac.network_policy_config else None,
                        # Add other addons as needed (e.g., dns_cache_config, gce_persistent_disk_csi_driver_config)
                        "cloud_run_config_disabled": ac.cloud_run_config.disabled if ac.cloud_run_config else None,
                        "gcp_filestore_csi_driver_config_disabled": ac.gcp_filestore_csi_driver_config.enabled is False if ac.gcp_filestore_csi_driver_config else None, # Note: uses 'enabled' flag
                        "gke_backup_agent_config_enabled": ac.gke_backup_agent_config.enabled if ac.gke_backup_agent_config else None, # Note: uses 'enabled' flag
                    }


                # Security Features
                if cluster.binary_authorization:
                    info["BinaryAuthorization"] = cluster.binary_authorization.enabled
                if cluster.shielded_nodes:
                    info["ShieldedNodes"] = cluster.shielded_nodes.enabled

                # Node Pools
                if cluster.node_pools:
                    for np in cluster.node_pools:
                        np_info = {
                            "NodePoolName": np.name,
                            "NodePoolAutoscaling": None,
                            "NodePoolMinNodes": None,
                            "NodePoolMaxNodes": None,
                            "NodePoolDiskSize": None,
                            "NodePoolDiskType": None,
                            "NodePoolMachineType": None,
                            "NodePoolImage": None,
                            "NodePoolInitialNodecount": np.initial_node_count,
                            "NodePoolIG": [get_resource_name(url) for url in np.instance_group_urls],
                            "NodePoolAutoRepair": None,
                            "NodePoolAutoUpgrade": None,
                            "NodePoolPodCIDRSize": np.pod_ipv4_cidr_size,
                            "NodePoolStatus": container_v1.types.NodePool.Status(np.status).name,
                            "NodepoolVersion": np.version,
                            "NodePoolTags": [],
                            "NodePoolTaints": [] # Store taints as list of dicts
                        }

                        if np.autoscaling:
                            np_info["NodePoolAutoscaling"] = np.autoscaling.enabled
                            np_info["NodePoolMinNodes"] = np.autoscaling.min_node_count
                            np_info["NodePoolMaxNodes"] = np.autoscaling.max_node_count

                        if np.config:
                            npc = np.config
                            np_info["NodePoolDiskSize"] = npc.disk_size_gb
                            np_info["NodePoolDiskType"] = npc.disk_type
                            np_info["NodePoolMachineType"] = npc.machine_type
                            np_info["NodePoolImage"] = npc.image_type
                            if npc.tags:
                                np_info["NodePoolTags"] = list(npc.tags)
                            if npc.taints:
                                np_info["NodePoolTaints"] = [
                                    {"key": taint.key, "value": taint.value, "effect": container_v1.types.NodeTaint.Effect(taint.effect).name}
                                    for taint in npc.taints
                                ]

                        if np.management:
                            np_info["NodePoolAutoRepair"] = np.management.auto_repair
                            np_info["NodePoolAutoUpgrade"] = np.management.auto_upgrade

                        info["NodePools"].append(np_info)

                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing clusters or accessing GKE API. Ensure 'Kubernetes Engine API' is enabled and the service account has necessary roles (e.g., 'Kubernetes Engine Viewer'). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] GKE API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process GKE clusters: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            # Close the client
            if client:
                client.transport.close()


        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished GKE cluster fetch. Found {len(inventory)} clusters.")
        return inventory
