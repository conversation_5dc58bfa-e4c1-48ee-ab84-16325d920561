# --- File: gcp_inventory_tool/fetchers/app_engine_firewall.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import appengine_admin_v1
from google.api_core import exceptions as api_exceptions

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger(__name__)

class AppEngineFirewallFetcher(ServiceFetcher):
    """
    Fetches Google App Engine firewall ingress rules for a project.
    """
    SERVICE_NAME = "app_engine_firewall" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all App Engine firewall rules in the project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting App Engine firewall rule fetch...")
        inventory = []
        client = appengine_admin_v1.FirewallClient(credentials=credentials)
        # Parent format for App Engine Admin API calls
        parent = f"apps/{project_id}"

        try:
            # List all firewall ingress rules
            rules = client.list_ingress_rules(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing firewall rules...")

            for rule in rules:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Priority": rule.priority,
                    "Action": str(rule.action), # Get enum name (ALLOW, DENY)
                    "SourceRange": rule.source_range,
                    "Description": rule.description, # Added description field
                    "service": self.SERVICE_NAME
                }
                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing firewall rules or accessing App Engine Admin API. Ensure API is enabled and necessary roles granted. Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] App Engine application might not exist in this project or API not enabled. Details: {e}")
             return [] # App not found or API issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process App Engine firewall rules: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            # Ensure the client is closed
            if client:
                client.transport.close()

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished App Engine firewall rule fetch. Found {len(inventory)} rules.")
        return inventory
