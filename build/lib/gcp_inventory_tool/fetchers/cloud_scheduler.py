# --- File: gcp_inventory_tool/fetchers/cloud_scheduler_job.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import scheduler_v1
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso
from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, job name from a full Scheduler resource name
def _parse_scheduler_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and job name from a Scheduler resource name."""
    # Format: projects/{project}/locations/{location}/jobs/{job_name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'jobs':
            project_id = parts[1]
            location_id = parts[3]
            job_name = parts[5]
            return project_id, location_id, job_name
        else:
            logger.warning(f"Unrecognized Scheduler job name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing Scheduler job name '{name}': {e}")
        return None

class CloudSchedulerJobFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Scheduler job details for a project.
    Lists jobs across all locations.
    """
    SERVICE_NAME = "cloud_scheduler_job" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud Scheduler jobs in the specified project across all locations.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud Scheduler job fetch...")
        inventory = []
        client = scheduler_v1.CloudSchedulerClient(credentials=credentials)
        # Parent path to list jobs across all locations
        parent = f"projects/{project_id}/locations/-"

        try:
            jobs = client.list_jobs(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Cloud Scheduler jobs across locations...")

            for job in jobs:
                parsed_name_parts = _parse_scheduler_name(job.name)
                location = parsed_name_parts[1] if parsed_name_parts else "Unknown"
                short_name = parsed_name_parts[2] if parsed_name_parts else job.name # Fallback

                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": short_name,
                    "Description": job.description,
                    "LastAttemptTime": timestamp_to_iso(job.status.last_attempt_time) if job.status else None,
                    "UserUpdateTime": timestamp_to_iso(job.user_update_time),
                    "State": str(job.state), # ENABLED, PAUSED, DISABLED, UPDATE_FAILED
                    "Location": location,
                    "Timezone": job.time_zone,
                    "Schedule": job.schedule,
                    "TargetType": None, # Populated below
                    "PubSubTargetTopic": None, # Populated below
                    "HttpTargetUri": None, # Populated below
                    "HttpMethod": None, # Populated below
                    "AppEngineTargetService": None, # Populated below
                    "AppEngineTargetVersion": None, # Populated below
                    "AppEngineRouting": None, # Populated below
                    "AttemptDeadline": str(job.attempt_deadline) if job.attempt_deadline else None, # Added field
                    "RetryMaxAttempts": job.retry_config.max_retry_duration.seconds if job.retry_config and job.retry_config.max_retry_duration else None, # Added field (approx)
                    "RetryMinBackoff": str(job.retry_config.min_backoff_duration) if job.retry_config else None, # Added field
                    "RetryMaxBackoff": str(job.retry_config.max_backoff_duration) if job.retry_config else None, # Added field
                    "RetryMaxDoublings": job.retry_config.max_doublings if job.retry_config else None, # Added field
                    "service": self.SERVICE_NAME
                }

                # Determine Target Type and details
                if job.pubsub_target:
                    info["TargetType"] = "PUBSUB"
                    info["PubSubTargetTopic"] = get_resource_name(job.pubsub_target.topic_name)
                elif job.app_engine_http_target:
                    info["TargetType"] = "APP_ENGINE_HTTP"
                    info["HttpMethod"] = str(job.app_engine_http_target.http_method)
                    if job.app_engine_http_target.app_engine_routing:
                         ae_routing = job.app_engine_http_target.app_engine_routing
                         info["AppEngineTargetService"] = ae_routing.service
                         info["AppEngineTargetVersion"] = ae_routing.version
                         # Instance omitted for brevity, add if needed: ae_routing.instance
                         info["AppEngineRouting"] = f"service={ae_routing.service}, version={ae_routing.version}"
                elif job.http_target:
                     info["TargetType"] = "HTTP"
                     info["HttpTargetUri"] = job.http_target.uri
                     info["HttpMethod"] = str(job.http_target.http_method)
                     # Add oauth/oidc token info if needed

                inventory.append(info)

        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud Scheduler API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Cloud Scheduler jobs. Ensure API is enabled and necessary roles granted (e.g., 'Cloud Scheduler Job Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Cloud Scheduler jobs: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud Scheduler job fetch. Found {len(inventory)} jobs.")
        return inventory
