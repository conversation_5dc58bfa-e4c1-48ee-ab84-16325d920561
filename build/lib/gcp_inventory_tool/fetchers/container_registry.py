# --- File: gcp_inventory_tool/fetchers/container_registry_repo.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import artifactregistry_v1
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Standard GCR hostnames - add others if needed (e.g., asia.gcr.io)
GCR_HOSTNAMES = ["gcr.io", "us.gcr.io", "eu.gcr.io", "asia.gcr.io"]

# Helper function to parse project, location, repo name from a full Artifact Registry resource name
def _parse_artifact_repo_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and repository name from an Artifact Registry resource name."""
    # Format: projects/{project}/locations/{location}/repositories/{repo_name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'repositories':
            project_id = parts[1]
            location_id = parts[3]
            repo_name = parts[5]
            return project_id, location_id, repo_name
        else:
            logger.warning(f"Unrecognized Artifact Registry repository name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing Artifact Registry repository name '{name}': {e}")
        return None


class ContainerRegistryRepositoryFetcher(ServiceFetcher):
    """
    Fetches Google Container Registry (GCR) repositories for a project.
    Approximates this by listing Artifact Registry Docker repositories hosted
    on standard GCR domains (gcr.io, us.gcr.io, etc.).
    """
    SERVICE_NAME = "container_registry_repo" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for GCR-hosted Docker repositories in Artifact Registry.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Container Registry (via Artifact Registry) fetch...")
        inventory = []
        # Use the Artifact Registry client
        client = artifactregistry_v1.ArtifactRegistryClient(credentials=credentials)
        parent = f"projects/{project_id}/locations/-"

        try:
            repositories = client.list_repositories(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Artifact Registry repositories to find GCR hosts...")

            for repo in repositories:
                # Filter 1: Must be Docker format
                if repo.format_ != artifactregistry_v1.Repository.Format.DOCKER:
                    continue

                # Filter 2: Check if the name indicates it's hosted on a GCR domain
                # The full repo name is like: projects/proj/locations/loc/repositories/gcr.io-proj-id
                # The actual hostname is often embedded in the repo name itself for gcr.io domains in AR.
                parsed_name_parts = _parse_artifact_repo_name(repo.name)
                if not parsed_name_parts:
                    continue
                _, location, short_name = parsed_name_parts

                hostname = None
                repo_suffix = None # Part after hostname/project

                # Try to match known GCR hostnames within the AR repo name
                for gcr_host in GCR_HOSTNAMES:
                    # AR often names gcr.io repos like 'gcr.io-projectid' or 'us.gcr.io-projectid'
                    # Or sometimes the repo name directly reflects the path under the host
                    # This logic is heuristic and might need adjustment based on actual naming patterns observed
                    if short_name.startswith(gcr_host.replace('.', '-') + '-'): # e.g., gcr-io-my-project
                         hostname = gcr_host
                         repo_suffix = project_id # Usually the project ID follows in the name
                         break
                    # Add more pattern checks if needed based on your AR naming conventions for GCR repos

                # If no specific pattern matched, but it's Docker, maybe report it anyway?
                # For now, only report if we identified a GCR hostname pattern.
                if hostname:
                    # Prepare info dictionary based on PowerShell script's fields
                    info = {
                        "ProjectID": project_id,
                        "Name": repo_suffix or short_name, # Use suffix if found, else short AR name
                        "Hostname": hostname,
                        "ArtifactRegistryName": short_name, # Keep original AR name
                        "Location": location,
                        "CreationDate": timestamp_to_iso(repo.create_time),
                        "UpdationDate": timestamp_to_iso(repo.update_time),
                        "Labels": dict(repo.labels) if repo.labels else {},
                        "service": self.SERVICE_NAME
                    }
                    inventory.append(info)
                # else:
                    # logger.debug(f"Skipping AR Docker repo '{short_name}' as it doesn't match known GCR hostname patterns.")


        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Artifact Registry API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Artifact Registry repositories. Ensure API is enabled and necessary roles granted (e.g., 'Artifact Registry Reader'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Artifact Registry repositories for GCR check: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Container Registry (via AR) fetch. Found {len(inventory)} potential GCR repositories.")
        return inventory
