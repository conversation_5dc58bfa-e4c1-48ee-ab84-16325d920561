# --- File: gcp_inventory_tool/fetchers/vpc_connector.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import vpcaccess_v1
from google.api_core import exceptions as api_exceptions

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, name from a full connector name
def _parse_connector_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and name from a connector resource name."""
    # Format: projects/{project}/locations/{location}/connectors/{name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'connectors':
            project_id = parts[1]
            location_id = parts[3]
            connector_name = parts[5]
            return project_id, location_id, connector_name
        else:
            logger.warning(f"Unrecognized connector name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing connector name '{name}': {e}")
        return None

class VpcConnectorFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Serverless VPC Access Connector details for a project.
    """
    SERVICE_NAME = "vpc_connector" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Serverless VPC Access Connectors in the specified project across all regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting VPC Access Connector fetch...")
        inventory = []
        client = vpcaccess_v1.VpcAccessServiceClient(credentials=credentials)
        # Parent path to list connectors across all locations for the project
        parent = f"projects/{project_id}/locations/-"

        try:
            connectors = client.list_connectors(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing connectors across regions...")

            for connector in connectors:
                parsed_name_parts = _parse_connector_name(connector.name)
                location = parsed_name_parts[1] if parsed_name_parts else "Unknown"
                short_name = parsed_name_parts[2] if parsed_name_parts else connector.name # Fallback

                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": short_name,
                    "Network": connector.network,
                    "State": str(connector.state), # Get enum name
                    "IpCidrRange": connector.ip_cidr_range,
                    "Subnet": connector.subnet.name if connector.subnet else None, # Subnet name only
                    "MachineType": connector.machine_type,
                    "MaxInstances": connector.max_instances,
                    "MinInstances": connector.min_instances,
                    "MaxThroughput": connector.max_throughput,
                    "MinThroughput": connector.min_throughput,
                    "Location": location,
                    "ConnectedProjects": list(connector.connected_projects) if connector.connected_projects else [], # Added field
                    "service": self.SERVICE_NAME
                }
                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing VPC Access Connectors. Ensure 'VPC Access API' is enabled and necessary roles granted (e.g., 'Compute Network Viewer', 'vpcaccess.connectors.list' permission). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled or maybe no connectors exist,
             # though list usually returns empty in the latter case.
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] VPC Access API might not be enabled or project not found. Details: {e}")
             return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process VPC Access Connectors: {e}", exc_info=True)
            return [] # Fail gracefully for other errors
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished VPC Access Connector fetch. Found {len(inventory)} connectors.")
        return inventory
