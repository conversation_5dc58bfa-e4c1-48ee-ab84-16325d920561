# --- File: gcp_inventory_tool/fetchers/vpc_network.py ---
import logging
from typing import List, Dict, Any, Optional
from collections import defaultdict
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


# Helper function to extract region from subnet URL
def _get_region_from_subnet_url(url: Optional[str]) -> Optional[str]:
    """Extracts the region name from a subnet URL."""
    if not url or 'regions' not in url:
        return None
    try:
        parts = url.split('/')
        region_index = parts.index('regions') + 1
        return parts[region_index]
    except (ValueError, IndexError):
        logger.warning(f"Could not parse region from subnet URL: {url}")
        return None


class VPCNetworkFetcher(ServiceFetcher):
    """
    Fetches Google Cloud VPC Network details for a project, including peering info derived from Addresses.
    """
    SERVICE_NAME = "vpc_network" # Unique key for this service type

    def _get_peering_addresses(self, client: compute_v1.AddressesClient, project_id: str) -> Dict[str, List[Dict[str, Any]]]:
        """Fetches all addresses and filters for VPC_PEERING purpose, indexed by network self_link."""
        peering_addresses_by_network = defaultdict(list)
        try:
            # Use aggregated list to get addresses from all regions + global
            agg_addresses = client.aggregated_list(project=project_id)
            for region, response in agg_addresses:
                if response.addresses:
                    for address in response.addresses:
                        # Check if it's for VPC peering and associated with a network
                        if address.purpose == compute_v1.Address.Purpose.VPC_PEERING and address.network:
                            addr_info = {
                                "name": address.name,
                                "address": address.address,
                                "prefix_length": address.prefix_length,
                                "network": address.network # Keep the full network link for matching
                            }
                            peering_addresses_by_network[address.network].append(addr_info)
                            logger.debug(f"Found VPC_PEERING address '{address.name}' for network '{address.network}'")

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing addresses: {e}")
            # Continue without address info, peering details might be incomplete
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list addresses: {e}", exc_info=True)
            # Continue without address info

        return peering_addresses_by_network


    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all VPC Networks in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting VPC Network fetch...")
        inventory = []
        network_client = compute_v1.NetworksClient(credentials=credentials)
        address_client = compute_v1.AddressesClient(credentials=credentials) # Need this for peering info

        # --- Step 1: Fetch VPC Peering Addresses ---
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Fetching addresses for peering lookup...")
        peering_addresses = self._get_peering_addresses(address_client, project_id)
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished fetching addresses.")

        # --- Step 2: Fetch and Process Networks ---
        try:
            networks = network_client.list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing networks...")

            for network in networks:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": network.name,
                    "CreationDate": network.creation_timestamp,
                    "Description": network.description,
                    "Subnets": [], # List of subnet names
                    "Regions": [], # List of unique regions derived from subnets
                    "ServiceNetworking": None, # CIDR populated from peering address
                    "NetApp": None, # CIDR populated from peering address
                    "VPCPeerings": [], # List of other peering network names
                    "Id": str(network.id), # Added ID
                    "SelfLink": network.self_link, # Added self_link for matching addresses
                    "AutoCreateSubnetworks": network.auto_create_subnetworks, # Added field
                    "RoutingMode": str(network.routing_config.routing_mode) if network.routing_config else None, # Added field
                    "service": self.SERVICE_NAME
                }

                # Process Subnets and Regions
                unique_regions = set()
                if network.subnetworks:
                    for subnet_url in network.subnetworks:
                        subnet_name = get_resource_name(subnet_url)
                        region_name = _get_region_from_subnet_url(subnet_url)
                        if subnet_name:
                            info["Subnets"].append(subnet_name)
                        if region_name:
                            unique_regions.add(region_name)
                info["Regions"] = sorted(list(unique_regions))

                # Process Peerings
                if network.peerings:
                    for peering in network.peerings:
                        peering_network_name = get_resource_name(peering.network)
                        # Check for specific peering types based on name (as in PS script)
                        # but use address lookup for CIDR
                        is_service_networking = "servicenetworking" in peering_network_name.lower() if peering_network_name else False
                        is_netapp = "netapp-tenant-vpc" in peering_network_name.lower() if peering_network_name else False

                        if is_service_networking or is_netapp:
                            # Look for a matching address in the pre-fetched list
                            # Match based on the network self_link
                            matched_addr = None
                            if network.self_link in peering_addresses:
                                # Find the first address associated with this network
                                # (assuming one peering address per type per network)
                                # Refinement: PS script used name filters ('gcp-01', 'netapp').
                                # This version relies solely on purpose=VPC_PEERING and network match.
                                # If more specific matching is needed, address names could be checked here.
                                if peering_addresses[network.self_link]:
                                     matched_addr = peering_addresses[network.self_link][0] # Take the first match

                            if matched_addr:
                                cidr = f"{matched_addr['address']}/{matched_addr['prefix_length']}"
                                if is_service_networking:
                                    info["ServiceNetworking"] = cidr
                                    logger.debug(f"Found ServiceNetworking CIDR '{cidr}' for VPC '{network.name}' via address '{matched_addr['name']}'")
                                elif is_netapp:
                                    info["NetApp"] = cidr
                                    logger.debug(f"Found NetApp CIDR '{cidr}' for VPC '{network.name}' via address '{matched_addr['name']}'")
                            else:
                                logger.warning(f"Could not find matching VPC_PEERING address for specific peering '{peering.name}' in VPC '{network.name}'")
                                # Set field to indicate presence but missing CIDR?
                                if is_service_networking: info["ServiceNetworking"] = "Present (CIDR not found)"
                                if is_netapp: info["NetApp"] = "Present (CIDR not found)"

                        else:
                            # Add other peerings by name
                            if peering_network_name:
                                info["VPCPeerings"].append(peering_network_name)

                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing networks or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            # Addresses might have been fetched, but network list failed
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process VPC Networks: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            # Ensure clients are closed
            if network_client:
                network_client.transport.close()
            if address_client:
                address_client.transport.close()

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished VPC Network fetch. Found {len(inventory)} networks.")
        return inventory
