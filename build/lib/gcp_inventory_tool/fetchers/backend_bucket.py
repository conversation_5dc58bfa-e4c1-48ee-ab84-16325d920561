# --- File: gcp_inventory_tool/fetchers/backend_bucket.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger(__name__)


class BackendBucketFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Backend Bucket details for a project.
    Backend Buckets are global resources.
    """
    SERVICE_NAME = "backend_bucket" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Backend Buckets in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Backend Bucket fetch...")
        inventory = []
        client = compute_v1.BackendBucketsClient(credentials=credentials)

        try:
            # List all backend buckets in the project (global resource)
            buckets = client.list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing backend buckets...")

            for bucket in buckets:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": bucket.name,
                    "BucketName": bucket.bucket_name,
                    "Description": bucket.description,
                    "CreationTimestamp": bucket.creation_timestamp,
                    "Location": "global", # Backend buckets are global
                    "EnableCDN": bucket.enable_cdn,
                    # CDN Policy fields
                    "CacheMode": None,
                    "ClientTtl": None,
                    "DefaultTtl": None,
                    "MaxTtl": None,
                    "NegativeCaching": None,
                    "NegativeCachingPolicy": [], # List of dicts {code, ttl}
                    "RequestCoalescing": None,
                    "ServeWhileStale": None,
                    "SignedUrlCacheMaxAgeSec": None,
                    # Other fields
                    "CustomResponseHeaders": list(bucket.custom_response_headers) if bucket.custom_response_headers else [], # Added
                    "Id": str(bucket.id), # Added
                    "service": self.SERVICE_NAME
                }

                # Populate CDN Policy details if they exist
                if bucket.cdn_policy:
                    cdn = bucket.cdn_policy
                    info["CacheMode"] = str(cdn.cache_mode)
                    info["ClientTtl"] = cdn.client_ttl
                    info["DefaultTtl"] = cdn.default_ttl
                    info["MaxTtl"] = cdn.max_ttl
                    info["NegativeCaching"] = cdn.negative_caching
                    if cdn.negative_caching_policy:
                         info["NegativeCachingPolicy"] = [{"code": p.code, "ttl": p.ttl} for p in cdn.negative_caching_policy]
                    info["RequestCoalescing"] = cdn.request_coalescing
                    info["ServeWhileStale"] = cdn.serve_while_stale
                    info["SignedUrlCacheMaxAgeSec"] = cdn.signed_url_cache_max_age_sec
                    # Add other CDN policy fields if needed (e.g., cache_key_policy)

                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing backend buckets or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Backend Buckets: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            # Ensure the client is closed
            if client:
                client.transport.close()

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Backend Bucket fetch. Found {len(inventory)} buckets.")
        return inventory
