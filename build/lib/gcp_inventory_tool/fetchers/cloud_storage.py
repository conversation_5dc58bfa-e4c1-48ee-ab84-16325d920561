import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import storage
from google.api_core import exceptions as api_exceptions
import datetime # Import datetime for timezone handling

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

class CloudStorageFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Storage bucket inventory details for a project.
    Maps details similarly to the provided PowerShell script's output.
    """
    SERVICE_NAME = "storage" # Must match the key in config.yaml

    def _format_datetime(self, dt: Optional[datetime.datetime]) -> Optional[str]:
        """Helper to format datetime objects consistently, handling None."""
        if dt:
            # Ensure the datetime is timezone-aware (client library usually provides UTC)
            if dt.tzinfo is None:
                # Assume UTC if timezone is missing (adjust if necessary)
                dt = dt.replace(tzinfo=datetime.timezone.utc)
            # Format as ISO 8601 string
            return dt.isoformat()
        return None

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud Storage buckets in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud Storage bucket fetch...")
        inventory = []
        try:
            # Instantiate the client, explicitly passing the project and credentials
            storage_client = storage.Client(project=project_id, credentials=credentials)

            # List buckets for the specified project. Handles pagination automatically.
            buckets = storage_client.list_buckets()

            for bucket in buckets:
                # Reload bucket metadata to ensure all attributes are populated
                # Some attributes might not be present in the initial list response.
                # print(bucket.__dict__)
                try:
                    bucket.reload()
                except Exception as reload_err:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Failed to reload metadata for bucket '{bucket.name}': {reload_err}. Some details might be missing.")
                    # Continue with potentially partial data from list_buckets

                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id, # Use the project_id passed to the function
                    "Name": bucket.name,
                    "LocationType": None,
                    "CreationDate": None,
                    "UpdationDate": None,
                    "StorageClass": None,
                    "BucketPolicyOnlyEnabled": None,
                    "Logging": None, # Store the logging config dict or None
                    "CORS": None, # Store the CORS list or None
                    "PublicAccessPrevention": None,
                    "WebsiteConfiguration": None, # Store website config dict or None
                    "DefaultKMSKey": None,
                    "MetaGeneration": None,
                    "RequestorPaysEnabled": None,
                    "Location": None,
                    "VersioningEnabled": None,
                    "DefaultACL": "None", # Default based on PowerShell logic
                    "ACL": "None", # Default based on PowerShell logic
                    "Labels": {}, # Store as dict
                    "service": self.SERVICE_NAME
                }
                
                # Populate fields from the bucket object
                info["LocationType"] = bucket.location_type
                info["CreationDate"] = self._format_datetime(bucket.time_created)
                info["UpdationDate"] = self._format_datetime(bucket.updated)
                info["StorageClass"] = bucket.storage_class
                info["Location"] = bucket.location # Location constraint

                # IAM Configuration related fields
                if bucket.iam_configuration:
                    if bucket.iam_configuration['bucketPolicyOnly']:
                        info["BucketPolicyOnlyEnabled"] = bucket.iam_configuration['bucketPolicyOnly']['enabled']
                    info["PublicAccessPrevention"] = bucket.iam_configuration['publicAccessPrevention']

                # Other configurations
                extras = bucket.__dict__
                
                info["Logging"] = extras.get('logging') # Returns dict or None
                info["CORS"] = extras.get('cors') # Returns list or None
                info["WebsiteConfiguration"] = extras.get('website') # Returns dict or None
                info["DefaultKMSKey"] = extras.get('default_kms_key_name')
                info["MetaGeneration"] = extras.get('metageneration')
                info["RequestorPaysEnabled"] = extras.get('requester_pays')
                info["VersioningEnabled"] = extras.get('versioning_enabled')

                # Check for ACL presence (more robust than length check)
                # Note: Accessing ACLs might require additional permissions (storage.buckets.getIamPolicy)
                # Depending on permissions, these might remain None or raise an error during reload/access.
                try:
                    if bucket.default_object_acl and len(bucket.default_object_acl) > 0:
                         info["DefaultACL"] = "Present"
                    # Check bucket ACLs - bucket.acl might trigger a fetch if not already loaded
                    if bucket.acl and len(bucket.acl) > 0:
                         info["ACL"] = "Present"
                except api_exceptions.Forbidden:
                     logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Permission denied accessing ACLs for bucket '{bucket.name}'. Setting ACL status to 'Permission Denied'.")
                     info["DefaultACL"] = "Permission Denied"
                     info["ACL"] = "Permission Denied"
                except Exception as acl_err:
                     logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Error checking ACLs for bucket '{bucket.name}': {acl_err}")
                     # Keep default "None" or set to "Error"

                # Labels
                if bucket.labels:
                    info["Labels"] = dict(bucket.labels) # Store as dictionary

                inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing buckets or accessing Storage API. Ensure 'Cloud Storage API' is enabled and the service account has necessary roles (e.g., 'Viewer', 'Storage Object Viewer'). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud Storage API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to fetch Cloud Storage buckets: {e}", exc_info=True)
            return [] # Fail gracefully for this service

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud Storage bucket fetch. Found {len(inventory)} buckets.")
        return inventory
