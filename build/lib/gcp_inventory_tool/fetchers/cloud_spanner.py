# --- File: gcp_inventory_tool/fetchers/spanner_instance.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import spanner_admin_instance_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, config name from a full Spanner config name
def _parse_spanner_config_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and config name from a Spanner instance config name."""
    # Format: projects/{project}/instanceConfigs/{config_name} -> location is embedded in config name usually
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 4 and parts[0] == 'projects' and parts[2] == 'instanceConfigs':
            project_id = parts[1]
            config_name = parts[3]
            # Extract location heuristically from config name (e.g., regional-us-central1 -> us-central1)
            location = config_name.replace("regional-", "").replace("nam-", "").replace("eur-", "").replace("asia-", "") # Basic cleaning
            # Handle multi-region names if needed, e.g., nam3 -> multi-region
            if location.startswith("nam") or location.startswith("eur") or location.startswith("asia"):
                 location = f"multi-region-{location}" # Indicate multi-region
            return project_id, location, config_name
        else:
            logger.warning(f"Unrecognized Spanner config name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing Spanner config name '{name}': {e}")
        return None

class SpannerInstanceFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Spanner instance details for a project.
    """
    SERVICE_NAME = "spanner_instance" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Spanner instances in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Spanner instance fetch...")
        inventory = []
        client = spanner_admin_instance_v1.InstanceAdminClient(credentials=credentials)
        parent = f"projects/{project_id}"

        try:
            instances = client.list_instances(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Spanner instances...")

            for instance in instances:
                # Extract location from config name
                config_parts = _parse_spanner_config_name(instance.config)
                location = config_parts[1] if config_parts else "Unknown"

                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": instance.display_name, # User-defined display name
                    "InstanceId": instance.name.split('/')[-1], # Actual instance ID
                    "Location": location, # Parsed from config
                    "ConfigName": get_resource_name(instance.config), # Added field
                    "NodeCount": instance.node_count,
                    "ProcessingUnits": instance.processing_units,
                    "State": str(instance.state), # CREATING, READY
                    "Labels": dict(instance.labels) if instance.labels else {}, # Added field
                    "CreationTimestamp": timestamp_to_iso(instance.create_time), # Added field
                    "service": self.SERVICE_NAME
                }
                inventory.append(info)

        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Spanner API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Spanner instances. Ensure API is enabled and necessary roles granted (e.g., 'Cloud Spanner Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Spanner instances: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Spanner instance fetch. Found {len(inventory)} instances.")
        return inventory
