# --- File: gcp_inventory_tool/fetchers/forwarding_rule.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class ForwardingRuleFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Forwarding Rule details for a project.
    Covers both global and regional forwarding rules.
    """
    SERVICE_NAME = "forwarding_rule" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Forwarding Rules in the specified project across all regions and global.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Forwarding Rule fetch...")
        inventory = []
        client = compute_v1.ForwardingRulesClient(credentials=credentials)

        try:
            # Use aggregated list to get rules from all regions and global
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing forwarding rules across regions/global...")

            # The response is an iterator of tuples (scope, response_object)
            # Scope is like "regions/us-central1" or "global"
            for scope, response in agg_list:
                if response.forwarding_rules:
                    scope_name = get_resource_name(scope) # Region name or 'global'
                    is_global = "global" in scope.lower()
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.forwarding_rules)} rules in scope {scope_name}...")

                    for rule in response.forwarding_rules:
                        # Determine location based on scope or target URL as fallback
                        location = scope_name
                        if is_global and not location: # Should be 'global' from scope_name
                             location = "global"
                        elif not location and rule.target: # Fallback parsing target if region somehow missing
                             try:
                                 location = rule.target.split('/')[-3] if rule.target else "Unknown"
                             except IndexError:
                                 location = "Unknown"


                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": rule.name,
                            "Target": get_resource_name(rule.target),
                            "Description": rule.description,
                            "LoadBalancingScheme": str(rule.load_balancing_scheme),
                            "Location": location,
                            "Network": get_resource_name(rule.network),
                            "Subnetwork": get_resource_name(rule.subnetwork),
                            "IPProtocol": str(rule.ip_protocol),
                            "IPAddress": rule.ip_address,
                            "Ports": list(rule.ports) if rule.ports else [], # Return list
                            "PortRange": rule.port_range,
                            "BackendService": get_resource_name(rule.backend_service), # Specific to certain LB types
                            "CreationTimestamp": rule.creation_timestamp,
                            "Labels": dict(rule.labels) if rule.labels else {},
                            "Id": str(rule.id), # Added ID
                            "NetworkTier": str(rule.network_tier), # Added field
                            "IsMirroringCollector": rule.is_mirroring_collector, # Added field
                            "service": self.SERVICE_NAME
                        }
                        inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing forwarding rules or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Forwarding Rules: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Forwarding Rule fetch. Found {len(inventory)} rules.")
        return inventory
