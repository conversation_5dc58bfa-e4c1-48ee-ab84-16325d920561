# --- File: gcp_inventory_tool/fetchers/dataflow_job.py ---
import logging
from typing import List, Dict, Any
from google.oauth2.credentials import Credentials
from google.cloud import dataflow_v1beta3
from google.api_core import exceptions as api_exceptions

from ..utils.get_regions import get_available_regions
from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class DataflowJobFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Dataflow job details for a project.
    Lists jobs across dynamically fetched available regions.
    """
    SERVICE_NAME = "dataflow_job" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Dataflow jobs in the specified project across available regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Dataflow job fetch...")
        inventory = []
        # Use the v1beta3 client as often used by gcloud for jobs list
        client = None # Initialize client to None
        regions_to_check = [] # Initialize regions list

        try: # Outer try block starts here
            client = dataflow_v1beta3.JobsV1Beta3Client(credentials=credentials)

            # --- Dynamically fetch regions ---
            regions_to_check = get_available_regions(project_id, credentials)
            if not regions_to_check:
                 logger.warning(f"[{project_id}][{self.SERVICE_NAME}] No regions found or fetched. Skipping Dataflow job check.")
                 # Need to close client here if regions fail
                 if client:
                     client.transport.close()
                 return []
            # ---------------------------------


            for region in regions_to_check:
                # Indentation level 1 inside the try block
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Checking region {region} for Dataflow jobs...")
                try: # Inner try for processing a specific region
                    # List jobs for the current region
                    # View defaults to JOB_VIEW_SUMMARY, change if more detail needed from list
                    request = dataflow_v1beta3.ListJobsRequest(project_id=project_id, location=region)
                    # Set a reasonable page size
                    jobs = client.list_jobs(request=request, page_size=100)

                    for job in jobs:
                        # Indentation level 2 inside the try block
                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": job.project_id,
                            "Name": job.name, # User-specified job name
                            "JobID": job.id, # Unique Job ID
                            "Type": str(job.type_), # JOB_TYPE_STREAMING or JOB_TYPE_BATCH
                            "Location": job.location, # Region
                            "CreationDate": timestamp_to_iso(job.create_time),
                            "State": str(job.current_state), # JOB_STATE_RUNNING, DONE, FAILED etc.
                            "StateTime": timestamp_to_iso(job.current_state_time),
                            "ClientRequestId": job.client_request_id, # Added field
                            "CurrentWorkers": None, # Requires get_job_metrics call
                            "SDK": None, # Populated below
                            "Version": None, # Populated below
                            "service": self.SERVICE_NAME
                        }
                        # Note: Getting metrics like current workers might require a client.get_job_metrics call per job.
                        # Sticking to list_jobs results for efficiency based on PS script.

                        # Extract SDK info heuristically
                        if job.environment and job.environment.sdk_pipeline_options:
                             sdk_opts = job.environment.sdk_pipeline_options
                             # Convert Struct protobuf to dict for easier access
                             # Use the helper method provided by the library
                             sdk_opts_dict = dataflow_v1beta3.types.Struct.to_dict(sdk_opts)
                             info["SDK"] = sdk_opts_dict.get('googclient_language') # Common key

                        # Extract Version info
                        if job.environment:
                             # Version field is a Struct, access its fields if needed
                             # For simplicity, converting the whole version struct to string representation
                             info["Version"] = str(job.environment.version)


                        inventory.append(info)

                # Correctly indented except blocks for the inner try (region processing)
                except api_exceptions.NotFound as e:
                     logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Dataflow API/Region not found or enabled for region '{region}'. Details: {e}")
                except api_exceptions.Forbidden as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Dataflow jobs in region '{region}'. Details: {e}")
                except Exception as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Dataflow jobs in region '{region}': {e}", exc_info=True)
                # Continue to the next region even if one fails

        # Correctly indented except blocks for the outer try (initial setup/client creation)
        except Exception as e:
             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Unexpected error during Dataflow fetch setup or region iteration: {e}", exc_info=True)
             # No need to explicitly return here, finally block will execute
             # and function will return inventory (which might be empty)
        # Correctly indented finally block for the outer try
        finally:
            # Ensure the client is closed if it was successfully initialized
            if client:
                try:
                    client.transport.close()
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Dataflow client transport closed.")
                except Exception as close_e:
                     logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error closing Dataflow client transport: {close_e}")

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Dataflow job fetch. Found {len(inventory)} jobs across checked regions.")
        return inventory
