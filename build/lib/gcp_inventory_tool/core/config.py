import yaml
import os
import logging
from typing import Dict, List, Any, Optional

logger = logging.getLogger(__name__)

DEFAULT_CONFIG_FILENAME = "config.yaml"

def load_config(config_path: Optional[str] = None) -> Dict[str, Any]:
    """
    Loads configuration settings from a specified YAML file.

    Args:
        config_path: The path to the YAML configuration file. If None,
                     defaults to 'config.yaml' in the current working directory.

    Returns:
        A dictionary containing the loaded configuration. Returns an empty
        dictionary if the file is not found but doesn't raise an error
        (to allow CLI overrides).

    Raises:
        yaml.YAMLError: If the file contains invalid YAML syntax.
        ValueError: If the configuration has invalid structure (but not missing keys).
        TypeError: If the loaded config is not a dictionary.
    """
    resolved_path = config_path or DEFAULT_CONFIG_FILENAME
    logger.info(f"Attempting to load configuration from: {resolved_path}")

    if not os.path.exists(resolved_path):
        logger.warning(f"Configuration file not found: {resolved_path}. Proceeding with defaults/CLI args.")
        return {} # Return empty dict if file not found

    try:
        with open(resolved_path, 'r') as f:
            config_data = yaml.safe_load(f)
    except yaml.YAMLError as e:
        logger.error(f"Error parsing YAML configuration file {resolved_path}: {e}", exc_info=True)
        raise
    except IOError as e:
        logger.error(f"Error reading configuration file {resolved_path}: {e}", exc_info=True)
        raise IOError(f"Could not read configuration file: {resolved_path}") from e

    if config_data is None: # Handle empty config file
         logger.warning(f"Configuration file {resolved_path} is empty.")
         return {}

    if not isinstance(config_data, dict):
         logger.error(f"Invalid configuration format in {resolved_path}. Expected a dictionary (key-value pairs), got {type(config_data)}.")
         raise TypeError(f"Configuration file {resolved_path} must contain a dictionary.")

    # --- Basic Validation (Optional keys now) ---
    if 'projects' in config_data and (not isinstance(config_data['projects'], list) or not all(isinstance(p, str) for p in config_data['projects'])):
        raise ValueError(f"If 'projects' key exists, it must contain a list of strings (project IDs) in {resolved_path}")

    if 'services' in config_data and (not isinstance(config_data['services'], list) or not all(isinstance(s, str) for s in config_data['services'])):
        raise ValueError(f"If 'services' key exists, it must contain a list of strings (service names) in {resolved_path}")

    logger.info(f"Configuration loaded successfully from {resolved_path}.")
    return config_data