import logging
from typing import List, Dict, Optional, Any, Sequence # Added Sequence
from concurrent.futures import ThreadPoolExecutor, Future, as_completed
import time
import os # Import os for cpu_count

from . import credentials as cred_manager
from ..fetchers import get_fetcher
from ..formatters import base_formatter, get_formatter # Import get_formatter
# Import google auth exceptions for handling in init
import google.auth.exceptions

logger = logging.getLogger(__name__) # Get logger for this module

class InventoryGenerator:
    """
    Orchestrates the GCP resource inventory generation process.

    Loads configuration, manages credentials, discovers and runs service
    fetchers concurrently across specified projects, aggregates results,
    and handles output formatting internally based on initialization parameters.
    """

    def __init__(
        self,
        projects: Sequence[str],
        services: Sequence[str],
        max_workers: int,
        output_format: str,
        output_file: Optional[str] = None,
        format_options: Optional[Dict[str, Any]] = None,
        # config_dict: Dict[str, Any], # Keep loaded config if needed for other settings
        # Logging is assumed to be configured by the caller (cli.py)
    ):
        """
        Initializes the InventoryGenerator.

        Args:
            projects: List of project IDs to scan.
            services: List of service names to scan.
            max_workers: Maximum number of concurrent threads for fetching resources.
            output_format: The desired output format ('json', 'csv', 'excel', 'console').
            output_file: Path to the output file (required for file-based formats).
            format_options: Dictionary of options specific to the chosen formatter.
            # config_dict: Dictionary containing loaded configuration (for potential future use).
        """
        # Assume logging is already configured by the caller (cli.py)
        try:
            self.credentials = cred_manager.get_credentials() # Get credentials once
        except google.auth.exceptions.DefaultCredentialsError as e:
            logger.error(f"Failed to get credentials during InventoryGenerator initialization: {e}")
            raise RuntimeError(f"Credential initialization failed: {e}") from e

        self.projects = list(projects)
        self.services = list(services)
        self.max_workers = max(1, max_workers)
        self.output_format = output_format
        self.output_file = output_file
        self.format_options = format_options or {} # Ensure it's a dict

        # --- Validate inputs ---
        if not self.projects:
             logger.error("InventoryGenerator initialized with no target projects.")
             raise ValueError("No target projects specified.")
        if not self.services:
             logger.warning("InventoryGenerator initialized with no services. Inventory will be empty.")
        # Validate output file requirement for certain formats
        if self.output_format in ['csv', 'excel'] and not self.output_file:
             msg = f"Output format '{self.output_format}' requires an output file path."
             logger.error(msg)
             raise ValueError(msg)
        # ----------------------

        # --- Initialize Formatter ---
        FormatterClass = get_formatter(self.output_format)
        if not FormatterClass:
             msg = f"Unsupported output format specified: '{self.output_format}'."
             logger.error(msg)
             raise ValueError(msg)
        try:
            # Pass the combined output options (file path + specific format options)
            combined_output_options = {"file": self.output_file, **self.format_options}
            self.formatter = FormatterClass(output_options=combined_output_options)
        except ImportError as e: # Catch missing dependencies for formatter here
             logger.critical(f"Import Error initializing formatter '{self.output_format}': {e}")
             raise RuntimeError(f"Missing dependency for format '{self.output_format}'. Please install required libraries.") from e
        except Exception as e:
             logger.error(f"Failed to initialize formatter '{self.output_format}': {e}", exc_info=True)
             raise RuntimeError(f"Formatter initialization failed: {e}") from e
        # --------------------------

        logger.info(f"InventoryGenerator initialized for projects: {self.projects}")
        logger.info(f"InventoryGenerator initialized for services: {self.services}")
        logger.info(f"Using max_workers = {self.max_workers}")
        logger.info(f"Output format set to: {self.output_format}")
        if self.output_file:
             logger.info(f"Output file set to: {os.path.abspath(self.output_file)}")

        self.fetcher_classes = self._load_fetchers() # Load fetchers based on final self.services list


    def _load_fetchers(self) -> Dict[str, type]:
        """Loads and validates the fetcher classes specified in self.services."""
        loaded_fetchers = {}
        if not self.services:
             return {}

        logger.info(f"Attempting to load fetchers for services: {self.services}")
        for service_name in self.services:
            fetcher_cls = get_fetcher(service_name)
            if fetcher_cls:
                logger.debug(f"Successfully found fetcher class for service '{service_name}': {fetcher_cls.__name__}")
                loaded_fetchers[service_name] = fetcher_cls
            else:
                logger.warning(f"No registered fetcher found for service '{service_name}'. It will be skipped.")
        return loaded_fetchers

    def _run_fetcher_task(self, service_name: str, project_id: str) -> Optional[List[Dict[str, Any]]]:
        """Internal method to instantiate and run a single fetcher for a project."""
        fetcher_cls = self.fetcher_classes.get(service_name)
        if not fetcher_cls:
            logger.error(f"Fetcher class for '{service_name}' not found during task execution.")
            return None

        try:
            fetcher_instance = fetcher_cls()
            logger.info(f"Running fetcher '{fetcher_cls.__name__}' for project '{project_id}'...")
            start_time = time.monotonic()
            resources = fetcher_instance.fetch_resources(project_id, self.credentials)
            duration = time.monotonic() - start_time
            resource_count = len(resources) if resources is not None else 0
            logger.info(f"Fetcher '{fetcher_cls.__name__}' completed for project '{project_id}' in {duration:.2f}s. Found {resource_count} resources.")
            return resources
        except Exception as e:
            logger.error(f"Error running fetcher '{fetcher_cls.__name__}' for project '{project_id}': {e}", exc_info=True)
            return None

    def run_inventory(self):
        """
        Runs the inventory process concurrently across configured projects and services,
        and formats the output using the internally configured formatter.
        """
        all_resources: List[Dict[str, Any]] = []
        projects_to_scan = self.projects
        service_names_to_scan = list(self.fetcher_classes.keys())

        if not projects_to_scan or not service_names_to_scan:
            logger.warning("No projects or loadable services configured. Cannot run inventory.")
            # Format an empty list if needed by the formatter? Or just return.
            self.format_output([]) # Format empty list
            return

        logger.info(f"Starting inventory run for {len(projects_to_scan)} projects: {projects_to_scan}")
        logger.info(f"Fetching resources for {len(service_names_to_scan)} services: {service_names_to_scan}")
        total_tasks = len(projects_to_scan) * len(service_names_to_scan)
        logger.info(f"Submitting {total_tasks} fetch tasks to thread pool...")

        completed_tasks = 0
        start_time = time.monotonic()

        with ThreadPoolExecutor(max_workers=self.max_workers, thread_name_prefix='GCPInventoryFetcher') as executor:
            future_to_task: Dict[Future, str] = {}
            for project_id in projects_to_scan:
                for service_name in service_names_to_scan:
                    task_desc = f"{service_name} in {project_id}"
                    future = executor.submit(self._run_fetcher_task, service_name, project_id)
                    future_to_task[future] = task_desc

            for future in as_completed(future_to_task):
                task_desc = future_to_task[future]
                completed_tasks += 1
                try:
                    result = future.result()
                    if result is not None:
                        if isinstance(result, list):
                            all_resources.extend(result)
                            if logger.isEnabledFor(logging.DEBUG):
                                 logger.debug(f"Successfully processed task '{task_desc}'. Added {len(result)} resources.")
                        else:
                            logger.warning(f"Task '{task_desc}' returned an unexpected type: {type(result)}. Expected list or None.")
                    else:
                        logger.warning(f"Task '{task_desc}' failed (returned None). Check previous error logs.")
                except Exception as e:
                    logger.error(f"An unexpected error occurred retrieving result for task '{task_desc}': {e}", exc_info=True)

                if completed_tasks % 10 == 0 or completed_tasks == total_tasks:
                     progress_percent = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
                     logger.info(f"Inventory progress: {completed_tasks}/{total_tasks} tasks completed ({progress_percent:.0f}%).")

        end_time = time.monotonic()
        total_duration = end_time - start_time
        logger.info(f"Inventory generation finished in {total_duration:.2f} seconds.")
        logger.info(f"Total resources collected across all projects and services: {len(all_resources)}")

        # --- Format Output Internally ---
        self.format_output(all_resources)
        # -----------------------------

    def format_output(self, resources: List[Dict[str, Any]]):
        """Formats the collected resources using the internally configured formatter."""
        if not hasattr(self, 'formatter') or not isinstance(self.formatter, base_formatter.OutputFormatter):
            logger.error("Internal Formatter not initialized correctly. Cannot format output.")
            return

        logger.info(f"Formatting {len(resources)} resources using {self.formatter.__class__.__name__}...")
        try:
            self.formatter.format(resources) # Call format on the instance variable
            logger.info("Output formatting complete.")
        except Exception as e:
            logger.error(f"Error during output formatting using {self.formatter.__class__.__name__}: {e}", exc_info=True)

