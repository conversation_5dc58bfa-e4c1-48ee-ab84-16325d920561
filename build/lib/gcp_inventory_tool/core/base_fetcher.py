import abc
from google.oauth2.credentials import Credentials
from typing import List, Dict, Any

class ServiceFetcher(abc.ABC):
    """
    Abstract Base Class for all GCP service inventory fetchers.

    Each subclass should implement the `fetch_resources` method to retrieve
    inventory data for a specific GCP service (e.g., Compute Engine VMs,
    Cloud Storage Buckets).
    """
    # Unique identifier for the service this fetcher handles.
    # Should match the keys used in the configuration file's 'services' list.
    SERVICE_NAME = "base_service"

    def __init__(self):
        """Initialize the fetcher."""
        # Placeholder for potential common initialization logic
        pass

    @abc.abstractmethod
    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches resources for the specific GCP service within a given project.

        This method must be implemented by concrete subclasses.

        Args:
            project_id: The GCP project ID to scan.
            credentials: A google.oauth2.credentials.Credentials object
                         to authenticate API calls.

        Returns:
            A list of dictionaries. Each dictionary represents a discovered
            resource and should contain relevant properties. The structure
            can vary per service but should ideally include common fields
            like 'id', 'name', 'project_id', 'service', 'location', etc.
            Returns an empty list if no resources are found or if the
            relevant API is not enabled or accessible.

        Raises:
            Exception: Subclasses may raise exceptions for critical, unrecoverable
                       errors during fetching (though handling common API errors
                       like permissions or not found internally and returning []
                       is often preferred for robustness).
        """
        pass
