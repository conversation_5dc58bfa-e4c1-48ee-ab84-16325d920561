import csv
import logging
import os
import json # For serializing complex types
from typing import List, Dict, Any, Optional

from .base_formatter import OutputFormatter

logger = logging.getLogger('gcp_inventory')

class CsvFormatter(OutputFormatter):
    """Formats the inventory data as CSV output."""

    DEFAULT_DELIMITER = ','
    DEFAULT_LIST_SEPARATOR = '; ' # Separator for joining list items

    def _flatten_value(self, value: Any) -> str:
        """Converts complex types (lists, dicts) into string representations for CSV."""
        if isinstance(value, list):
            # Join list items with a separator
            # Convert each item to string first to handle non-string list items
            return self.DEFAULT_LIST_SEPARATOR.join(map(str, value))
        elif isinstance(value, dict):
            # Serialize dicts to JSON strings
            try:
                # Use compact JSON format
                return json.dumps(value, separators=(',', ':'), default=str)
            except TypeError:
                logger.warning(f"Could not JSON serialize dict for CSV: {value}. Using str().")
                return str(value) # Fallback to default string representation
        elif value is None:
            return "" # Represent None as empty string
        else:
            # Convert other types to string
            return str(value)


    def format(self, resources: List[Dict[str, Any]]):
        """
        Formats the resources list as CSV and writes it to a file.

        Requires 'file' option to be set in output_options.
        Determines header row based on the keys of the *first* resource,
        placing 'ProjectID' and 'Name' first if they exist.
        Flattens lists and dictionaries into string representations.

        Args:
            resources: The list of resource dictionaries.
        """
        output_file = self.options.get('file', None)
        delimiter = self.options.get('csv_delimiter', self.DEFAULT_DELIMITER)

        if not output_file:
            logger.error("CSV output requires an output file path. Please specify 'file' in output options or via --output-file.")
            return # Cannot print meaningful CSV to stdout easily

        if not resources:
            logger.warning("No resources provided to format as CSV.")
            # Create an empty file or a file with just headers? Let's skip for now.
            return

        logger.info(f"Formatting {len(resources)} resources as CSV.")
        logger.debug(f"CSV output target: {output_file}")
        logger.debug(f"CSV delimiter: '{delimiter}'")

        # --- Determine Header Row (based on the first resource) ---
        # Get keys from the first resource in their original order (Python 3.7+)
        first_resource_keys = list(resources[0].keys())

        # --- Define fixed starting columns ---
        fixed_headers = []
        remaining_keys = list(first_resource_keys) # Copy the list

        if 'ProjectID' in remaining_keys:
             fixed_headers.append('ProjectID')
             remaining_keys.remove('ProjectID')
        if 'Name' in remaining_keys:
             fixed_headers.append('Name')
             remaining_keys.remove('Name')
        # Add other fixed columns if needed, removing them from remaining_keys

        # Combine fixed headers and the rest of the keys from the first resource
        # The order of remaining_keys is preserved from the first dictionary.
        header = fixed_headers + remaining_keys
        # ----------------------------------------------------

        logger.debug(f"CSV header based on first resource: {header}")

        try:
            # Ensure directory exists
            output_dir = os.path.dirname(output_file)
            if output_dir: # Check if path includes a directory
                 os.makedirs(output_dir, exist_ok=True)

            with open(output_file, 'w', newline='', encoding='utf-8') as csvfile:
                # Use restval='' to handle missing keys gracefully in subsequent rows
                # Use fieldnames=header to enforce the desired order
                writer = csv.DictWriter(
                    csvfile,
                    fieldnames=header,
                    delimiter=delimiter,
                    quoting=csv.QUOTE_MINIMAL,
                    restval='', # Output empty string for keys missing in a specific row
                    extrasaction='ignore' # Ignore keys in data not present in header (shouldn't happen often with this logic)
                )

                writer.writeheader() # Write the header row in the determined order

                # Write data rows
                for resource in resources:
                    # Prepare row data, flattening complex types and handling missing keys
                    # Use get() with default None, then flatten handles None -> ""
                    row_data = {key: self._flatten_value(resource.get(key)) for key in header}
                    writer.writerow(row_data)

            logger.info(f"Inventory successfully written to CSV file: {output_file}")

        except IOError as e:
            logger.error(f"Failed to write CSV output to file {output_file}: {e}", exc_info=True)
        except Exception as e:
            logger.error(f"An unexpected error occurred during CSV formatting: {e}", exc_info=True)
