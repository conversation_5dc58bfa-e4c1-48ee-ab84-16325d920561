# --- File: gcp_inventory_tool/formatters/excel_formatter.py ---
import logging
import os
import json # For serializing complex types
from typing import List, Dict, Any, Optional
from collections import defaultdict
try:
    import pandas as pd
    PANDAS_AVAILABLE = True
except ImportError:
    PANDAS_AVAILABLE = False

from .base_formatter import OutputFormatter

logger = logging.getLogger(__name__)

class ExcelFormatter(OutputFormatter):
    """Formats the inventory data into an Excel file with one sheet per service."""

    DEFAULT_LIST_SEPARATOR = '; ' # Separator for joining list items

    def __init__(self, output_options: Optional[Dict[str, Any]] = None):
        """Initialize the Excel formatter and check for pandas dependency."""
        super().__init__(output_options)
        if not PANDAS_AVAILABLE:
            logger.error("The 'pandas' library is required for Excel output. Please install it (`pip install pandas openpyxl`).")
            # Raise an error or handle gracefully
            raise ImportError("The 'pandas' library is required for Excel output.")

    def _flatten_value_for_excel(self, value: Any) -> Any:
        """Converts complex types into string representations suitable for Excel."""
        if isinstance(value, list):
            # Join list items with a separator
            return self.DEFAULT_LIST_SEPARATOR.join(map(str, value))
        elif isinstance(value, dict):
            # Serialize dicts to JSON strings
            try:
                return json.dumps(value, separators=(',', ':'), default=str)
            except TypeError:
                logger.warning(f"Could not JSON serialize dict for Excel: {value}. Using str().")
                return str(value)
        # Keep None as None, pandas handles it well
        # Keep basic types (str, int, float, bool) as they are
        return value

    def _get_ordered_header(self, resources: List[Dict[str, Any]]) -> List[str]:
         """Determines header order based on the first resource, prioritizing ProjectID and Name."""
         if not resources:
             return []

         first_resource_keys = list(resources[0].keys())
         fixed_headers = []
         remaining_keys = list(first_resource_keys)

         if 'ProjectID' in remaining_keys:
              fixed_headers.append('ProjectID')
              remaining_keys.remove('ProjectID')

         name_key = None
         if 'Name' in remaining_keys:
             name_key = 'Name'
         else:
             common_name_keys = ['InstanceName', 'PolicyName', 'RuleName', 'BucketName', 'DisplayName', 'SubscriptionName', 'TopicName', 'JobName', 'ClusterName', 'ServiceName']
             for key in first_resource_keys:
                 if key in common_name_keys:
                     name_key = key
                     break
         if name_key and name_key in remaining_keys:
              fixed_headers.append(name_key)
              remaining_keys.remove(name_key)

         return fixed_headers + remaining_keys


    def format(self, resources: List[Dict[str, Any]]):
        """
        Formats the resources list into an Excel file with one sheet per service.

        Requires 'file' option to be set in output_options (must end in .xlsx).
        Groups resources by the 'service' key.

        Args:
            resources: The list of resource dictionaries. Each dict must have a 'service' key.
        """
        output_file = self.options.get('file', None)

        if not output_file:
            logger.error("Excel output requires an output file path. Please specify 'file' in output options or via --output-file.")
            return
        if not output_file.lower().endswith('.xlsx'):
             logger.error(f"Output file for Excel format must end with .xlsx. Provided: {output_file}")
             return

        if not resources:
            logger.warning("No resources provided to format as Excel.")
            # Create an empty Excel file? Let's skip for now.
            return

        logger.info(f"Formatting {len(resources)} resources into Excel file: {output_file}")

        # --- Group resources by service ---
        resources_by_service = defaultdict(list)
        for resource in resources:
            service_key = resource.get('service', 'UnknownService') # Group resources without a service key separately
            # Create a copy to avoid modifying the original dict when flattening
            processed_resource = {k: self._flatten_value_for_excel(v) for k, v in resource.items()}
            resources_by_service[service_key].append(processed_resource)
        # ---------------------------------

        try:
            # Ensure output directory exists
            output_dir = os.path.dirname(output_file)
            if output_dir:
                 os.makedirs(output_dir, exist_ok=True)

            # Create an Excel writer object using openpyxl engine
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                logger.debug(f"Writing {len(resources_by_service)} sheets to Excel file...")
                # Write each service group to a separate sheet
                for service_name, service_resources in resources_by_service.items():
                    if not service_resources:
                        logger.debug(f"Skipping empty sheet for service: {service_name}")
                        continue

                    logger.info(f"Writing sheet for service: {service_name} ({len(service_resources)} resources)")
                    # Determine header order for this specific sheet
                    header = self._get_ordered_header(service_resources)

                    # Create DataFrame with specified column order
                    # If a resource is missing a key from the header, it will get NaN
                    df = pd.DataFrame(service_resources, columns=header)

                    # Sanitize sheet name (Excel has limitations)
                    safe_sheet_name = service_name[:31] # Max length 31 chars
                    safe_sheet_name = safe_sheet_name.replace(':', '_').replace('\\', '_').replace('/', '_')
                    safe_sheet_name = safe_sheet_name.replace('?', '_').replace('*', '_').replace('[', '_').replace(']', '_')

                    # Write DataFrame to sheet
                    df.to_excel(writer, sheet_name=safe_sheet_name, index=False, na_rep='') # Write NaN as empty string

            logger.info(f"Inventory successfully written to Excel file: {output_file}")

        except ImportError:
             # This should be caught in __init__, but double-check
             logger.error("Pandas/Openpyxl library not found. Cannot write Excel file.")
        except Exception as e:
            logger.error(f"An unexpected error occurred during Excel formatting: {e}", exc_info=True)

