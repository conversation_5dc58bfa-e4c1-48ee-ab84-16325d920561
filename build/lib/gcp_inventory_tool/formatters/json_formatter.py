import json
import logging
import sys
from typing import List, Dict, Any, Optional

from .base_formatter import OutputFormatter

logger = logging.getLogger('gcp_inventory')

class JsonFormatter(OutputFormatter):
    """Formats the inventory data as JSON output."""

    DEFAULT_INDENT = 2

    def format(self, resources: List[Dict[str, Any]]):
        """
        Formats the resources list as JSON and outputs it.

        Output destination (file or stdout) and indentation are controlled
        by options passed during initialization (e.g., from config file).

        Args:
            resources: The list of resource dictionaries.
        """
        output_file = self.options.get('file', None) # Get 'file' from options dict
        indent = self.options.get('json_indent', self.DEFAULT_INDENT) # Get 'json_indent' or use default

        logger.info(f"Formatting {len(resources)} resources as JSON.")
        logger.debug(f"JSON output target: {'stdout' if output_file is None else output_file}")
        logger.debug(f"JSON indentation level: {indent}")

        try:
            # Use default=str to handle potential non-serializable types like datetime
            json_output = json.dumps(resources, indent=indent, default=str)

            if output_file:
                try:
                    with open(output_file, 'w', encoding='utf-8') as f:
                        f.write(json_output)
                    logger.info(f"Inventory successfully written to JSON file: {output_file}")
                except IOError as e:
                    logger.error(f"Failed to write JSON output to file {output_file}: {e}", exc_info=True)
                    # Optionally: Fallback to stdout or raise error
                    print("Error writing to file, printing to stdout instead:\n", json_output)
            else:
                # Print to standard output if no file specified
                print(json_output)
                logger.info("Inventory successfully printed to stdout as JSON.")

        except TypeError as e:
            logger.error(f"Error serializing inventory data to JSON: {e}. Check resource data types.", exc_info=True)
            # Potentially print problematic part of data if possible for debugging
        except Exception as e:
            logger.error(f"An unexpected error occurred during JSON formatting: {e}", exc_info=True)
