from typing import Optional
import logging
from google.protobuf import timestamp_pb2

logger = logging.getLogger('gcp_inventory')

def timestamp_to_iso(ts: Optional[timestamp_pb2.Timestamp]) -> Optional[str]:
     """Converts a Protobuf Timestamp to an ISO 8601 string."""
     if ts:
         try:
             # Convert datetime object first
             # Protobuf timestamps are timezone-aware (UTC)
             dt = ts.ToDatetime()
             return dt.isoformat()
         except Exception as e:
             logger.warning(f"Could not convert timestamp {ts} to ISO string: {e}")
             return str(ts) # Fallback
     return None