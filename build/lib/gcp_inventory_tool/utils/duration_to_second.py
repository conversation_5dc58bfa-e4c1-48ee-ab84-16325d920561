# Helper to convert protobuf Duration to seconds string
# (Can be shared or moved to a common utils module if used frequently)
from typing import Optional, Union
from google.protobuf import duration_pb2 # For handling duration fields
import datetime

def duration_to_seconds_str(duration: Optional[Union[duration_pb2.Duration, datetime.timedelta]]) -> Optional[str]:
    """Converts a Protobuf Duration object or datetime.timedelta to a string representing seconds."""
    if duration is None:
        return None

    # Handle datetime.timedelta objects
    if isinstance(duration, datetime.timedelta):
        return str(int(duration.total_seconds()))

    # Handle protobuf Duration objects
    if hasattr(duration, 'seconds') or hasattr(duration, 'nanos'):
        if duration.seconds or (hasattr(duration, 'nanos') and duration.nanos):
            return str(duration.seconds)

    return None