import logging
import sys
import os
from typing import Optional

# Default log format
DEFAULT_LOG_FORMAT = '%(asctime)s - %(levelname)s - %(name)s:%(filename)s - %(message)s'
DEBUG_LOG_FORMAT = '%(asctime)s - %(levelname)s - %(name)s - %(filename)s:%(lineno)d - %(message)s'

# Hardcoded logger name
LOGGER_NAME = "gcp_inventory"


def setup_logging(
    level=logging.INFO,
    log_format: Optional[str] = None,
    log_to_file: bool = False,
    log_file: str = "logs/app.log"
):
    """
    Configures a logger with a hardcoded name without affecting the root logger.

    This function sets up a logger that can be accessed in other files using
    logging.getLogger("gcp_inventory") without needing to import this module again.

    If log_to_file is True, logs will ONLY be written to the specified file.
    If log_to_file is False, logs will be written to the console.

    Args:
        level: The desired logging level (e.g., logging.DEBUG, logging.INFO).
        log_format: Optional custom log format string.
        log_to_file: Whether to log to a file instead of the console.
        log_file: Path to the log file if logging to file is enabled.

    Returns:
        The logger name that was configured, which can be used in other files.

    Example:
        # In your main application file:
        from gcp_inventory_tool.utils.logging_config import setup_logging
        setup_logging(log_to_file=True)

        # In any other file:
        import logging
        logger = logging.getLogger("gcp_inventory")
        logger.info("This is a log message")
    """
    chosen_format = log_format or (DEBUG_LOG_FORMAT if level == logging.DEBUG else DEFAULT_LOG_FORMAT)

    # Create formatter
    formatter = logging.Formatter(chosen_format)

    # Configure the logger with hardcoded name
    custom_logger = logging.getLogger(LOGGER_NAME)

    # Remove existing handlers from custom logger
    for handler in custom_logger.handlers[:]:
        custom_logger.removeHandler(handler)

    # Add handlers based on log_to_file setting
    if log_to_file:
        # If log_to_file is True, only log to file (not to console)
        if os.path.dirname(log_file):
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
        file_handler = logging.FileHandler(log_file, mode='w')  # 'w' = write mode
        file_handler.setFormatter(formatter)
        custom_logger.addHandler(file_handler)
    else:
        # If log_to_file is False, log to console
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setFormatter(formatter)
        custom_logger.addHandler(console_handler)

    # Set the custom logger level
    custom_logger.setLevel(level)

    # Make sure our custom logger doesn't propagate to avoid duplicate logs
    custom_logger.propagate = False

    # Silence noisy third-party loggers (unless DEBUG)
    if level > logging.DEBUG:
        # Silence commonly noisy loggers
        for noisy_logger in ['googleapiclient.discovery_cache', 'google.auth.transport.requests', 'urllib3.connectionpool']:
            logging.getLogger(noisy_logger).setLevel(logging.ERROR)

    custom_logger.info(f"Logging configured with name='{LOGGER_NAME}', level={logging.getLevelName(level)}")
    if log_to_file:
        custom_logger.info(f"File logging enabled: {os.path.abspath(log_file)}")
    else:
        custom_logger.info("File logging disabled")

    # Return the logger name so it can be used in other files with logging.getLogger(name)
    return LOGGER_NAME