2025-06-09 13:40:13,938 - INFO - gcp_inventory:logging_config.py - Logging configured with name='gcp_inventory', level=INFO
2025-06-09 13:40:13,939 - INFO - gcp_inventory:logging_config.py - File logging enabled: /app/examples/inventory_run.log
2025-06-09 13:40:13,939 - INFO - gcp_inventory:credentials.py - Attempting to get credentials using ADC with scopes: ['https://www.googleapis.com/auth/cloud-platform']
2025-06-09 13:40:14,943 - INFO - gcp_inventory:credentials.py - ADC did not detect a default project ID.
2025-06-09 13:40:14,943 - INFO - gcp_inventory:credentials.py - Successfully obtained credentials via ADC.
2025-06-09 13:40:14,943 - INFO - gcp_inventory:__init__.py - Dynamically loading service fetchers...
2025-06-09 13:40:18,060 - WARNING - gcp_inventory:__init__.py - Duplicate fetcher found for service 'vpc_connector'. Overwriting VpcConnectorFetcher with VpcConnectorFetcher.
2025-06-09 13:40:18,061 - INFO - gcp_inventory:__init__.py - Registered fetchers for services: 'address' (AddressFetcher),
        'alert_policy' (AlertPolicyFetcher),
        'app_engine_firewall' (AppEngineFirewallFetcher),
        'app_engine_service' (AppEngineServiceFetcher),
        'artifact_registry_repo' (ArtifactRegistryRepositoryFetcher),
        'backend_bucket' (BackendBucketFetcher),
        'backend_service' (BackendServiceFetcher),
        'bigtable' (BigtableFetcher),
        'classic_vpn_gateway' (ClassicVpnGatewayFetcher),
        'cloud_build_trigger' (CloudBuildTriggerFetcher),
        'cloud_function' (CloudFunctionFetcher),
        'cloud_router' (CloudRouterFetcher),
        'cloud_run_service' (CloudRunServiceFetcher),
        'cloud_scheduler_job' (CloudSchedulerJobFetcher),
        'spanner_instance' (SpannerInstanceFetcher),
        'cloud_sql' (CloudSqlInstanceFetcher),
        'storage' (CloudStorageFetcher),
        'composer_environment' (ComposerEnvironmentFetcher),
        'compute' (ComputeEngineFetcher),
        'container_registry_repo' (ContainerRegistryRepositoryFetcher),
        'data_fusion' (DataFusionFetcher),
        'vpc_connector' (VpcConnectorFetcher),
        'dataproc_cluster' (DataprocClusterFetcher),
        'dns_managed_zone' (DnsManagedZoneFetcher),
        'dns_policy' (DnsPolicyFetcher),
        'firewall_policy' (FirewallPolicyFetcher),
        'firewall_rule' (FirewallRuleFetcher),
        'forwarding_rule' (ForwardingRuleFetcher),
        'gke_cluster' (GKEClusterFetcher),
        'ha_vpn_gateway' (HaVpnGatewayFetcher),
        'health_check' (HealthCheckFetcher),
        'image' (ImageFetcher),
        'instance_group_manager' (InstanceGroupManagerFetcher),
        'instance_template' (InstanceTemplateFetcher),
        'interconnect_attachment' (InterconnectAttachmentFetcher),
        'kms_key' (KmsKeyFetcher),
        'log_sink' (LogSinkFetcher),
        'memcached' (MemcachedFetcher),
        'project_iam' (ProjectIAMFetcher),
        'redis' (RedisFetcher),
        'route' (RouteFetcher),
        'secret_manager' (SecretManagerFetcher),
        'security_policy' (SecurityPolicyFetcher),
        'service_account' (ServiceAccountFetcher),
        'ssl_certificate' (SslCertificateFetcher),
        'subnet' (SubnetFetcher),
        'pubsub' (PubSubFetcher),
        'pubsub_topic' (PubSubTopicFetcher),
        'vpc_network' (VPCNetworkFetcher),
        'vpc_connector' (VpcConnectorFetcher),
        'vpn_tunnel' (VpnTunnelFetcher)
2025-06-09 13:40:18,062 - INFO - gcp_inventory:compute_engine.py - [ce-ps3][compute] Starting detailed VM fetch...
2025-06-09 13:40:18,068 - INFO - gcp_inventory:cloud_storage.py - [ce-ps3][storage] Starting Cloud Storage bucket fetch...
2025-06-09 13:40:18,070 - INFO - gcp_inventory:subscriptions.py - [ce-ps3][pubsub] Starting Pub/Sub subscription fetch...
2025-06-09 13:40:18,074 - INFO - gcp_inventory:topics.py - [ce-ps3][pubsub_topic] Starting Pub/Sub topic fetch...
2025-06-09 13:40:18,086 - INFO - gcp_inventory:gke.py - [ce-ps3][gke_cluster] Starting GKE cluster fetch...
2025-06-09 13:40:18,087 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Starting Cloud Function fetch (V1 & V2)...
2025-06-09 13:40:18,087 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Fetching V1 functions...
2025-06-09 13:40:18,287 - INFO - gcp_inventory:app_engine_service.py - [ce-ps3][app_engine_service] Starting App Engine service/version fetch...
2025-06-09 13:40:18,635 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Starting VPC Network fetch...
2025-06-09 13:40:18,637 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Fetching addresses for peering lookup...
2025-06-09 13:40:18,702 - INFO - gcp_inventory:app_engine_service.py - [ce-ps3][app_engine_service] Finished App Engine service/version fetch. Found 0 versions across services.
2025-06-09 13:40:18,703 - INFO - gcp_inventory:subnet.py - [ce-ps3][subnet] Starting VPC Subnet fetch...
2025-06-09 13:40:18,886 - INFO - gcp_inventory:address.py - [ce-ps3][address] Starting Compute Address (PIP) fetch...
2025-06-09 13:40:19,062 - INFO - gcp_inventory:topics.py - [ce-ps3][pubsub_topic] Finished Pub/Sub topic fetch. Found 5 topics.
2025-06-09 13:40:19,063 - INFO - gcp_inventory:cloud_router.py - [ce-ps3][cloud_router] Starting Cloud Router fetch...
2025-06-09 13:40:19,069 - INFO - gcp_inventory:service_account.py - [ce-ps3][service_account] Starting IAM Service Account fetch...
2025-06-09 13:40:19,125 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Found 2 V1 functions.
2025-06-09 13:40:19,125 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Fetching V2 functions...
2025-06-09 13:40:19,380 - INFO - gcp_inventory:subnet.py - [ce-ps3][subnet] Finished VPC Subnet fetch. Found 59 subnets.
2025-06-09 13:40:19,380 - INFO - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector] Starting VPC Access Connector fetch across all regions...
2025-06-09 13:40:19,387 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Finished fetching addresses.
2025-06-09 13:40:19,453 - ERROR - gcp_inventory:subscriptions.py - [ce-ps3][pubsub] Failed to process details for subscription projects/ce-ps3/subscriptions/eventarc-us-central1-test-glide-543427-sub-254: 'datetime.timedelta' object has no attribute 'nanos'
Traceback (most recent call last):
  File "/app/gcp_inventory_tool/fetchers/subscriptions.py", line 74, in fetch_resources
    "MessageRetentionDuration": duration_to_seconds_str(subscription.message_retention_duration),
                                ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/app/gcp_inventory_tool/utils/duration_to_second.py", line 8, in duration_to_seconds_str
    if duration and (duration.seconds or duration.nanos):
                                         ^^^^^^^^^^^^^^
AttributeError: 'datetime.timedelta' object has no attribute 'nanos'
2025-06-09 13:40:19,454 - INFO - gcp_inventory:subscriptions.py - [ce-ps3][pubsub] Finished Pub/Sub subscription fetch. Found details for 0 subscriptions.
2025-06-09 13:40:19,454 - INFO - gcp_inventory:project_iam.py - [ce-ps3][project_iam] Starting Project IAM Policy fetch...
2025-06-09 13:40:19,727 - INFO - gcp_inventory:address.py - [ce-ps3][address] Finished Compute Address fetch. Found 10 addresses.
2025-06-09 13:40:19,727 - INFO - gcp_inventory:image.py - [ce-ps3][image] Starting Compute Image fetch...
2025-06-09 13:40:19,776 - INFO - gcp_inventory:cloud_router.py - [ce-ps3][cloud_router] Finished Cloud Router fetch. Found 4 routers.
2025-06-09 13:40:19,776 - INFO - gcp_inventory:ha_vpn_gateway.py - [ce-ps3][ha_vpn_gateway] Starting HA VPN Gateway fetch...
2025-06-09 13:40:19,894 - WARNING - gcp_inventory:vpc.py - Could not find matching VPC_PEERING address for specific peering 'servicenetworking-googleapis-com' in VPC 'ce-ps3-nikhil-vpc-01'
2025-06-09 13:40:19,895 - WARNING - gcp_inventory:vpc.py - Could not find matching VPC_PEERING address for specific peering 'servicenetworking-googleapis-com' in VPC 'default'
2025-06-09 13:40:19,896 - INFO - gcp_inventory:vpc.py - [ce-ps3][vpc_network] Finished VPC Network fetch. Found 7 networks.
2025-06-09 13:40:19,896 - INFO - gcp_inventory:classic_vpn_gateway.py - [ce-ps3][classic_vpn_gateway] Starting Classic VPN Gateway fetch...
2025-06-09 13:40:19,984 - INFO - gcp_inventory:gke.py - [ce-ps3][gke_cluster] Finished GKE cluster fetch. Found 1 clusters.
2025-06-09 13:40:19,984 - INFO - gcp_inventory:vpn_tunnel.py - [ce-ps3][vpn_tunnel] Starting VPN Tunnel fetch...
2025-06-09 13:40:20,052 - INFO - gcp_inventory:compute_engine.py - [ce-ps3][compute] Iterating through zones/instances...
2025-06-09 13:40:20,128 - INFO - gcp_inventory:image.py - [ce-ps3][image] Finished Compute Image fetch. Found 8 custom images.
2025-06-09 13:40:20,128 - INFO - gcp_inventory:dns_managed_zone.py - [ce-ps3][dns_managed_zone] Starting Cloud DNS Managed Zone fetch...
2025-06-09 13:40:20,479 - INFO - gcp_inventory:project_iam.py - [ce-ps3][project_iam] Finished Project IAM Policy fetch. Found 117 members with roles.
2025-06-09 13:40:20,479 - INFO - gcp_inventory:dns_policy.py - [ce-ps3][dns_policy] Starting Cloud DNS Policy fetch...
2025-06-09 13:40:20,483 - INFO - gcp_inventory:ha_vpn_gateway.py - [ce-ps3][ha_vpn_gateway] Finished HA VPN Gateway fetch. Found 0 gateways.
2025-06-09 13:40:20,484 - INFO - gcp_inventory:kms_key.py - [ce-ps3][kms_key] Starting KMS Key fetch...
2025-06-09 13:40:20,513 - INFO - gcp_inventory:dns_managed_zone.py - [ce-ps3][dns_managed_zone] Finished Cloud DNS Managed Zone fetch. Found 4 zones.
2025-06-09 13:40:20,513 - INFO - gcp_inventory:instance_group_manager.py - [ce-ps3][instance_group_manager] Starting Instance Group Manager fetch...
2025-06-09 13:40:20,552 - WARNING - gcp_inventory:kms_key.py - [ce-ps3][kms_key] KMS API might not be enabled or project not found. Details: 404 The request concerns location '-' but was sent to location 'global'. Either Cloud KMS is not available in '-' or the request was misrouted. gRPC clients must ensure that 'x-goog-request-params' is specified in request metadata. See https://cloud.google.com/kms/docs/grpc for more information. [reason: "KMS_RESOURCE_NOT_FOUND_IN_LOCATION"
domain: "cloudkms.googleapis.com"
]
2025-06-09 13:40:20,552 - INFO - gcp_inventory:instance_template.py - [ce-ps3][instance_template] Starting Instance Template fetch...
2025-06-09 13:40:20,632 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Found 5 V2 functions.
2025-06-09 13:40:20,632 - INFO - gcp_inventory:cloud_functions.py - [ce-ps3][cloud_function] Finished Cloud Function fetch. Found total 7 functions (V1+V2).
2025-06-09 13:40:20,659 - INFO - gcp_inventory:service_account.py - [ce-ps3][service_account] Finished IAM Service Account fetch. Found 27 service accounts.
2025-06-09 13:40:20,660 - INFO - gcp_inventory:security_policy.py - [ce-ps3][security_policy] Starting Security Policy fetch...
2025-06-09 13:40:20,669 - INFO - gcp_inventory:vpn_tunnel.py - [ce-ps3][vpn_tunnel] Finished VPN Tunnel fetch. Found 0 tunnels.
2025-06-09 13:40:20,669 - INFO - gcp_inventory:health_check.py - [ce-ps3][health_check] Starting Health Check fetch...
2025-06-09 13:40:20,683 - INFO - gcp_inventory:classic_vpn_gateway.py - [ce-ps3][classic_vpn_gateway] Finished Classic VPN Gateway fetch. Found 0 gateways.
2025-06-09 13:40:20,683 - INFO - gcp_inventory:ssl_certificate.py - [ce-ps3][ssl_certificate] Starting SSL Certificate fetch...
2025-06-09 13:40:20,769 - INFO - gcp_inventory:dns_policy.py - [ce-ps3][dns_policy] Finished Cloud DNS Policy fetch. Found 0 policies.
2025-06-09 13:40:20,769 - INFO - gcp_inventory:data_fusion.py - [ce-ps3][data_fusion] Starting Data Fusion instance fetch...
2025-06-09 13:40:20,945 - INFO - gcp_inventory:redis.py - [ce-ps3][redis] Starting Redis instance fetch...
2025-06-09 13:40:20,976 - INFO - gcp_inventory:instance_template.py - [ce-ps3][instance_template] Finished Instance Template fetch. Found 1 templates.
2025-06-09 13:40:20,976 - INFO - gcp_inventory:memcached.py - [ce-ps3][memcached] Starting Memcached instance fetch...
2025-06-09 13:40:20,997 - INFO - gcp_inventory:vpc_connector.py - [ce-ps3][vpc_connector] Will check 42 regions for VPC connectors.
2025-06-09 13:40:21,338 - INFO - gcp_inventory:security_policy.py - [ce-ps3][security_policy] Finished Security Policy fetch. Found 1 policies.
2025-06-09 13:40:21,338 - INFO - gcp_inventory:cloud_scheduler.py - [ce-ps3][cloud_scheduler_job] Starting Cloud Scheduler job fetch...
2025-06-09 13:40:21,339 - INFO - gcp_inventory:cloud_scheduler.py - [ce-ps3][cloud_scheduler_job] Using location list: ['us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2', 'europe-west1', 'europe-west2', 'europe-west3', 'asia-east1', 'asia-northeast1', 'asia-south1', 'australia-southeast1']
2025-06-09 13:40:21,457 - INFO - gcp_inventory:health_check.py - [ce-ps3][health_check] Finished Health Check fetch. Found 9 health checks.
2025-06-09 13:40:21,457 - INFO - gcp_inventory:cloud_run_service.py - [ce-ps3][cloud_run_service] Starting Cloud Run Service fetch...
2025-06-09 13:40:21,821 - ERROR - gcp_inventory:data_fusion.py - [ce-ps3][data_fusion] Permission denied listing Data Fusion instances. Ensure API is enabled and necessary roles granted (e.g., 'Data Fusion Viewer'). Details: 403 Cloud Data Fusion API has not been used in project ce-ps3 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/datafusion.googleapis.com/overview?project=ce-ps3 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "datafusion.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Data Fusion API"
}
metadata {
  key: "containerInfo"
  value: "ce-ps3"
}
metadata {
  key: "consumer"
  value: "projects/ce-ps3"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/datafusion.googleapis.com/overview?project=ce-ps3"
}
, locale: "en-US"
message: "Cloud Data Fusion API has not been used in project ce-ps3 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/datafusion.googleapis.com/overview?project=ce-ps3 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/datafusion.googleapis.com/overview?project=ce-ps3"
}
]
2025-06-09 13:40:21,821 - INFO - gcp_inventory:bigtable.py - [ce-ps3][bigtable] Starting Bigtable instance & cluster fetch...
2025-06-09 13:40:22,096 - ERROR - gcp_inventory:memcached.py - [ce-ps3][memcached] Permission denied listing Memcached instances. Ensure API is enabled and necessary roles granted (e.g., 'Cloud Memorystore Memcached Viewer'). Details: 403 Cloud Memorystore for Memcached API has not been used in project ce-ps3 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/memcache.googleapis.com/overview?project=ce-ps3 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry. [reason: "SERVICE_DISABLED"
domain: "googleapis.com"
metadata {
  key: "service"
  value: "memcache.googleapis.com"
}
metadata {
  key: "serviceTitle"
  value: "Cloud Memorystore for Memcached API"
}
metadata {
  key: "containerInfo"
  value: "ce-ps3"
}
metadata {
  key: "consumer"
  value: "projects/ce-ps3"
}
metadata {
  key: "activationUrl"
  value: "https://console.developers.google.com/apis/api/memcache.googleapis.com/overview?project=ce-ps3"
}
, locale: "en-US"
message: "Cloud Memorystore for Memcached API has not been used in project ce-ps3 before or it is disabled. Enable it by visiting https://console.developers.google.com/apis/api/memcache.googleapis.com/overview?project=ce-ps3 then retry. If you enabled this API recently, wait a few minutes for the action to propagate to our systems and retry."
, links {
  description: "Google developers console API activation"
  url: "https://console.developers.google.com/apis/api/memcache.googleapis.com/overview?project=ce-ps3"
}
]
2025-06-09 13:40:22,096 - INFO - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Starting Cloud Composer environment fetch...
2025-06-09 13:40:22,098 - INFO - gcp_inventory:composer_environment.py - [ce-ps3][composer_environment] Using location list: ['us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2', 'europe-west1', 'europe-west2', 'europe-west3', 'asia-east1', 'asia-northeast1', 'asia-south1', 'australia-southeast1']
2025-06-09 13:40:22,273 - INFO - gcp_inventory:ssl_certificate.py - [ce-ps3][ssl_certificate] Finished SSL Certificate fetch. Found 5 certificates.
2025-06-09 13:40:22,274 - INFO - gcp_inventory:dataproc_cluster.py - [ce-ps3][dataproc_cluster] Starting Dataproc cluster fetch...
2025-06-09 13:40:22,351 - INFO - gcp_inventory:redis.py - [ce-ps3][redis] Finished Redis instance fetch. Found 0 instances.
2025-06-09 13:40:22,352 - INFO - gcp_inventory:artifact_registry.py - [ce-ps3][artifact_registry_repo] Starting Artifact Registry repository fetch...
2025-06-09 13:40:23,295 - INFO - gcp_inventory:bigtable.py - [ce-ps3][bigtable] Finished Bigtable instance/cluster fetch. Found 0 clusters.
2025-06-09 13:40:23,295 - INFO - gcp_inventory:container_registry.py - [ce-ps3][container_registry_repo] Starting Container Registry (via Artifact Registry) fetch...
2025-06-09 13:40:23,308 - ERROR - gcp_inventory:artifact_registry.py - [ce-ps3][artifact_registry_repo] Failed to list or process Artifact Registry repositories: 400 Invalid project name: "projects/ce-ps3/locations/-"
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/usr/local/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.INVALID_ARGUMENT
	details = "Invalid project name: "projects/ce-ps3/locations/-""
	debug_error_string = "UNKNOWN:Error received from peer ipv4:*************:443 {grpc_message:"Invalid project name: \"projects/ce-ps3/locations/-\"", grpc_status:3}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/gcp_inventory_tool/fetchers/artifact_registry.py", line 53, in fetch_resources
    repositories = client.list_repositories(parent=parent)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/google/cloud/artifactregistry_v1/services/artifact_registry/client.py", line 2337, in list_repositories
    response = rpc(
               ^^^^
  File "/usr/local/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 Invalid project name: "projects/ce-ps3/locations/-"
2025-06-09 13:40:23,311 - INFO - gcp_inventory:log_sink.py - [ce-ps3][log_sink] Starting Logging Sink fetch...
2025-06-09 13:40:23,529 - INFO - gcp_inventory:instance_group_manager.py - [ce-ps3][instance_group_manager] Finished Instance Group Manager fetch. Found 3 managers.
2025-06-09 13:40:23,530 - INFO - gcp_inventory:alert_policy.py - [ce-ps3][alert_policy] Starting Alert Policy fetch...
2025-06-09 13:40:23,568 - INFO - gcp_inventory:log_sink.py - [ce-ps3][log_sink] Finished Logging Sink fetch. Found 2 sinks.
2025-06-09 13:40:23,569 - INFO - gcp_inventory:interconnect.py - [ce-ps3][interconnect_attachment] Starting Interconnect Attachment fetch...
2025-06-09 13:40:23,828 - ERROR - gcp_inventory:alert_policy.py - [ce-ps3][alert_policy] Failed to list notification channels: 'bool' object has no attribute 'value'
Traceback (most recent call last):
  File "/app/gcp_inventory_tool/fetchers/alert_policy.py", line 42, in _get_notification_channels
    "enabled": channel.enabled.value if channel.enabled else None, # Get boolean value
               ^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'bool' object has no attribute 'value'
2025-06-09 13:40:23,865 - INFO - gcp_inventory:dataproc_cluster.py - [ce-ps3][dataproc_cluster] Will check Dataproc clusters in regions: ['africa-south1', 'asia-east1', 'asia-east2', 'asia-northeast1', 'asia-northeast2', 'asia-northeast3', 'asia-south1', 'asia-south2', 'asia-southeast1', 'asia-southeast2', 'australia-southeast1', 'australia-southeast2', 'europe-central2', 'europe-north1', 'europe-north2', 'europe-southwest1', 'europe-west1', 'europe-west10', 'europe-west12', 'europe-west2', 'europe-west3', 'europe-west4', 'europe-west6', 'europe-west8', 'europe-west9', 'me-central1', 'me-central2', 'me-west1', 'northamerica-northeast1', 'northamerica-northeast2', 'northamerica-south1', 'southamerica-east1', 'southamerica-west1', 'us-central1', 'us-east1', 'us-east4', 'us-east5', 'us-south1', 'us-west1', 'us-west2', 'us-west3', 'us-west4']
2025-06-09 13:40:24,154 - ERROR - gcp_inventory:alert_policy.py - [ce-ps3][alert_policy] Failed to list or process Alert Policies: 'bool' object has no attribute 'value'
Traceback (most recent call last):
  File "/app/gcp_inventory_tool/fetchers/alert_policy.py", line 142, in fetch_resources
    "Enabled": policy.enabled.value if policy.enabled else None, # Get boolean value
               ^^^^^^^^^^^^^^^^^^^^
AttributeError: 'bool' object has no attribute 'value'
2025-06-09 13:40:24,245 - ERROR - gcp_inventory:container_registry.py - [ce-ps3][container_registry_repo] Failed to list or process Artifact Registry repositories for GCR check: 400 Invalid project name: "projects/ce-ps3/locations/-"
Traceback (most recent call last):
  File "/usr/local/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 76, in error_remapped_callable
    return callable_(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/grpc/_interceptor.py", line 277, in __call__
    response, ignored_call = self._with_call(
                             ^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/grpc/_interceptor.py", line 332, in _with_call
    return call.result(), call
           ^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/grpc/_channel.py", line 440, in result
    raise self
  File "/usr/local/lib/python3.11/site-packages/grpc/_interceptor.py", line 315, in continuation
    response, call = self._thunk(new_method).with_call(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/grpc/_channel.py", line 1198, in with_call
    return _end_unary_response_blocking(state, call, True, None)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/grpc/_channel.py", line 1006, in _end_unary_response_blocking
    raise _InactiveRpcError(state)  # pytype: disable=not-instantiable
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
grpc._channel._InactiveRpcError: <_InactiveRpcError of RPC that terminated with:
	status = StatusCode.INVALID_ARGUMENT
	details = "Invalid project name: "projects/ce-ps3/locations/-""
	debug_error_string = "UNKNOWN:Error received from peer ipv4:*************:443 {grpc_message:"Invalid project name: \"projects/ce-ps3/locations/-\"", grpc_status:3}"
>

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/app/gcp_inventory_tool/fetchers/container_registry.py", line 57, in fetch_resources
    repositories = client.list_repositories(parent=parent)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/google/cloud/artifactregistry_v1/services/artifact_registry/client.py", line 2337, in list_repositories
    response = rpc(
               ^^^^
  File "/usr/local/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py", line 131, in __call__
    return wrapped_func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/google/api_core/grpc_helpers.py", line 78, in error_remapped_callable
    raise exceptions.from_grpc_error(exc) from exc
google.api_core.exceptions.InvalidArgument: 400 Invalid project name: "projects/ce-ps3/locations/-"
2025-06-09 13:40:24,269 - INFO - gcp_inventory:interconnect.py - [ce-ps3][interconnect_attachment] Finished Interconnect Attachment fetch. Found 0 attachments.

An unexpected error occurred during Excel formatting: 7"2�\� cannot be used in worksheets.
Traceback (most recent call last):
  File "/app/gcp_inventory_tool/formatters/excel_formatter.py", line 139, in format
    df.to_excel(writer, sheet_name=safe_sheet_name, index=False, na_rep='') # Write NaN as empty string
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/pandas/util/_decorators.py", line 333, in wrapper
    return func(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/pandas/core/generic.py", line 2436, in to_excel
    formatter.write(
  File "/usr/local/lib/python3.11/site-packages/pandas/io/formats/excel.py", line 952, in write
    writer._write_cells(
  File "/usr/local/lib/python3.11/site-packages/pandas/io/excel/_openpyxl.py", line 490, in _write_cells
    xcell.value, fmt = self._value_with_fmt(cell.val)
    ^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/openpyxl/cell/cell.py", line 218, in value
    self._bind_value(value)
  File "/usr/local/lib/python3.11/site-packages/openpyxl/cell/cell.py", line 197, in _bind_value
    value = self.check_string(value)
            ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/usr/local/lib/python3.11/site-packages/openpyxl/cell/cell.py", line 165, in check_string
    raise IllegalCharacterError(f"{value} cannot be used in worksheets.")
openpyxl.utils.exceptions.IllegalCharacterError: 7"2�\� cannot be used in worksheets.