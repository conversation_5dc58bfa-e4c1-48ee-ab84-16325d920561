# --- File: gcp_inventory_tool/utils/timeout_config.py ---
"""
Timeout and retry configuration utilities for GCP API clients.
Provides standardized timeout and retry policies to handle API rate limits and transient errors.
"""

import logging
import time
import random
from typing import Optional, Dict, Any
from google.api_core import retry
from google.api_core import exceptions as api_exceptions

logger = logging.getLogger('gcp_inventory')

# Default timeout configurations for different service types
# Adjusted for parallel execution to handle rate limiting and contention
DEFAULT_TIMEOUTS = {
    # Services that typically have fast responses
    'fast': {
        'timeout': 45.0,  # Increased from 30s for parallel execution
        'retry_timeout': 180.0,  # Increased from 120s
        'initial_delay': 1.0,
        'max_delay': 30.0,
        'multiplier': 1.5,  # Gentler backoff for fast services
    },
    # Services that may have moderate response times
    'moderate': {
        'timeout': 90.0,  # Increased from 60s
        'retry_timeout': 450.0,  # Increased from 300s
        'initial_delay': 2.0,
        'max_delay': 60.0,
        'multiplier': 2.0,
    },
    # Services that may have slow responses (like large data operations)
    'slow': {
        'timeout': 180.0,  # Increased from 120s
        'retry_timeout': 900.0,  # Increased from 600s
        'initial_delay': 3.0,
        'max_delay': 120.0,
        'multiplier': 2.5,
    },
    # Services that are known to be very slow or have large datasets
    'very_slow': {
        'timeout': 450.0,  # Increased from 300s
        'retry_timeout': 1800.0,  # Increased from 1200s
        'initial_delay': 5.0,
        'max_delay': 300.0,
        'multiplier': 3.0,
    }
}

# Service-specific timeout configurations
SERVICE_TIMEOUT_CONFIGS = {
    'gke_cluster': 'slow',  # GKE clusters can take time to list
    'pubsub': 'moderate',  # Pub/Sub subscriptions
    'pubsub_topic': 'moderate',  # Pub/Sub topics
    'project_iam': 'moderate',  # Project IAM policies
    'kms_key': 'slow',  # KMS keys can be slow
    'vpc_connector': 'moderate',  # VPC connectors
    'cloud_run_service': 'moderate',  # Cloud Run services
    'dataproc_cluster': 'slow',  # Dataproc clusters
    'bigtable': 'moderate',  # Bigtable instances
    'cloud_scheduler_job': 'fast',  # Cloud Scheduler jobs
    'memcached': 'fast',  # Memcached instances
    'redis': 'fast',  # Redis instances
    'data_fusion': 'slow',  # Data Fusion instances
    'composer_environment': 'slow',  # Composer environments
    'default': 'moderate'  # Default for unspecified services
}


def get_timeout_config(service_name: str) -> Dict[str, float]:
    """
    Get timeout configuration for a specific service.
    
    Args:
        service_name: The service name (should match SERVICE_NAME from fetchers)
        
    Returns:
        Dictionary with 'timeout' and 'retry_timeout' values
    """
    config_type = SERVICE_TIMEOUT_CONFIGS.get(service_name, 'default')
    if config_type == 'default':
        config_type = SERVICE_TIMEOUT_CONFIGS['default']
    
    config = DEFAULT_TIMEOUTS[config_type].copy()
    logger.debug(f"Using {config_type} timeout config for service '{service_name}': {config}")
    return config


def create_retry_policy(service_name: str, max_retry_delay: float = 60.0) -> retry.Retry:
    """
    Create a retry policy for a specific service.
    
    Args:
        service_name: The service name
        max_retry_delay: Maximum delay between retries in seconds
        
    Returns:
        Configured retry policy
    """
    timeout_config = get_timeout_config(service_name)
    
    # Define which exceptions should trigger retries
    retry_exceptions = (
        api_exceptions.DeadlineExceeded,
        api_exceptions.ServiceUnavailable,
        api_exceptions.InternalServerError,
        api_exceptions.TooManyRequests,
        api_exceptions.ResourceExhausted,
    )
    
    return retry.Retry(
        predicate=retry.if_exception_type(*retry_exceptions),
        initial=1.0,  # Initial delay
        maximum=max_retry_delay,  # Maximum delay between retries
        multiplier=2.0,  # Exponential backoff multiplier
        timeout=timeout_config['retry_timeout'],  # Total timeout for all retries
        deadline=timeout_config['retry_timeout']  # Same as timeout
    )


def get_client_timeout(service_name: str) -> float:
    """
    Get the client timeout for a specific service.
    
    Args:
        service_name: The service name
        
    Returns:
        Timeout value in seconds
    """
    return get_timeout_config(service_name)['timeout']


def log_timeout_info(service_name: str, project_id: str):
    """
    Log timeout configuration information for debugging.
    
    Args:
        service_name: The service name
        project_id: The project ID
    """
    config = get_timeout_config(service_name)
    logger.debug(
        f"[{project_id}][{service_name}] Using timeout config: "
        f"client_timeout={config['timeout']}s, retry_timeout={config['retry_timeout']}s"
    )


def handle_timeout_exception(e: Exception, service_name: str, project_id: str, operation: str = "operation") -> None:
    """
    Handle timeout exceptions with appropriate logging.

    Args:
        e: The exception that occurred
        service_name: The service name
        project_id: The project ID
        operation: Description of the operation that timed out
    """
    error_str = str(e)

    if "DNS resolution failed" in error_str:
        logger.warning(
            f"[{project_id}][{service_name}] DNS resolution failed during {operation}. "
            f"This is common with high parallel execution (10+ workers). "
            f"Recommendations: 1) Reduce max_workers to 3-5, 2) Check DNS resolver, "
            f"3) Consider running sequentially for problematic services. Error: {e}"
        )
    elif isinstance(e, (api_exceptions.DeadlineExceeded, api_exceptions.RetryError)):
        logger.warning(
            f"[{project_id}][{service_name}] {operation} timed out. "
            f"This may be due to large datasets, API rate limits, or network issues. "
            f"Consider running the inventory during off-peak hours or for smaller scopes. Error: {e}"
        )
    elif isinstance(e, api_exceptions.ServiceUnavailable):
        logger.warning(
            f"[{project_id}][{service_name}] Service temporarily unavailable during {operation}. "
            f"This may be due to DNS issues or service maintenance. Error: {e}"
        )
    else:
        logger.error(
            f"[{project_id}][{service_name}] Unexpected error during {operation}: {e}",
            exc_info=True
        )


def add_staggered_delay(service_name: str, project_id: str, max_delay: float = 2.0):
    """
    Add a random delay to prevent thundering herd problem in parallel execution.

    Args:
        service_name: The service name
        project_id: The project ID
        max_delay: Maximum delay in seconds
    """
    delay = random.uniform(0.1, max_delay)
    logger.debug(f"[{project_id}][{service_name}] Adding staggered delay of {delay:.2f}s to prevent DNS overload")
    time.sleep(delay)
