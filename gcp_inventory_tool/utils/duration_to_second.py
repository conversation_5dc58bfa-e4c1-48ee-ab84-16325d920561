# Helper to convert protobuf Duration to seconds string
# (Can be shared or moved to a common utils module if used frequently)
from typing import Optional
from google.protobuf import duration_pb2 # For handling duration fields

def duration_to_seconds_str(duration: Optional[duration_pb2.Duration]) -> Optional[str]:
    """Converts a Protobuf Duration object to a string representing seconds."""
    if duration and (duration.seconds or duration.nanos):
         return str(duration.seconds)
    return None