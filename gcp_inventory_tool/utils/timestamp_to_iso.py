# from typing import Optional
# import logging
# from google.protobuf import timestamp_pb2

# logger = logging.getLogger('gcp_inventory')

# def timestamp_to_iso(ts: Optional[timestamp_pb2.Timestamp]) -> Optional[str]:
#      """Converts a Protobuf Timestamp to an ISO 8601 string."""
#      if ts:
#          try:
#              # Convert datetime object first
#              # Protobuf timestamps are timezone-aware (UTC)
#              dt = ts.ToDatetime()
#              return dt.isoformat()
#          except Exception as e:
#              logger.warning(f"Could not convert timestamp {ts} to ISO string: {e}")
#              return str(ts) # Fallback
#      return None

import logging
from datetime import datetime
from google.protobuf.timestamp_pb2 import Timestamp
from google.protobuf.json_format import MessageToDict

logger = logging.getLogger('gcp_inventory')

def timestamp_to_iso(timestamp):
    """
    Converts various timestamp formats to ISO 8601 string format.
    
    Args:
        timestamp: Can be a Timestamp protobuf, DatetimeWithNanoseconds from google-cloud-core,
                  or a Python datetime object.
    
    Returns:
        An ISO 8601 formatted string, or None if conversion fails.
    """
    if timestamp is None:
        return None
    
    try:
        # Case 1: If it's already a string, check if it looks like an ISO format and return it
        if isinstance(timestamp, str):
            # Simple validation that it looks like an ISO timestamp
            try:
                # Try to parse it to ensure it's a valid datetime string
                datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                return timestamp  # It's already in a string format
            except ValueError:
                # If it's not a valid datetime string, continue to other conversion methods
                pass
        
        # Case 2: Handle Google Protobuf Timestamp objects
        if isinstance(timestamp, Timestamp):
            return timestamp.ToDatetime().isoformat()
        
        # Case 3: Handle DatetimeWithNanoseconds objects from google.cloud
        # These have datetime-like properties but not the ToDatetime method
        if hasattr(timestamp, 'year') and hasattr(timestamp, 'month') and hasattr(timestamp, 'day'):
            # Convert it to a standard datetime and then to ISO format
            try:
                # If it has tzinfo, use it directly
                if hasattr(timestamp, 'tzinfo') and timestamp.tzinfo:
                    dt = datetime(
                        year=timestamp.year,
                        month=timestamp.month,
                        day=timestamp.day,
                        hour=timestamp.hour,
                        minute=timestamp.minute,
                        second=timestamp.second,
                        microsecond=timestamp.microsecond,
                        tzinfo=timestamp.tzinfo
                    )
                else:
                    # If no tzinfo, assume UTC
                    dt = datetime(
                        year=timestamp.year,
                        month=timestamp.month,
                        day=timestamp.day,
                        hour=timestamp.hour,
                        minute=timestamp.minute,
                        second=timestamp.second,
                        microsecond=timestamp.microsecond
                    )
                return dt.isoformat()
            except AttributeError:
                # If needed attributes aren't available, try the next approach
                pass
        
        # Case 4: If it's a standard Python datetime object
        if isinstance(timestamp, datetime):
            return timestamp.isoformat()
        
        # Case 5: If it's already in string format but didn't parse earlier,
        # just return it as is and log a warning
        if isinstance(timestamp, str):
            logger.warning(f"Returning timestamp string as-is, may not be ISO format: {timestamp}")
            return timestamp
            
        # If we get here, we've tried all known methods and failed
        logger.warning(f"Could not convert timestamp {timestamp} to ISO string: Unknown format type {type(timestamp)}")
        # Return string representation as last resort
        return str(timestamp)
        
    except Exception as e:
        logger.warning(f"Could not convert timestamp {timestamp} to ISO string: {str(e)}")
        return None