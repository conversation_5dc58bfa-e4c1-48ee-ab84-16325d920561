resource "google_storage_bucket" "bucket" {
  name          = var.bucket_name
  location      = var.bucket_location
  force_destroy = true

  public_access_prevention = var.public_access_prevention
}

variable "bucket_name" {
  default =  "no-public-access-bucket"
  type = string
}

variable "bucket_location" {
  default =  "US"
  type = string
}

variable "public_access_prevention" {
  default =  "enforced"
  type = string
}


output "bucket_name" {
  value = google_storage_bucket.bucket.name
}

output "bucket_location" {
  value = google_storage_bucket.bucket.location
}

output "public_access_prevention" {
  value = google_storage_bucket.bucket.public_access_prevention
}
