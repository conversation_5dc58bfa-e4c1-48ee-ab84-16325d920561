from typing import Optional
import logging

logger = logging.getLogger('gcp_inventory')

def get_resource_name(url: Optional[str]) -> Optional[str]:
    """Extracts the last part (resource name) from a GCP resource URL/path."""
    if not url:
        return None
    try:
        # Handle cases where the URL might already be just the name or empty
        if '/' not in url:
            return url if url else None
        return url.split('/')[-1]
    except Exception:
        logger.warning(f"Could not parse resource name from path: {url}")
        return url # Return original url as fallback