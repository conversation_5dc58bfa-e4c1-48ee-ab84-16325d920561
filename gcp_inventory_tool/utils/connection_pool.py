# --- File: gcp_inventory_tool/utils/connection_pool.py ---
"""
Connection pool management for GCP API clients to prevent DNS resolution failures
and connection exhaustion during parallel execution.
"""

import logging
import threading
import time
from typing import Dict, Any, Optional
from google.oauth2.credentials import Credentials
import grpc

logger = logging.getLogger('gcp_inventory')

class ConnectionPoolManager:
    """
    Manages connection pools and DNS resolution for GCP API clients.
    Prevents DNS resolution failures and connection exhaustion during parallel execution.
    """

    def __init__(self):
        self._lock = threading.Lock()
        self._dns_cache = {}
        self._client_cache = {}  # Cache clients to reuse connections
        self._connection_semaphore = threading.Semaphore(3)  # Reduced to 3 concurrent connections
        self._last_dns_clear = time.time()
        self.DNS_CACHE_TTL = 300  # 5 minutes
        self.CLIENT_CACHE_TTL = 600  # 10 minutes
        
    def get_grpc_channel_options(self) -> list:
        """
        Get optimized gRPC channel options for parallel execution.
        These options specifically address C-ares DNS resolution issues.

        Returns:
            List of gRPC channel options
        """
        return [
            # DNS resolution settings - key for fixing C-ares issues
            ('grpc.dns_resolution_timeout_ms', 30000),  # Increased to 30 seconds
            ('grpc.dns_enable_srv_queries', False),  # Disable SRV queries
            ('grpc.use_local_subchannel_pool', True),  # Use local subchannel pool

            # Connection pool settings to reduce concurrent connections
            ('grpc.max_concurrent_streams', 10),  # Limit concurrent streams per connection
            ('grpc.max_connection_idle_ms', 300000),  # 5 minutes - keep connections longer
            ('grpc.max_connection_age_ms', 1800000),  # 30 minutes - longer connection age
            ('grpc.max_connection_age_grace_ms', 60000),  # 1 minute grace

            # Keepalive settings to maintain connections
            ('grpc.keepalive_time_ms', 60000),  # 1 minute
            ('grpc.keepalive_timeout_ms', 10000),  # 10 seconds
            ('grpc.keepalive_permit_without_calls', True),
            ('grpc.http2.max_pings_without_data', 0),
            ('grpc.http2.min_time_between_pings_ms', 30000),  # 30 seconds
            ('grpc.http2.min_ping_interval_without_data_ms', 300000),  # 5 minutes

            # Retry and buffer settings
            ('grpc.enable_retries', True),
            ('grpc.per_rpc_retry_buffer_size', 2 * 1024 * 1024),  # 2MB

            # Channel pool settings to reuse connections
            ('grpc.max_send_message_length', 4 * 1024 * 1024),  # 4MB
            ('grpc.max_receive_message_length', 4 * 1024 * 1024),  # 4MB
        ]
    
    def acquire_connection(self) -> bool:
        """
        Acquire a connection slot from the semaphore.
        
        Returns:
            True if connection acquired, False if timeout
        """
        try:
            acquired = self._connection_semaphore.acquire(timeout=30)
            if acquired:
                logger.debug("Connection slot acquired")
            else:
                logger.warning("Failed to acquire connection slot within 30 seconds")
            return acquired
        except Exception as e:
            logger.error(f"Error acquiring connection slot: {e}")
            return False
    
    def release_connection(self):
        """Release a connection slot back to the semaphore."""
        try:
            self._connection_semaphore.release()
            logger.debug("Connection slot released")
        except Exception as e:
            logger.error(f"Error releasing connection slot: {e}")
    
    def clear_dns_cache_if_needed(self):
        """Clear DNS cache if TTL has expired."""
        current_time = time.time()
        if current_time - self._last_dns_clear > self.DNS_CACHE_TTL:
            with self._lock:
                if current_time - self._last_dns_clear > self.DNS_CACHE_TTL:
                    self._dns_cache.clear()
                    self._last_dns_clear = current_time
                    logger.debug("DNS cache cleared due to TTL expiration")
    
    def get_client_config(self, service_name: str) -> Dict[str, Any]:
        """
        Get client configuration optimized for parallel execution.
        
        Args:
            service_name: The service name
            
        Returns:
            Dictionary with client configuration options
        """
        self.clear_dns_cache_if_needed()
        
        return {
            'grpc_channel_options': self.get_grpc_channel_options(),
            'connection_pool_size': 5,  # Limit connection pool size
            'dns_cache_enabled': True,
        }
    
    def get_cached_client(self, client_class, credentials: Credentials, **kwargs):
        """
        Get a cached client or create a new one with connection pool management.

        Args:
            client_class: The client class to instantiate
            credentials: GCP credentials
            **kwargs: Additional arguments for client creation

        Returns:
            Client instance or None if connection couldn't be acquired
        """
        # Create cache key based on client class and credentials
        cache_key = f"{client_class.__name__}_{id(credentials)}"

        with self._lock:
            # Check if we have a cached client
            if cache_key in self._client_cache:
                cached_client, timestamp = self._client_cache[cache_key]
                if time.time() - timestamp < self.CLIENT_CACHE_TTL:
                    logger.debug(f"Using cached {client_class.__name__}")
                    return cached_client
                else:
                    # Remove expired client
                    del self._client_cache[cache_key]
                    logger.debug(f"Expired cached {client_class.__name__}")

        # Create new client
        client = self.create_client_with_pool_management(client_class, credentials, **kwargs)
        if client:
            with self._lock:
                self._client_cache[cache_key] = (client, time.time())
                logger.debug(f"Cached new {client_class.__name__}")

        return client

    def create_client_with_pool_management(self, client_class, credentials: Credentials, **kwargs):
        """
        Create a client with connection pool management.

        Args:
            client_class: The client class to instantiate
            credentials: GCP credentials
            **kwargs: Additional arguments for client creation

        Returns:
            Client instance or None if connection couldn't be acquired
        """
        if not self.acquire_connection():
            logger.error("Could not acquire connection slot for client creation")
            return None

        try:
            # Add gRPC channel options to kwargs
            if 'transport' not in kwargs:
                kwargs['transport'] = 'grpc'

            # Note: gRPC options are handled at the transport level
            # The Google Cloud client libraries handle gRPC configuration internally
            # Our connection pooling and caching provides the main benefit

            # Create client with optimized settings
            client = client_class(credentials=credentials, **kwargs)
            logger.debug(f"Created {client_class.__name__} with connection pool management")
            return client

        except Exception as e:
            logger.error(f"Error creating client {client_class.__name__}: {e}")
            self.release_connection()
            return None


# Global connection pool manager instance
_connection_pool_manager = None
_manager_lock = threading.Lock()


def get_connection_pool_manager() -> ConnectionPoolManager:
    """
    Get the global connection pool manager instance (singleton).
    
    Returns:
        ConnectionPoolManager instance
    """
    global _connection_pool_manager
    if _connection_pool_manager is None:
        with _manager_lock:
            if _connection_pool_manager is None:
                _connection_pool_manager = ConnectionPoolManager()
                logger.debug("Created global connection pool manager")
    return _connection_pool_manager


def with_connection_management(func):
    """
    Decorator to add connection management to client operations.
    
    Args:
        func: Function to wrap
        
    Returns:
        Wrapped function
    """
    def wrapper(*args, **kwargs):
        manager = get_connection_pool_manager()
        try:
            return func(*args, **kwargs)
        finally:
            # Release connection when operation completes
            manager.release_connection()
    
    return wrapper


def create_managed_client(client_class, credentials: Credentials, **kwargs):
    """
    Create a client with connection pool management and caching.

    Args:
        client_class: The client class to instantiate
        credentials: GCP credentials
        **kwargs: Additional arguments for client creation

    Returns:
        Client instance or None if connection couldn't be acquired
    """
    manager = get_connection_pool_manager()
    return manager.get_cached_client(client_class, credentials, **kwargs)


def get_grpc_options():
    """
    Get optimized gRPC channel options for parallel execution.
    
    Returns:
        List of gRPC channel options
    """
    manager = get_connection_pool_manager()
    return manager.get_grpc_channel_options()
