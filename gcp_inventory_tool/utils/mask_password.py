from typing import Optional

def mask_value_based_on_name(
    name: str,
    value: Optional[str],
    keywords: Optional[list[str]] = None,
    # Parameters for value masking (used when name matches keywords)
    start_reveal: int = 1, # <-- Changed default
    end_reveal: int = 1,   # <-- Changed default
    min_length_to_mask: int = 8, # Keep a minimum length for meaningful masking
    mask_char: str = '*'
) -> Optional[str]:
    """
    Masks a value using start/end reveal if its corresponding name contains
    certain keywords. Otherwise, returns the original value.

    Combines name-based triggering with value-based masking.

    Args:
        name: The name associated with the value (e.g., environment variable name,
              parameter name). Case-insensitive matching is performed.
        value: The actual value (e.g., the secret read from the environment).
               Can be None.
        keywords: A list of keywords (lowercase) to check for in the name.
                  If the name contains any of these keywords, the value is masked
                  using the start/end reveal logic.
                  Defaults to ["password", "credentials", "key"].
        start_reveal: The number of characters to reveal at the beginning of the
                      value if masking is triggered. Defaults to 1. # <-- Updated default desc
        end_reveal: The number of characters to reveal at the end of the value
                    if masking is triggered. Defaults to 1. # <-- Updated default desc
        min_length_to_mask: The minimum length the value must have for start/end
                            reveal masking to be applied. Shorter values (if
                            masking is triggered) will be fully masked (showing
                            at most first/last if len > 2, else all masked).
                            Defaults to 8.
        mask_char: The character used for masking. Defaults to '*'.


    Returns:
        The original value if the name doesn't contain any keywords.
        A masked version of the value (revealing start/end) if the name
        contains a keyword and the value is long enough.
        A fully masked string (e.g., '********') if the name contains a keyword
        but the value is shorter than min_length_to_mask (or too short for
        even first/last reveal).
        None if the input value was None.
    """
    if value is None:
        return None

    if keywords is None:
        # Default keywords to check for (lowercase)
        keywords = ["password", "credentials", "key", "token", "secret"]

    should_mask = False
    # Perform case-insensitive check for keywords in the name
    name_lower = name.lower()
    for keyword in keywords:
        # Ensure keyword is lowercase for comparison
        if keyword.lower() in name_lower:
            should_mask = True
            break # Found a keyword, no need to check further

    if should_mask:
        # Apply the start/end reveal masking logic to the value
        secret_len = len(value)

        # Ensure reveal lengths are not negative
        start_reveal = max(0, start_reveal)
        end_reveal = max(0, end_reveal)

        # Handle very short strings - mask completely or based on reveal settings
        if secret_len < 3: # Cannot show distinct first and last
             return mask_char * secret_len
        if secret_len < min_length_to_mask:
             # For moderately short strings, still try to show first/last if possible
             if start_reveal + end_reveal >= secret_len:
                  # If reveal overlaps or covers, mask the middle char(s)
                  mid_mask_len = max(0, secret_len - start_reveal - end_reveal)
                  # Show first, mask middle (if any), show last
                  return value[:start_reveal] + (mask_char * mid_mask_len) + value[secret_len - end_reveal:]
             else:
                  # Fallback to fixed mask if min_length logic desired it
                  # Or implement the standard masking below. Let's use standard masking.
                  pass # Proceed to standard masking logic below

        # Standard masking for strings long enough
        # Ensure we don't try to reveal more characters than exist (already handled by short string checks)
        # Calculate the number of characters to mask
        mask_len = secret_len - start_reveal - end_reveal
        # Ensure mask_len is not negative (shouldn't happen with checks above)
        mask_len = max(0, mask_len)


        # Construct the masked string
        masked_value = (
            value[:start_reveal] +
            (mask_char * mask_len) +
            value[secret_len - end_reveal:]
        )
        return masked_value
    else:
        # If no keyword was found in the name, return the original value
        return value
