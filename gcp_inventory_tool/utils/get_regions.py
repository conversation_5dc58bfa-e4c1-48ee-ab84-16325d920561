import logging
import time
from typing import List, Dict, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions
import threading

logger = logging.getLogger('gcp_inventory')

# Thread-safe cache for regions per project
_region_cache: Dict[str, Dict[str, any]] = {}
_cache_lock = threading.Lock()

# Fallback regions for different service types
FALLBACK_REGIONS = {
    'compute': [
        'us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2', 'us-west3', 'us-west4',
        'europe-west1', 'europe-west2', 'europe-west3', 'europe-west4', 'europe-west6',
        'europe-north1', 'europe-central2',
        'asia-east1', 'asia-east2', 'asia-northeast1', 'asia-northeast2', 'asia-northeast3',
        'asia-south1', 'asia-south2', 'asia-southeast1', 'asia-southeast2',
        'australia-southeast1', 'australia-southeast2',
        'northamerica-northeast1', 'northamerica-northeast2',
        'southamerica-east1', 'southamerica-west1'
    ],
    'artifact_registry': [
        'us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2',
        'europe-west1', 'europe-west2', 'europe-west3', 'europe-west4', 'europe-west6',
        'asia-east1', 'asia-northeast1', 'asia-south1', 'asia-southeast1',
        'australia-southeast1', 'northamerica-northeast1', 'southamerica-east1'
    ],
    'cloud_functions': [
        'us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2',
        'europe-west1', 'europe-west2', 'europe-west3',
        'asia-east1', 'asia-northeast1', 'asia-south1',
        'australia-southeast1'
    ],
    'default': [
        'us-central1', 'us-east1', 'us-west1',
        'europe-west1', 'asia-east1'
    ]
}

# Cache TTL in seconds (1 hour)
CACHE_TTL = 3600


def get_available_regions(project_id: str, credentials: Credentials,
                         service_type: str = 'compute',
                         use_cache: bool = True,
                         max_retries: int = 3) -> List[str]:
    """
    Gets a list of available regions for the project with caching and fallback support.

    Args:
        project_id: GCP project ID
        credentials: GCP credentials
        service_type: Type of service to get regions for (affects fallback list)
        use_cache: Whether to use cached results
        max_retries: Maximum number of retry attempts

    Returns:
        List of region names
    """
    cache_key = f"{project_id}_{service_type}"

    # Check cache first
    if use_cache:
        with _cache_lock:
            if cache_key in _region_cache:
                cache_entry = _region_cache[cache_key]
                if time.time() - cache_entry['timestamp'] < CACHE_TTL:
                    logger.debug(f"[{project_id}] Using cached regions for {service_type}")
                    return cache_entry['regions']

    # Try to fetch regions dynamically
    regions = _fetch_regions_with_retry(project_id, credentials, max_retries)

    # If dynamic fetch failed, use fallback
    if not regions:
        fallback_regions = FALLBACK_REGIONS.get(service_type, FALLBACK_REGIONS['default'])
        logger.warning(f"[{project_id}] Using fallback regions for {service_type}: {len(fallback_regions)} regions")
        regions = fallback_regions.copy()

    # Cache the result
    if use_cache:
        with _cache_lock:
            _region_cache[cache_key] = {
                'regions': regions,
                'timestamp': time.time()
            }

    logger.info(f"[{project_id}] Using {len(regions)} regions for {service_type}")
    return regions


def _fetch_regions_with_retry(project_id: str, credentials: Credentials, max_retries: int) -> List[str]:
    """Fetch regions with retry logic."""
    regions = []
    region_client = None

    for attempt in range(max_retries):
        try:
            region_client = compute_v1.RegionsClient(credentials=credentials)
            request = compute_v1.ListRegionsRequest(project=project_id)

            for region in region_client.list(request=request):
                if region.status == 'UP':  # Only include active regions
                    regions.append(region.name)

            logger.debug(f"[{project_id}] Dynamically fetched {len(regions)} active regions (attempt {attempt + 1})")
            return regions

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}] Permission denied listing regions: {e}")
            break  # Don't retry on permission errors
        except api_exceptions.DeadlineExceeded as e:
            logger.warning(f"[{project_id}] Timeout listing regions (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)  # Exponential backoff
        except Exception as e:
            logger.warning(f"[{project_id}] Failed to list regions (attempt {attempt + 1}): {e}")
            if attempt < max_retries - 1:
                time.sleep(2 ** attempt)
        finally:
            if region_client:
                try:
                    region_client.transport.close()
                except Exception as close_e:
                    logger.debug(f"[{project_id}] Error closing region client transport: {close_e}")
                region_client = None

    return []


def get_regions_for_service(service_name: str, project_id: str, credentials: Credentials) -> List[str]:
    """
    Get appropriate regions for a specific service.

    Args:
        service_name: Name of the service (e.g., 'artifact_registry', 'cloud_functions')
        project_id: GCP project ID
        credentials: GCP credentials

    Returns:
        List of region names appropriate for the service
    """
    # Map service names to service types
    service_type_mapping = {
        'artifact_registry_repo': 'artifact_registry',
        'container_registry_repo': 'artifact_registry',
        'cloud_function': 'cloud_functions',
        'cloud_scheduler_job': 'cloud_functions',
        'composer_environment': 'cloud_functions',
        'vpc_connector': 'cloud_functions',
        'dataproc_cluster': 'compute',
        'dataflow_job': 'compute',
    }

    service_type = service_type_mapping.get(service_name, 'compute')
    return get_available_regions(project_id, credentials, service_type)


def clear_region_cache(project_id: Optional[str] = None) -> None:
    """Clear region cache for a specific project or all projects."""
    with _cache_lock:
        if project_id:
            keys_to_remove = [key for key in _region_cache.keys() if key.startswith(f"{project_id}_")]
            for key in keys_to_remove:
                del _region_cache[key]
            logger.debug(f"Cleared region cache for project {project_id}")
        else:
            _region_cache.clear()
            logger.debug("Cleared all region cache")


def get_cache_stats() -> Dict[str, any]:
    """Get cache statistics for monitoring."""
    with _cache_lock:
        current_time = time.time()
        valid_entries = sum(1 for entry in _region_cache.values()
                          if current_time - entry['timestamp'] < CACHE_TTL)
        return {
            'total_entries': len(_region_cache),
            'valid_entries': valid_entries,
            'expired_entries': len(_region_cache) - valid_entries,
            'cache_ttl': CACHE_TTL
        }