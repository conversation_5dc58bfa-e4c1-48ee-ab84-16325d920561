import logging
from typing import List
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1 # Import compute client for regions
from google.api_core import exceptions as api_exceptions

logger = logging.getLogger('gcp_inventory')


def get_available_regions(project_id: str, credentials: Credentials) -> List[str]:
    """Gets a list of available compute regions for the project."""
    regions = []
    region_client = None
    try:
        region_client = compute_v1.RegionsClient(credentials=credentials)
        request = compute_v1.ListRegionsRequest(project=project_id)
        for region in region_client.list(request=request):
            regions.append(region.name)
        logger.debug(f"[{project_id}] Dynamically fetched {len(regions)} regions.")
        return regions
    except api_exceptions.Forbidden as e:
        logger.error(f"[{project_id}] Permission denied listing regions: {e}. Cannot fetch regional resources dynamically.")
        return [] # Return empty list on permission error
    except Exception as e:
        logger.error(f"[{project_id}] Failed to list compute regions: {e}", exc_info=True)
        return [] # Return empty list on other errors
    finally:
        if region_client:
            try:
                region_client.transport.close()
            except Exception as close_e:
                logger.warning(f"[{project_id}] Error closing region client transport: {close_e}")