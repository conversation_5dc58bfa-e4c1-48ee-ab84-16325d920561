import logging
import json
from typing import List, Dict, Any, Optional
from collections import defaultdict
try:
    from tabulate import tabulate
    TABULATE_AVAILABLE = True
except ImportError:
    TABULATE_AVAILABLE = False

from .base_formatter import OutputFormatter

logger = logging.getLogger(__name__)

class ConsoleFormatter(OutputFormatter):
    """Formats the inventory data as a table printed to the console using tabulate."""

    DEFAULT_TABLE_FORMAT = "grid" # Default table style
    DEFAULT_LIST_SEPARATOR = ', ' # Separator for joining list items in console output
    MAX_WIDTH = 150 # Max width for complex types before truncating

    def __init__(self, output_options: Optional[Dict[str, Any]] = None):
        """Initialize the Console formatter and check for tabulate dependency."""
        super().__init__(output_options)
        if not TABULATE_AVAILABLE:
            logger.error("The 'tabulate' library is required for console table output. Please install it (`pip install tabulate`).")
            raise ImportError("The 'tabulate' library is required for console table output.")

    def _flatten_value_for_console(self, value: Any) -> str:
        """Converts complex types into readable string representations for console."""
        if isinstance(value, list):
            s = self.DEFAULT_LIST_SEPARATOR.join(map(str, value))
        elif isinstance(value, dict):
            try:
                s = json.dumps(value, separators=(',', ':'), default=str)
            except TypeError:
                s = str(value)
        elif value is None:
            return "" # Represent None as empty string
        else:
            s = str(value)

        # Truncate long strings
        if len(s) > self.MAX_WIDTH:
            return s[:self.MAX_WIDTH - 3] + "..."
        return s

    def _get_ordered_header(self, resources: List[Dict[str, Any]]) -> List[str]:
         """Determines header order based on the first resource, prioritizing ProjectID and Name."""
         if not resources:
             return []

         first_resource_keys = list(resources[0].keys())
         fixed_headers = []
         remaining_keys = list(first_resource_keys)

         if 'ProjectID' in remaining_keys:
              fixed_headers.append('ProjectID')
              remaining_keys.remove('ProjectID')

         name_key = None
         if 'Name' in remaining_keys:
             name_key = 'Name'
         else:
             common_name_keys = ['InstanceName', 'PolicyName', 'RuleName', 'BucketName', 'DisplayName', 'SubscriptionName', 'TopicName', 'JobName', 'ClusterName', 'ServiceName']
             for key in first_resource_keys:
                 if key in common_name_keys:
                     name_key = key
                     break
         if name_key and name_key in remaining_keys:
              fixed_headers.append(name_key)
              remaining_keys.remove(name_key)

         # Exclude the 'service' key from the console output header/table body
         if 'service' in remaining_keys:
             remaining_keys.remove('service')

         return fixed_headers + remaining_keys


    def format(self, resources: List[Dict[str, Any]]):
        """
        Formats the resources list as tables printed to the console.
        Groups resources by the 'service' key and prints one table per service.

        Args:
            resources: The list of resource dictionaries. Each dict must have a 'service' key.
        """
        table_format = self.options.get('table_format', self.DEFAULT_TABLE_FORMAT)

        if not resources:
            logger.warning("No resources provided to format for console.")
            print("\n--- No resources found ---")
            return

        logger.info(f"Formatting {len(resources)} resources for console output...")

        # --- Group resources by service ---
        resources_by_service = defaultdict(list)
        for resource in resources:
            service_key = resource.get('service', 'UnknownService')
            resources_by_service[service_key].append(resource)
        # ---------------------------------

        # --- Print table for each service ---
        for service_name, service_resources in sorted(resources_by_service.items()):
            if not service_resources:
                continue

            print(f"\n--- Inventory for Service: {service_name} ({len(service_resources)} resources) ---")

            # Determine header order for this specific table
            header = self._get_ordered_header(service_resources)
            if not header:
                print("(No data columns found for this service)")
                continue

            # Prepare table data (list of lists)
            table_data = []
            for resource in service_resources:
                 row = [self._flatten_value_for_console(resource.get(key)) for key in header]
                 table_data.append(row)

            # Print the table using tabulate
            try:
                print(tabulate(table_data, headers=header, tablefmt=table_format))
            except Exception as e:
                logger.error(f"Failed to generate table for service '{service_name}' using tabulate: {e}", exc_info=True)
                print(f"(Error generating table for {service_name})")
        # ----------------------------------
