#Import the base class to make it easily accessible
from .base_formatter import Output<PERSON><PERSON>atter

# Optionally, import concrete formatters to register them or make them available
from .json_formatter import Json<PERSON><PERSON>atter
from .csv_formatter import CsvFormatter
from .excel_formatter import ExcelFormatter # Import the new Excel formatter
from .console_formatter import <PERSON>sol<PERSON><PERSON><PERSON>atter

import logging
from typing import Dict, Type, Optional

logger = logging.getLogger(__name__)

# Simple registry for formatters (optional, but can be useful)
_FORMATTERS: Dict[str, Type[OutputFormatter]] = {
    "json": JsonFormatter,
    "csv": CsvFormatter,
    "excel": ExcelFormatter, # Register the Excel formatter
    "console": ConsoleFormatter,
}

def get_formatter(format_name: str) -> Optional[Type[OutputFormatter]]:
    """
    Retrieves the formatter class for a given format name.

    Args:
        format_name: The name of the format (e.g., 'json', 'csv', 'excel').

    Returns:
        The OutputFormatter subclass, or None if not found.
    """
    formatter_class = _FORMATTERS.get(format_name.lower())
    if not formatter_class:
         logger.warning(f"No formatter registered for format name: '{format_name}'")
    return formatter_class