import abc
from typing import List, Dict, Any, Optional

class OutputFormatter(abc.ABC):
    """
    Abstract Base Class for all output formatters.

    Subclasses must implement the `format` method to handle the presentation
    of the collected inventory data (e.g., writing to JSON, CSV, console).
    """

    def __init__(self, output_options: Optional[Dict[str, Any]] = None):
        """
        Initialize the formatter.

        Args:
            output_options: A dictionary of options specific to the formatter
                            (e.g., file path, indentation). Defaults to None.
        """
        self.options = output_options or {}

    @abc.abstractmethod
    def format(self, resources: List[Dict[str, Any]]):
        """
        Formats and outputs the provided resource inventory data.

        This method must be implemented by concrete subclasses.

        Args:
            resources: A list of dictionaries, where each dictionary
                       represents a discovered GCP resource.
        """
        pass
