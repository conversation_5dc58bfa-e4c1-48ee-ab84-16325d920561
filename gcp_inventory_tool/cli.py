import click # Use click instead of argparse
import logging
import sys
import os
from typing import Op<PERSON>, Tuple, List

# Adjust import path if running cli.py directly during development
# (<PERSON><PERSON> handles entry points differently, less likely needed but keep for safety)
if __name__ == "__main__" and "." not in __package__:
     script_dir = os.path.dirname(os.path.abspath(__file__))
     parent_dir = os.path.dirname(script_dir)
     sys.path.insert(0, parent_dir)
     # Set package context for relative imports
     __package__ = os.path.basename(script_dir) # Should be 'gcp_inventory_tool'

from .core.inventory import InventoryGenerator
from .formatters import get_formatter # Import formatter discovery
from .utils.logging_config import setup_logging # Use the setup function
from .core import config as cfg_manager # Import config loader
# Import google auth exceptions for handling in main
import google.auth.exceptions

# Logger for the CLI module itself
logger = logging.getLogger(__name__) # Uses the name 'gcp_inventory_tool.cli'

# Define available formats for click choice
AVAILABLE_FORMATS = ['json', 'csv', 'excel', 'console']
AVAILABLE_LOG_LEVELS = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']

# --- Click Command Definition ---
@click.command()
@click.option(
    '-c', '--config', 'config_file_path_arg', # Use different internal name
    default="config.yaml",
    help="Path to the YAML configuration file.",
    type=click.Path(exists=False, dir_okay=False)
)
@click.option(
    '-p', '--projects',
    help="Comma-separated list of project IDs to scan (overrides config file).",
    type=str,
    default=None
)
@click.option(
    '-s', '--services',
    help="Comma-separated list of services to scan (overrides config file).",
    type=str,
    default=None
)
@click.option(
    '-o', '--output-file',
    help="Path to the output file (required for csv/excel, optional for json).",
    type=click.Path(dir_okay=False, writable=True),
    default=None
)
@click.option(
    '-f', '--format', 'output_format_arg', # Use different internal name
    type=click.Choice(AVAILABLE_FORMATS, case_sensitive=False),
    default=None,
    help=f"Output format ({', '.join(AVAILABLE_FORMATS)}). Overrides config."
)
@click.option(
    '-w', '--max-workers',
    type=int,
    default=None,
    help="Number of concurrent workers for fetching. Overrides config/default."
)
@click.option(
    '-l', '--log-level', 'log_level_arg', # Use different internal name
    type=click.Choice(AVAILABLE_LOG_LEVELS, case_sensitive=False),
    default=None,
    help="Logging level. Overrides config."
)
@click.option(
    '--log-file', 'log_file_arg', # Use different internal name
    type=click.Path(dir_okay=False, writable=True),
    default=None,
    help="Path to the log file. Overrides config. Implies --log-to-file."
)
@click.option(
    '--log-to-file/--no-log-to-file', # Click's way of boolean flags
    'log_to_file_arg', # Use different internal name
    is_flag=True,
    default=None,
    help="Enable/disable logging to a file. Overrides config."
)
@click.version_option(package_name='gcp-inventory-tool') # Assumes package name matches entry point
def cli(
    config_file_path_arg: str,
    projects: Optional[str],
    services: Optional[str],
    output_file: Optional[str],
    output_format_arg: Optional[str],
    max_workers: Optional[int],
    log_level_arg: Optional[str],
    log_file_arg: Optional[str],
    log_to_file_arg: Optional[bool]
):
    """
    GCP Inventory Tool - Collects resource information from GCP projects.

    Fetches inventory for specified services across one or more projects
    and outputs the results in the chosen format.
    CLI arguments override settings in the configuration file.
    """

    # --- Resolve Config Path and Load Config ---
    config_file_path = config_file_path_arg
    if not os.path.isabs(config_file_path):
        config_file_path = os.path.abspath(config_file_path)

    config_data = {} # Initialize empty config
    try:
        config_data = cfg_manager.load_config(config_file_path) # Returns {} if not found
    except Exception as e:
        # Log basic error and exit if config parsing fails
        logging.basicConfig(level=logging.ERROR)
        logging.error(f"Error loading configuration file '{config_file_path}': {e}", exc_info=True)
        sys.exit(1)
    # ------------------------------------------

    # --- Determine Final Settings (CLI > Config > Default) ---
    # Logging
    log_config = config_data.get('logging', {})
    final_log_level_str = log_level_arg or log_config.get('level', 'INFO')
    final_log_to_file = log_to_file_arg if log_to_file_arg is not None else log_config.get('log_to_file', False)
    final_log_file = log_file_arg or log_config.get('log_file', 'logs/app.log')
    if log_file_arg: # If log file path is given via CLI, force enable file logging
        final_log_to_file = True
    final_log_level = getattr(logging, final_log_level_str.upper(), logging.INFO)

    # Projects & Services
    projects_list_cli: Optional[List[str]] = [p.strip() for p in projects.split(',')] if projects else None
    services_list_cli: Optional[List[str]] = [s.strip() for s in services.split(',')] if services else None
    final_projects = projects_list_cli if projects_list_cli is not None else config_data.get('projects', [])
    final_services = services_list_cli if services_list_cli is not None else config_data.get('services', [])

    # Max Workers
    exec_config = config_data.get('execution', {})
    cpu_cores = os.cpu_count()
    default_workers = cpu_cores + 4 if cpu_cores else 10
    final_max_workers = max_workers if max_workers is not None else exec_config.get('max_workers', default_workers)

    # Output Format & File
    output_config = config_data.get('output', {})
    final_output_format = output_format_arg or output_config.get('format', 'console') # Default to console
    final_output_file = output_file or output_config.get('file') # Default to None
    final_format_options = output_config.get('format_options', {}) # Get format options from config
    # ---------------------------------------------------------

    # --- Setup Logging (Once with final settings) ---
    setup_logging(
        level=final_log_level,
        log_to_file=final_log_to_file,
        log_file=final_log_file
    )
    # ------------------------------------------------

    # --- Validate Required Inputs ---
    if not final_projects:
        logger.error("No target projects specified. Provide projects via --projects argument or 'projects' list in config file.")
        sys.exit(1)
    if not final_services:
        logger.warning("No services specified via --services or config file. Inventory will be empty.")
        # Proceed even if services list is empty

    # Validate output file requirement for certain formats
    if final_output_format in ['csv', 'excel'] and not final_output_file:
         logger.error(f"Output format '{final_output_format}' requires an output file. Use --output-file.")
         sys.exit(1)
    if final_output_format == 'console' and final_output_file:
         logger.warning(f"Output file '{final_output_file}' specified, but format is 'console'. Output will be printed to console only.")
    # --------------------------------

    # --- Initialize and Run ---
    try:
        # Initialize Generator with the final determined parameters
        inventory_gen = InventoryGenerator(
            projects=final_projects,
            services=final_services,
            max_workers=final_max_workers,
            output_format=final_output_format,
            output_file=final_output_file,
            format_options=final_format_options
            # config_dict=config_data # Pass config if needed for other settings
        )

        # Run the main inventory process (which now includes formatting)
        inventory_gen.run_inventory()

        logger.info("GCP Inventory Tool finished successfully.")
        sys.exit(0)

    # --- Exception Handling ---
    except FileNotFoundError as e:
        logger.error(f"Configuration Error: Config file issue. {e}")
        sys.exit(1)
    except (ValueError, TypeError) as e: # Catch config validation errors or other value errors
         logger.error(f"Configuration or Input Error: {e}")
         sys.exit(1)
    except google.auth.exceptions.DefaultCredentialsError as e:
         logger.error(f"Authentication Error: {e}")
         logger.error("Please ensure Application Default Credentials (ADC) are configured correctly (e.g., run 'gcloud auth application-default login').")
         sys.exit(1)
    except RuntimeError as e: # Catch initialization errors from InventoryGenerator
         logger.error(f"Initialization Error: {e}")
         sys.exit(1)
    except ImportError as e: # Catch missing dependency errors for formatters
         logger.critical(f"Import Error: Required library might be missing for the selected format '{final_output_format}'. Details: {e}")
         logger.critical("Please ensure all dependencies in requirements.txt are installed (`pip install -r requirements.txt`).")
         sys.exit(1)
    except Exception as e:
        logger.critical(f"An unexpected critical error occurred: {e}", exc_info=True)
        sys.exit(2) # Use a different exit code for unexpected errors

# --- This makes the script executable via entry point ---
if __name__ == "__main__":
    cli()
