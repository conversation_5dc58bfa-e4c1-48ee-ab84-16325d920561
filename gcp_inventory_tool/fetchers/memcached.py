# --- File: gcp_inventory_tool/fetchers/memcached.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import memcache_v1
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso
from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, instance name from a full Memcached resource name
def _parse_memcached_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and instance name from a Memcached resource name."""
    # Format: projects/{project}/locations/{location}/instances/{instance_name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'instances':
            project_id = parts[1]
            location_id = parts[3]
            instance_name = parts[5]
            return project_id, location_id, instance_name
        else:
            logger.warning(f"Unrecognized Memcached instance name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing Memcached instance name '{name}': {e}")
        return None


class MemcachedFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Memorystore for Memcached instance details for a project.
    Lists instances across all locations. Returns one item per instance, with nodes nested.
    """
    SERVICE_NAME = "memcached" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Memcached instances in the specified project across all locations.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Memcached instance fetch...")
        inventory = []
        client = memcache_v1.CloudMemcacheClient(credentials=credentials)
        # Parent path to list instances across all locations
        parent = f"projects/{project_id}/locations/-"

        try:
            instances = client.list_instances(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Memcached instances across locations...")

            for instance in instances:
                parsed_name_parts = _parse_memcached_name(instance.name)
                location = parsed_name_parts[1] if parsed_name_parts else "Unknown"
                short_name = parsed_name_parts[2] if parsed_name_parts else instance.name # Fallback

                # Prepare info dictionary for the instance
                info = {
                    "ProjectID": project_id,
                    "InstanceName": short_name,
                    "CreationDate": timestamp_to_iso(instance.create_time),
                    "UpdationDate": timestamp_to_iso(instance.update_time), # Added field
                    "State": str(instance.state), # READY, CREATING, DELETING etc.
                    "Location": location, # Parsed from name
                    "Cpu": instance.node_config.cpu_count,
                    "MemorySizeGB": instance.node_config.memory_size_mb / 1024.0 if instance.node_config else None,
                    "Endpoint": instance.discovery_endpoint,
                    "Network": get_resource_name(instance.authorized_network),
                    "Version": instance.memcache_full_version,
                    "NodeCount": instance.node_count, # Added field
                    "Zones": list(instance.zones) if instance.zones else [], # Added field
                    "Labels": dict(instance.labels) if instance.labels else {}, # Added field
                    "MemcacheNodes": [], # Populated below
                    "service": self.SERVICE_NAME
                    # Note: PowerShell script had 'ServiceAccount' mapped to persistenceIamIdentity - Memcached doesn't have persistence
                }

                # Process Nodes (PowerShell script created one row per node)
                # Here, we nest node info within the instance
                if instance.memcache_nodes:
                    for node in instance.memcache_nodes:
                        node_info = {
                            "NodeId": node.node_id,
                            "State": str(node.state),
                            "Location": node.zone, # Node specific zone
                            "Host": node.host,
                            "Port": node.port,
                            # Parameters are not directly available on node, only instance level memcache_parameters
                        }
                        info["MemcacheNodes"].append(node_info)

                inventory.append(info)

        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Memcached API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Memcached instances. Ensure API is enabled and necessary roles granted (e.g., 'Cloud Memorystore Memcached Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Memcached instances: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Memcached instance fetch. Found {len(inventory)} instances.")
        return inventory
