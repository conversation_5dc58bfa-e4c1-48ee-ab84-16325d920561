# This file marks the 'fetchers' directory as a Python package.
# It's crucial for discovering and registering service fetcher classes.

import pkgutil
import importlib
import inspect
import logging
from typing import Dict, Type, Optional
from ..core.base_fetcher import ServiceFetcher # Import the base class

logger = logging.getLogger('gcp_inventory')

# Dictionary to hold discovered fetcher classes, mapping SERVICE_NAME to the class.
_FETCHERS: Dict[str, Type[ServiceFetcher]] = {}
_FETCHERS_LOADED = False # Flag to ensure loading happens only once

def _load_fetchers_dynamically():
    """
    Dynamically imports modules in this directory and registers ServiceFetcher subclasses.
    This avoids needing to manually import every fetcher class here.
    """
    global _FETCHERS, _FETCHERS_LOADED
    if _FETCHERS_LOADED:
        return

    logger.info("Dynamically loading service fetchers...")
    fetchers_found = []

    # Iterate over modules in the current package (gcp_inventory_tool.fetchers)
    for _, module_name, _ in pkgutil.iter_modules(__path__, f"{__package__}."):
        try:
            module = importlib.import_module(module_name)
            # Look for classes within the imported module
            for name, obj in inspect.getmembers(module, inspect.isclass):
                # Check if it's a subclass of ServiceFetcher and not the base class itself
                if issubclass(obj, ServiceFetcher) and obj is not ServiceFetcher:
                    if hasattr(obj, 'SERVICE_NAME') and isinstance(obj.SERVICE_NAME, str) and obj.SERVICE_NAME != ServiceFetcher.SERVICE_NAME:
                        service_key = obj.SERVICE_NAME
                        if service_key in _FETCHERS:
                             logger.warning(f"Duplicate fetcher found for service '{service_key}'. Overwriting { _FETCHERS[service_key].__name__} with {obj.__name__}.")
                        _FETCHERS[service_key] = obj
                        fetchers_found.append(f"'{service_key}' ({obj.__name__})")
                    else:
                        logger.warning(f"Fetcher class {obj.__name__} in {module_name} is missing a valid SERVICE_NAME class attribute. Skipping registration.")
        except ImportError as e:
            logger.error(f"Failed to import module {module_name} while discovering fetchers: {e}", exc_info=True)
        except Exception as e:
             logger.error(f"Unexpected error loading fetchers from {module_name}: {e}", exc_info=True)


    if fetchers_found:
        fetchers_list_str = ',\n        '.join(fetchers_found)
        logger.info(f"Registered fetchers for services: {fetchers_list_str}")
    else:
         logger.warning("No service fetchers were found or registered.")

    _FETCHERS_LOADED = True


def get_fetcher(service_name: str) -> Optional[Type[ServiceFetcher]]:
    """
    Retrieves the registered fetcher class for a given service name.

    Args:
        service_name: The unique string identifier for the service (e.g., 'compute', 'storage').

    Returns:
        The corresponding ServiceFetcher subclass, or None if no fetcher
        is registered for that service name.
    """
    if not _FETCHERS_LOADED:
        _load_fetchers_dynamically() # Load fetchers on first call

    fetcher_class = _FETCHERS.get(service_name)
    if not fetcher_class:
         logger.debug(f"No fetcher registered for service name: '{service_name}'")
    return fetcher_class
