# # --- File: gcp_inventory_tool/fetchers/composer_environment.py ---
# import logging
# from typing import List, Dict, Any, Optional, Tuple
# from google.oauth2.credentials import Credentials
# from google.cloud.orchestration.airflow.service_v1 import EnvironmentsClient
# from google.api_core import exceptions as api_exceptions

# from ..utils.resource_name import get_resource_name
# from ..utils.timestamp_to_iso import timestamp_to_iso
# from ..core.base_fetcher import ServiceFetcher

# logger = logging.getLogger('gcp_inventory')

# # Helper function to parse project, location, environment name from a full Composer resource name
# def _parse_composer_name(name: str) -> Optional[Tuple[str, str, str]]:
#     """Parses project, location, and environment name from a Composer resource name."""
#     # Format: projects/{project}/locations/{location}/environments/{env_name}
#     if not isinstance(name, str):
#         logger.warning(f"Invalid name type for parsing: {type(name)}")
#         return None
#     parts = name.split('/')
#     try:
#         if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'environments':
#             project_id = parts[1]
#             location_id = parts[3]
#             env_name = parts[5]
#             return project_id, location_id, env_name
#         else:
#             logger.warning(f"Unrecognized Composer environment name format for parsing: {name}")
#             return None
#     except (ValueError, IndexError) as e:
#         logger.error(f"Error parsing Composer environment name '{name}': {e}")
#         return None


# class ComposerEnvironmentFetcher(ServiceFetcher):
#     """
#     Fetches Google Cloud Composer environment details for a project.
#     Lists environments across all locations.
#     """
#     SERVICE_NAME = "composer_environment" # Unique key for this service type

#     def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
#         """
#         Fetches details for all Composer environments in the specified project across all locations.
#         """
#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud Composer environment fetch...")
#         inventory = []
#         client = EnvironmentsClient(credentials=credentials)
#         # Parent path to list environments across all locations
#         parent = f"projects/{project_id}/locations/-"

#         try:
#             environments = client.list_environments(parent=parent)
#             logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Composer environments across locations...")

#             for env in environments:
#                 parsed_name_parts = _parse_composer_name(env.name)
#                 location = parsed_name_parts[1] if parsed_name_parts else "Unknown"
#                 short_name = parsed_name_parts[2] if parsed_name_parts else env.name # Fallback

#                 # Prepare info dictionary based on PowerShell script's fields
#                 info = {
#                     "ProjectID": project_id,
#                     "Name": short_name,
#                     "Location": location,
#                     "State": str(env.state), # RUNNING, CREATING, DELETING etc.
#                     "CreationDate": timestamp_to_iso(env.create_time),
#                     "UpdationDate": timestamp_to_iso(env.update_time),
#                     "AirflowUri": None,
#                     "DagGcsPrefix": None,
#                     "DatabaseMachineType": None, # Renamed from PS script for clarity
#                     "GKECluster": None,
#                     "NodeDisksizeGB": None,
#                     "NodeZone": None,
#                     "NodeSize": None, # Node machine type
#                     "NodeNetwork": None,
#                     "NodeSubnetwork": None, # Added field
#                     "NodeServiceAccount": None,
#                     "NodeCount": None,
#                     "CloudSQLCIDR": None,
#                     "EnablePrivateEnv": None,
#                     "WebServerIpv4CidrBlock": None,
#                     "WebServerIpv4ReservedRange": None, # This field isn't directly in API response
#                     "MasterIpv4CidrBlock": None, # Added field
#                     "CloudComposerNetworkIpv4CidrBlock": None, # Added field
#                     "AirflowConfigOverrides": {},
#                     "EnvVariables": {},
#                     "ImageVersion": None,
#                     "PypiPackages": {},
#                     "PythonVersion": None,
#                     "WebServerSize": None, # Web server machine type
#                     "Weballowedranges": [], # List of dicts {value, description}
#                     "Labels": dict(env.labels) if env.labels else {}, # Added field
#                     "service": self.SERVICE_NAME
#                 }

#                 # Populate from config object if it exists
#                 if env.config:
#                     config = env.config
#                     info["AirflowUri"] = config.airflow_uri
#                     info["DagGcsPrefix"] = config.dag_gcs_prefix
#                     info["GKECluster"] = get_resource_name(config.gke_cluster)
#                     info["NodeCount"] = config.node_count

#                     if config.node_config:
#                         nc = config.node_config
#                         info["NodeDisksizeGB"] = nc.disk_size_gb
#                         info["NodeZone"] = get_resource_name(nc.location) # Location is the zone URL
#                         info["NodeSize"] = get_resource_name(nc.machine_type)
#                         info["NodeNetwork"] = get_resource_name(nc.network)
#                         info["NodeSubnetwork"] = get_resource_name(nc.subnetwork)
#                         info["NodeServiceAccount"] = nc.service_account
#                         # Add tags, oauth scopes if needed: nc.oauth_scopes, nc.tags

#                     if config.database_config:
#                          # This was mapped to MachineType in PS script, seems incorrect
#                          info["DatabaseMachineType"] = config.database_config.machine_type

#                     if config.web_server_config:
#                          info["WebServerSize"] = config.web_server_config.machine_type

#                     if config.private_environment_config:
#                         pec = config.private_environment_config
#                         info["EnablePrivateEnv"] = pec.enable_private_environment
#                         info["CloudSQLCIDR"] = pec.cloud_sql_ipv4_cidr_block
#                         info["WebServerIpv4CidrBlock"] = pec.web_server_ipv4_cidr_block
#                         info["MasterIpv4CidrBlock"] = pec.master_ipv4_cidr_block # Added
#                         info["CloudComposerNetworkIpv4CidrBlock"] = pec.cloud_composer_network_ipv4_cidr_block # Added

#                     if config.web_server_network_access_control:
#                         wsnac = config.web_server_network_access_control
#                         if wsnac.allowed_ip_ranges:
#                             info["Weballowedranges"] = [
#                                 {"value": r.value, "description": r.description}
#                                 for r in wsnac.allowed_ip_ranges
#                             ]

#                     if config.software_config:
#                         sc = config.software_config
#                         info["AirflowConfigOverrides"] = dict(sc.airflow_config_overrides) if sc.airflow_config_overrides else {}
#                         info["EnvVariables"] = dict(sc.env_variables) if sc.env_variables else {}
#                         info["ImageVersion"] = sc.image_version
#                         info["PypiPackages"] = dict(sc.pypi_packages) if sc.pypi_packages else {}
#                         info["PythonVersion"] = sc.python_version

#                 inventory.append(info)

#         except api_exceptions.NotFound as e:
#              # This might indicate the API isn't enabled
#              logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud Composer API might not be enabled or project not found. Details: {e}")
#              return []
#         except api_exceptions.Forbidden as e:
#             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Composer environments. Ensure API is enabled and necessary roles granted (e.g., 'Composer Viewer'). Details: {e}")
#             return [] # Return empty list on permission errors
#         except Exception as e:
#             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Composer environments: {e}", exc_info=True)
#             return [] # Fail gracefully
#         finally:
#             try:
#                 client.transport.close()
#             except Exception:
#                 pass

#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud Composer environment fetch. Found {len(inventory)} environments.")
#         return inventory

import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud.orchestration.airflow.service_v1 import EnvironmentsClient

from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, environment name from a full Composer resource name
def _parse_composer_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and environment name from a Composer resource name."""
    # Format: projects/{project}/locations/{location}/environments/{env_name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'environments':
            project_id = parts[1]
            location_id = parts[3]
            env_name = parts[5]
            return project_id, location_id, env_name
        else:
            logger.warning(f"Unrecognized Composer environment name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing Composer environment name '{name}': {e}")
        return None


class ComposerEnvironmentFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Composer environment details for a project.
    Lists environments across all locations.
    """
    SERVICE_NAME = "composer_environment" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Composer environments in the specified project across all locations.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud Composer environment fetch...")
        inventory = []
        client = EnvironmentsClient(credentials=credentials)
        
        try:
            # Get all available locations for Composer
            locations = ['us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2', 
                        'europe-west1', 'europe-west2', 'europe-west3', 'asia-east1', 
                        'asia-northeast1', 'asia-south1', 'australia-southeast1']
            logger.info(f"[{project_id}][{self.SERVICE_NAME}] Using location list: {locations}")
            
            # Iterate through each location to fetch environments
            for location in locations:
                try:
                    parent = f"projects/{project_id}/locations/{location}"
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Checking for Composer environments in location: {location}")
                    
                    # List environments in this specific location
                    environments = client.list_environments(parent=parent)
                    
                    for env in environments:
                        parsed_name_parts = _parse_composer_name(env.name)
                        loc = parsed_name_parts[1] if parsed_name_parts else location
                        short_name = parsed_name_parts[2] if parsed_name_parts else env.name # Fallback

                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": short_name,
                            "Location": loc,
                            "State": str(env.state), # RUNNING, CREATING, DELETING etc.
                            "CreationDate": timestamp_to_iso(env.create_time),
                            "UpdationDate": timestamp_to_iso(env.update_time),
                            "AirflowUri": None,
                            "DagGcsPrefix": None,
                            "DatabaseMachineType": None, # Renamed from PS script for clarity
                            "GKECluster": None,
                            "NodeDisksizeGB": None,
                            "NodeZone": None,
                            "NodeSize": None, # Node machine type
                            "NodeNetwork": None,
                            "NodeSubnetwork": None, # Added field
                            "NodeServiceAccount": None,
                            "NodeCount": None,
                            "CloudSQLCIDR": None,
                            "EnablePrivateEnv": None,
                            "WebServerIpv4CidrBlock": None,
                            "WebServerIpv4ReservedRange": None, # This field isn't directly in API response
                            "MasterIpv4CidrBlock": None, # Added field
                            "CloudComposerNetworkIpv4CidrBlock": None, # Added field
                            "AirflowConfigOverrides": {},
                            "EnvVariables": {},
                            "ImageVersion": None,
                            "PypiPackages": {},
                            "PythonVersion": None,
                            "WebServerSize": None, # Web server machine type
                            "Weballowedranges": [], # List of dicts {value, description}
                            "Labels": dict(env.labels) if env.labels else {}, # Added field
                            "service": self.SERVICE_NAME
                        }

                        # Populate from config object if it exists
                        if env.config:
                            config = env.config
                            info["AirflowUri"] = config.airflow_uri
                            info["DagGcsPrefix"] = config.dag_gcs_prefix
                            info["GKECluster"] = get_resource_name(config.gke_cluster)
                            info["NodeCount"] = config.node_count

                            if config.node_config:
                                nc = config.node_config
                                info["NodeDisksizeGB"] = nc.disk_size_gb
                                info["NodeZone"] = get_resource_name(nc.location) # Location is the zone URL
                                info["NodeSize"] = get_resource_name(nc.machine_type)
                                info["NodeNetwork"] = get_resource_name(nc.network)
                                info["NodeSubnetwork"] = get_resource_name(nc.subnetwork)
                                info["NodeServiceAccount"] = nc.service_account
                                # Add tags, oauth scopes if needed: nc.oauth_scopes, nc.tags

                            if config.database_config:
                                 # This was mapped to MachineType in PS script, seems incorrect
                                 info["DatabaseMachineType"] = config.database_config.machine_type

                            if config.web_server_config:
                                 info["WebServerSize"] = config.web_server_config.machine_type

                            if config.private_environment_config:
                                pec = config.private_environment_config
                                info["EnablePrivateEnv"] = pec.enable_private_environment
                                info["CloudSQLCIDR"] = pec.cloud_sql_ipv4_cidr_block
                                info["WebServerIpv4CidrBlock"] = pec.web_server_ipv4_cidr_block
                                info["MasterIpv4CidrBlock"] = pec.master_ipv4_cidr_block # Added
                                info["CloudComposerNetworkIpv4CidrBlock"] = pec.cloud_composer_network_ipv4_cidr_block # Added

                            if config.web_server_network_access_control:
                                wsnac = config.web_server_network_access_control
                                if wsnac.allowed_ip_ranges:
                                    info["Weballowedranges"] = [
                                        {"value": r.value, "description": r.description}
                                        for r in wsnac.allowed_ip_ranges
                                    ]

                            if config.software_config:
                                sc = config.software_config
                                info["AirflowConfigOverrides"] = dict(sc.airflow_config_overrides) if sc.airflow_config_overrides else {}
                                info["EnvVariables"] = dict(sc.env_variables) if sc.env_variables else {}
                                info["ImageVersion"] = sc.image_version
                                info["PypiPackages"] = dict(sc.pypi_packages) if sc.pypi_packages else {}
                                info["PythonVersion"] = sc.python_version

                        inventory.append(info)
                        
                except api_exceptions.NotFound:
                    # Skip if no environments in this location
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] No Composer environments found in location: {location}")
                    continue
                except api_exceptions.Forbidden as e:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Permission denied for location {location}: {e}")
                    continue
                except Exception as e:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error processing location {location}: {e}")
                    continue

        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud Composer API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Composer environments. Ensure API is enabled and necessary roles granted (e.g., 'Composer Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Composer environments: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud Composer environment fetch. Found {len(inventory)} environments.")
        return inventory