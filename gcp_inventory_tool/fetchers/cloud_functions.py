# --- File: gcp_inventory_tool/fetchers/cloud_function.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import functions_v1
from google.cloud import functions_v2
from google.api_core import exceptions as api_exceptions

from ..utils.duration_to_second import duration_to_seconds_str
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


# Helper function to parse project, location, name from a full function name
def _parse_function_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and name from a function resource name."""
    # Format: projects/{project}/locations/{location}/functions/{name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'functions':
            project_id = parts[1]
            location_id = parts[3]
            function_name = parts[5]
            return project_id, location_id, function_name
        else:
            logger.warning(f"Unrecognized function name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing function name '{name}': {e}")
        return None


class CloudFunctionFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Functions (V1 and V2) details for a project.
    Maps details similarly to the provided PowerShell script's output.
    """
    SERVICE_NAME = "cloud_function" # Unique key for this service type

    def _parse_v1_function(self, func: functions_v1.types.CloudFunction, project_id: str) -> Dict[str, Any]:
        """Parses a V1 CloudFunction object into the common dictionary format."""
        parsed_name_parts = _parse_function_name(func.name)
        location = parsed_name_parts[1] if parsed_name_parts else "Unknown"
        short_name = parsed_name_parts[2] if parsed_name_parts else func.name # Fallback

        info = {
            "ProjectID": project_id,
            "Name": short_name,
            "Location": location,
            "Status": str(func.status), # V1 uses enum CloudFunctionStatus
            "Generation": "FIRST",
            "AvailableMemoryMb": func.available_memory_mb,
            "Memory": None, # V1 specific field used
            "CPU": None, # V1 doesn't have explicit CPU config
            "EntryPoint": func.entry_point,
            "IngressSettings": str(func.ingress_settings),
            "MinInstances": func.min_instances if func.min_instances else 0, # Default 0 if not set
            "MaxInstances": func.max_instances,
            "Concurrency": None, # V1 doesn't have explicit concurrency config
            "Runtime": func.runtime,
            "ServiceAccountEmail": func.service_account_email,
            "BuildServiceAccount": func.build_service_account, # V1 specific field name
            "SourceUploadUrl": func.source_upload_url, # V1 specific field name
            "SourceBucket": None, # V1 source info is different
            "SourceObject": None, # V1 source info is different
            "Timeout": duration_to_seconds_str(func.timeout), # V1 uses Duration proto
            "UpdateTime": func.update_time.isoformat() if func.update_time else None,
            "VersionId": str(func.version_id),
            "DockerRegistry": func.docker_registry, # V1 specific field name
            "SecurityLevel": None, # Populated from trigger
            "HTTPSTrigger": None, # Populated from trigger
            "EnvVariables": dict(func.environment_variables) if func.environment_variables else {},
            "SecretEnvVariables": [], # Populated below
            "EventTriggers": [], # Populated below
            "Labels": dict(func.labels) if func.labels else {},
            "VpcConnector": func.vpc_connector,
            "VpcConnectorEgress": str(func.vpc_connector_egress_settings),
            "SecretVolumes": [], # Populated below
            "service": self.SERVICE_NAME
        }

        # HTTPS Trigger V1
        if func.https_trigger:
            info["HTTPSTrigger"] = func.https_trigger.url
            # V1 https_trigger doesn't have security_level directly in the main object

        # Event Trigger V1
        if func.event_trigger:
            et = func.event_trigger
            info["EventTriggers"].append({
                "eventType": et.event_type,
                "resource": et.resource,
                "service": et.service,
                "failurePolicy": str(et.failure_policy.retry) if et.failure_policy else None
            })

        # Secret Env Vars V1
        if func.secret_environment_variables:
            info["SecretEnvVariables"] = [
                {"key": sev.key, "projectId": sev.project_id, "secret": sev.secret, "version": sev.version}
                for sev in func.secret_environment_variables
            ]

        # Secret Volumes V1
        if func.secret_volumes:
             info["SecretVolumes"] = [
                 {"mountPath": sv.mount_path, "projectId": sv.project_id, "secret": sv.secret,
                  "versions": [{"version": v.version, "path": v.path} for v in sv.versions]}
                 for sv in func.secret_volumes
             ]

        return info

    def _parse_v2_function(self, func: functions_v2.types.Function, project_id: str) -> Dict[str, Any]:
        """Parses a V2 Function object into the common dictionary format."""
        parsed_name_parts = _parse_function_name(func.name)
        location = parsed_name_parts[1] if parsed_name_parts else "Unknown"
        short_name = parsed_name_parts[2] if parsed_name_parts else func.name # Fallback

        info = {
            "ProjectID": project_id,
            "Name": short_name,
            "Location": location,
            "Status": str(func.state), # V2 uses enum State
            "Generation": "SECOND",
            "AvailableMemoryMb": None, # V2 uses serviceConfig
            "Memory": None, # Populated from serviceConfig
            "CPU": None, # Populated from serviceConfig
            "EntryPoint": None, # Populated from buildConfig
            "IngressSettings": None, # Populated from serviceConfig
            "MinInstances": 0, # Default 0, populated from serviceConfig
            "MaxInstances": None, # Populated from serviceConfig
            "Concurrency": None, # Populated from serviceConfig
            "Runtime": None, # Populated from buildConfig
            "ServiceAccountEmail": None, # Populated from serviceConfig
            "BuildServiceAccount": None, # Populated from buildConfig
            "SourceUploadUrl": None, # V2 uses buildConfig
            "SourceBucket": None, # Populated from buildConfig
            "SourceObject": None, # Populated from buildConfig
            "Timeout": None, # Populated from serviceConfig
            "UpdateTime": func.update_time.isoformat() if func.update_time else None,
            "VersionId": None, # Populated from serviceConfig revision
            "DockerRegistry": None, # Populated from buildConfig
            "SecurityLevel": None, # V2 doesn't have this field directly
            "HTTPSTrigger": None, # Populated from serviceConfig URI
            "EnvVariables": {}, # Populated from serviceConfig
            "SecretEnvVariables": [], # Populated from serviceConfig
            "EventTriggers": [], # Populated from eventTrigger
            "Labels": dict(func.labels) if func.labels else {},
            "VpcConnector": None, # Populated from serviceConfig
            "VpcConnectorEgress": None, # Populated from serviceConfig
            "SecretVolumes": [], # Populated from serviceConfig
            "service": self.SERVICE_NAME
        }

        # Service Config V2
        if func.service_config:
            sc = func.service_config
            info["Memory"] = sc.available_memory
            info["CPU"] = sc.available_cpu # Added in V2
            info["IngressSettings"] = str(sc.ingress_settings)
            info["MinInstances"] = sc.min_instance_count if sc.min_instance_count else 0
            info["MaxInstances"] = sc.max_instance_count
            info["Concurrency"] = sc.max_instance_request_concurrency
            info["ServiceAccountEmail"] = sc.service_account_email
            info["Timeout"] = str(sc.timeout_seconds) if sc.timeout_seconds else None # V2 uses int seconds
            info["VersionId"] = sc.revision # Closest equivalent to V1 versionId
            info["HTTPSTrigger"] = sc.uri # Main trigger URI
            info["EnvVariables"] = dict(sc.environment_variables) if sc.environment_variables else {}
            info["VpcConnector"] = sc.vpc_connector
            info["VpcConnectorEgress"] = str(sc.vpc_connector_egress_settings)

            # Secret Env Vars V2
            if sc.secret_environment_variables:
                info["SecretEnvVariables"] = [
                    {"key": sev.key, "projectId": sev.project_id, "secret": sev.secret, "version": sev.version}
                    for sev in sc.secret_environment_variables
                ]
            # Secret Volumes V2
            if sc.secret_volumes:
                info["SecretVolumes"] = [
                    {"mountPath": sv.mount_path, "projectId": sv.project_id, "secret": sv.secret,
                     "versions": [{"version": v.version, "path": v.path} for v in sv.versions]}
                    for sv in sc.secret_volumes
                ]

        # Build Config V2
        if func.build_config:
            bc = func.build_config
            info["EntryPoint"] = bc.entry_point
            info["Runtime"] = bc.runtime
            info["BuildServiceAccount"] = bc.service_account # V2 specific field name
            info["DockerRegistry"] = bc.docker_registry # V2 specific field name
            if bc.source and bc.source.storage_source:
                ss = bc.source.storage_source
                info["SourceBucket"] = ss.bucket
                info["SourceObject"] = ss.object_
                # Construct a potential URL (approximates V1 SourceUploadUrl)
                info["SourceUploadUrl"] = f"https://storage.googleapis.com/{ss.bucket}/{ss.object_}"


        # Event Trigger V2
        if func.event_trigger:
            et = func.event_trigger
            # V2 event trigger structure is slightly different
            trigger_info = {
                "triggerRegion": et.trigger_region,
                "eventType": et.event_type,
                "retryPolicy": str(et.retry_policy),
                "serviceAccountEmail": et.service_account_email,
                "channel": et.channel,
                "pubsubTopic": et.pubsub_topic,
                # Extract filters if needed: dict(et.event_filters)
            }
            info["EventTriggers"].append(trigger_info)


        return info


    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud Functions (V1 & V2) in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud Function fetch (V1 & V2)...")
        inventory = []
        # Parent path for listing across all regions
        parent = f"projects/{project_id}/locations/-"

        # --- Fetch V1 Functions ---
        v1_client = None
        try:
            logger.info(f"[{project_id}][{self.SERVICE_NAME}] Fetching V1 functions...")
            v1_client = functions_v1.CloudFunctionsServiceClient(credentials=credentials)
            request_v1 = functions_v1.ListFunctionsRequest(parent=parent)
            functions_v1_list = v1_client.list_functions(request=request_v1)

            count_v1 = 0
            for func_v1 in functions_v1_list:
                inventory.append(self._parse_v1_function(func_v1, project_id))
                count_v1 += 1
            logger.info(f"[{project_id}][{self.SERVICE_NAME}] Found {count_v1} V1 functions.")

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing V1 functions: {e}")
            # Continue to V2 fetch
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list V1 functions: {e}", exc_info=True)
            # Continue to V2 fetch
        finally:
            if v1_client:
                v1_client.transport.close()

        # --- Fetch V2 Functions ---
        v2_client = None
        try:
            logger.info(f"[{project_id}][{self.SERVICE_NAME}] Fetching V2 functions...")
            v2_client = functions_v2.FunctionServiceClient(credentials=credentials)
            request_v2 = functions_v2.ListFunctionsRequest(parent=parent)
            functions_v2_list = v2_client.list_functions(request=request_v2)

            count_v2 = 0
            for func_v2 in functions_v2_list:
                inventory.append(self._parse_v2_function(func_v2, project_id))
                count_v2 += 1
            logger.info(f"[{project_id}][{self.SERVICE_NAME}] Found {count_v2} V2 functions.")

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing V2 functions: {e}")
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list V2 functions: {e}", exc_info=True)
        finally:
             if v2_client:
                v2_client.transport.close()

        # --- Final logging ---
        # Note: The API might require specific permissions (e.g., cloudfunctions.functions.list)
        # and the Cloud Functions API to be enabled.
        if not inventory and (count_v1 == 0 and count_v2 == 0):
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] No V1 or V2 functions found. Check API enablement and permissions.")

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud Function fetch. Found total {len(inventory)} functions (V1+V2).")
        return inventory
