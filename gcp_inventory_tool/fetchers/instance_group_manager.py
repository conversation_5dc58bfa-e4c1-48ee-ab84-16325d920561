# # --- File: gcp_inventory_tool/fetchers/instance_group_manager.py ---
# import logging
# from typing import List, Dict, Any, Optional
# from google.oauth2.credentials import Credentials
# from google.cloud import compute_v1
# from google.api_core import exceptions as api_exceptions

# from ..utils.resource_name import get_resource_name
# from ..core.base_fetcher import ServiceFetcher

# logger = logging.getLogger('gcp_inventory')


# class InstanceGroupManagerFetcher(ServiceFetcher):
#     """
#     Fetches Google Cloud Instance Group Manager (Managed Instance Group) details.
#     """
#     SERVICE_NAME = "instance_group_manager" # Unique key for this service type

#     def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
#         """
#         Fetches details for all Instance Group Managers in the specified project across all zones/regions.
#         """
#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Instance Group Manager fetch...")
#         inventory = []
#         client = compute_v1.InstanceGroupManagersClient(credentials=credentials)

#         try:
#             # Use aggregated list to get managers from all zones and regions
#             agg_list = client.aggregated_list(project=project_id)
#             logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing managers across zones/regions...")

#             # The response is an iterator of tuples (scope, response_object)
#             # Scope is like "zones/us-central1-a" or "regions/us-central1"
#             for scope, response in agg_list:
#                 managers = []
#                 if response.instance_group_managers:
#                     managers = response.instance_group_managers

#                 if managers:
#                     scope_name = get_resource_name(scope) # Zone or Region name
#                     is_regional = "regions" in scope.lower()
#                     logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(managers)} managers in scope {scope_name}...")

#                     for igm in managers:
#                         # Prepare info dictionary based on PowerShell script's fields
#                         info = {
#                             "ProjectID": project_id,
#                             "Name": igm.name,
#                             "Location": scope_name,
#                             "ScopeType": "REGIONAL" if is_regional else "ZONAL", # Added field
#                             "CreationDate": igm.creation_timestamp,
#                             "BaseInstanceName": igm.base_instance_name,
#                             "InstanceGroup": get_resource_name(igm.instance_group),
#                             "InstanceTemplate": get_resource_name(igm.instance_template),
#                             "Size": None, # Current size requires different API call (list_managed_instances)
#                             "TargetSize": igm.target_size,
#                             "Autoscaled": True if igm.auto_healer_status else False, # Check if autoscaler link exists
#                             "UpdatePolicyType": None,
#                             "ReplacementMethod": None,
#                             "MinimalAction": None,
#                             "MaxSurge": None,
#                             "MaxUnavailable": None,
#                             "Id": str(igm.id), # Added ID
#                             "service": self.SERVICE_NAME
#                         }

#                         # Process Update Policy
#                         if igm.update_policy:
#                             up = igm.update_policy
#                             info["UpdatePolicyType"] = str(up.type_) if up.type_ else None
#                             info["ReplacementMethod"] = str(up.replacement_method) if hasattr(up, 'replacement_method') and up.replacement_method else None # Newer field
#                             info["MinimalAction"] = str(up.minimal_action) if up.minimal_action else None

#                             # Max Surge / Unavailable can be fixed or percent
#                             if up.max_surge:
#                                 info["MaxSurge"] = up.max_surge.fixed or up.max_surge.percent or up.max_surge.calculated # Calculated is often populated
#                             if up.max_unavailable:
#                                 info["MaxUnavailable"] = up.max_unavailable.fixed or up.max_unavailable.percent or up.max_unavailable.calculated

#                         inventory.append(info)

#         except api_exceptions.Forbidden as e:
#             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Instance Group Managers or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Viewer). Details: {e}")
#             return [] # Return empty list on permission errors
#         except api_exceptions.NotFound as e:
#              logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
#              return [] # API not enabled or project issue
#         except Exception as e:
#             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Instance Group Managers: {e}", exc_info=True)
#             return [] # Fail gracefully
#         finally:
#             try:
#                 client.transport.close()
#             except Exception:
#                 pass

#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Instance Group Manager fetch. Found {len(inventory)} managers.")
#         return inventory

import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class InstanceGroupManagerFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Instance Group Manager (Managed Instance Group) details.
    """
    SERVICE_NAME = "instance_group_manager" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Instance Group Managers in the specified project across all zones/regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Instance Group Manager fetch...")
        inventory = []
        client = compute_v1.InstanceGroupManagersClient(credentials=credentials)
        
        # Create autoscaler client to check which instance groups are autoscaled
        autoscaler_client = compute_v1.AutoscalersClient(credentials=credentials)

        try:
            # Get all autoscalers to determine which instance groups are autoscaled
            autoscaled_igms = set()
            try:
                # Get all autoscalers across all zones (aggregated list)
                autoscalers_request = autoscaler_client.aggregated_list(project=project_id)
                
                for _, response in autoscalers_request:
                    if response.autoscalers:
                        for autoscaler in response.autoscalers:
                            # Add the target (which is an IGM) to our set of autoscaled IGMs
                            if autoscaler.target:
                                autoscaled_igms.add(autoscaler.target)
                
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Found {len(autoscaled_igms)} autoscaled instance groups")
            except Exception as e:
                logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Failed to fetch autoscalers: {e}")
                # Continue with the rest of the code - we'll just mark all as not autoscaled

            # Use aggregated list to get managers from all zones and regions
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing managers across zones/regions...")

            # The response is an iterator of tuples (scope, response_object)
            # Scope is like "zones/us-central1-a" or "regions/us-central1"
            for scope, response in agg_list:
                managers = []
                if response.instance_group_managers:
                    managers = response.instance_group_managers

                if managers:
                    scope_name = get_resource_name(scope) # Zone or Region name
                    is_regional = "regions" in scope.lower()
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(managers)} managers in scope {scope_name}...")

                    for igm in managers:
                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": igm.name,
                            "Location": scope_name,
                            "ScopeType": "REGIONAL" if is_regional else "ZONAL", # Added field
                            "CreationDate": igm.creation_timestamp,
                            "BaseInstanceName": igm.base_instance_name,
                            "InstanceGroup": get_resource_name(igm.instance_group),
                            "InstanceTemplate": get_resource_name(igm.instance_template),
                            "Size": None, # Current size requires different API call (list_managed_instances)
                            "TargetSize": igm.target_size,
                            "Autoscaled": igm.self_link in autoscaled_igms, # Check if this IGM is in our autoscaled set
                            "UpdatePolicyType": None,
                            "ReplacementMethod": None,
                            "MinimalAction": None,
                            "MaxSurge": None,
                            "MaxUnavailable": None,
                            "Id": str(igm.id), # Added ID
                            "service": self.SERVICE_NAME
                        }

                        # Process Update Policy
                        if igm.update_policy:
                            up = igm.update_policy
                            info["UpdatePolicyType"] = str(up.type_) if up.type_ else None
                            info["ReplacementMethod"] = str(up.replacement_method) if hasattr(up, 'replacement_method') and up.replacement_method else None # Newer field
                            info["MinimalAction"] = str(up.minimal_action) if up.minimal_action else None

                            # Max Surge / Unavailable can be fixed or percent
                            if up.max_surge:
                                info["MaxSurge"] = up.max_surge.fixed or up.max_surge.percent or up.max_surge.calculated # Calculated is often populated
                            if up.max_unavailable:
                                info["MaxUnavailable"] = up.max_unavailable.fixed or up.max_unavailable.percent or up.max_unavailable.calculated

                        inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Instance Group Managers or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Viewer). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Instance Group Managers: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                # Close both clients
                client.transport.close()
                autoscaler_client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Instance Group Manager fetch. Found {len(inventory)} managers.")
        return inventory