# --- File: gcp_inventory_tool/fetchers/log_sink.py ---
import logging
from typing import List, Dict, Any
from google.oauth2.credentials import Credentials
from google.cloud import logging_v2
from google.api_core import exceptions as api_exceptions

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

class LogSinkFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Logging Sink details for a project.
    """
    SERVICE_NAME = "log_sink" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Logging Sinks in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Logging Sink fetch...")
        inventory = []
        client = logging_v2.services.config_service_v2.ConfigServiceV2Client(credentials=credentials)
        parent = f"projects/{project_id}"

        try:
            sinks = client.list_sinks(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing logging sinks...")

            for sink in sinks:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": sink.name,
                    "Destination": sink.destination,
                    # Apply the specific filter replacement from PowerShell
                    "Filter": sink.filter.replace(")", ") ; ") if sink.filter else None,
                    "Description": sink.description, # Added field
                    "Disabled": sink.disabled, # Added field
                    "IncludeChildren": sink.include_children, # Added field
                    "WriterIdentity": sink.writer_identity, # Added field
                    "OutputVersionFormat": str(sink.output_version_format), # Added field
                    "service": self.SERVICE_NAME
                }
                inventory.append(info)

        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Logging API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Logging Sinks. Ensure API is enabled and necessary roles granted (e.g., 'Logging Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Logging Sinks: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Logging Sink fetch. Found {len(inventory)} sinks.")
        return inventory
