# --- File: gcp_inventory_tool/fetchers/dataflow_job.py ---

# --- File: gcp_inventory_tool/fetchers/vpc_connector.py ---

import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import vpcaccess_v1
from google.api_core import exceptions as api_exceptions
from google.cloud import compute_v1

from ..core.base_fetcher import ServiceFetcher
from ..utils.get_regions import get_available_regions  # Import the utility function

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, name from a full connector name
def _parse_connector_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and name from a connector resource name."""
    # Format: projects/{project}/locations/{location}/connectors/{name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'connectors':
            project_id = parts[1]
            location_id = parts[3]
            connector_name = parts[5]
            return project_id, location_id, connector_name
        else:
            logger.warning(f"Unrecognized connector name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing connector name '{name}': {e}")
        return None

class VpcConnectorFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Serverless VPC Access Connector details for a project.
    Lists connectors across dynamically fetched available regions.
    """
    SERVICE_NAME = "vpc_connector" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Serverless VPC Access Connectors in the specified project across available regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting VPC Access Connector fetch...")
        inventory = []
        client = None  # Initialize client
        regions_to_check = [] # Initialize regions list

        try: # Outer try block
            client = vpcaccess_v1.VpcAccessServiceClient(credentials=credentials)

            # --- Dynamically fetch regions ---
            regions_to_check = get_available_regions(project_id, credentials)
            if not regions_to_check:
                logger.warning(f"[{project_id}][{self.SERVICE_NAME}] No regions found or fetched. Skipping VPC Connector check.")
                if client:
                    client.transport.close()
                return []
            # ---------------------------------

            for region in regions_to_check:
                # Indentation level 1
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Checking region {region} for VPC Access Connectors...")
                try: # Inner try for region
                    parent = f"projects/{project_id}/locations/{region}"
                    connectors = client.list_connectors(parent=parent, page_size=100) # Add page_size
                    # No need to check for None, the client.list_connectors()  returns an iterable, even if empty

                    for connector in connectors:
                        # Indentation level 2
                        parsed_name_parts = _parse_connector_name(connector.name)
                        connector_location = parsed_name_parts[1] if parsed_name_parts else region
                        short_name = parsed_name_parts[2] if parsed_name_parts else connector.name

                        # Prepare info dictionary
                        info = {
                            "ProjectID": project_id,
                            "Name": short_name,
                            "Network": connector.network,
                            "State": str(connector.state),
                            "IpCidrRange": connector.ip_cidr_range,
                            "Subnet": connector.subnet.name if connector.subnet else None,
                            "MachineType": connector.machine_type,
                            "MaxInstances": connector.max_instances,
                            "MinInstances": connector.min_instances,
                            "MaxThroughput": connector.max_throughput,
                            "MinThroughput": connector.min_throughput,
                            "Location": connector_location,
                            "ConnectedProjects": list(connector.connected_projects) if connector.connected_projects else [],
                            "service": self.SERVICE_NAME
                        }
                        inventory.append(info)

                except api_exceptions.NotFound as e:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] VPC Access API/Region not found or enabled for region '{region}'. Details: {e}")
                except api_exceptions.Forbidden as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing VPC Access Connectors in region '{region}'. Details: {e}")
                except Exception as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process VPC Access Connectors in region '{region}': {e}", exc_info=True)
                # Continue to the next region

        except Exception as e: # Outer Exception
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Unexpected error during VPC Connector fetch setup or region iteration: {e}", exc_info=True)
        finally:
            if client:
                try:
                    client.transport.close()
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] VPC Access client transport closed.")
                except Exception as close_e:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error closing VPC Access client transport: {close_e}")

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished VPC Access Connector fetch across all regions. Found {len(inventory)} connectors.")
        return inventory
