# --- File: gcp_inventory_tool/fetchers/route.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger(__name__)


class RouteFetcher(ServiceFetcher):
    """
    Fetches Google Cloud VPC route details for a project.
    """
    SERVICE_NAME = "route" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all VPC routes in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting VPC route fetch...")
        inventory = []
        client = compute_v1.RoutesClient(credentials=credentials)

        try:
            # List all routes in the project
            routes = client.list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing routes...")

            for route in routes:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": route.name,
                    "CreationDate": route.creation_timestamp,
                    "Description": route.description,
                    "DestRange": route.dest_range,
                    "Network": get_resource_name(route.network),
                    "NextHopGateway": get_resource_name(route.next_hop_gateway),
                    "NextHopInstance": get_resource_name(route.next_hop_instance),
                    "NextHopIp": route.next_hop_ip,
                    "NextHopNetwork": get_resource_name(route.next_hop_network),
                    "NextHopPeering": route.next_hop_peering,
                    "NextHopVpnTunnel": get_resource_name(route.next_hop_vpn_tunnel),
                    # Handle potential next_hop_ilb field if needed (newer feature)
                    # "NextHopIlb": get_resource_name(route.next_hop_ilb),
                    "Priority": route.priority,
                    "Tags": list(route.tags) if route.tags else [], # Return as list
                    "Warnings": [], # Populated below
                    "service": self.SERVICE_NAME
                }

                # Extract warnings if present
                if route.warnings:
                    info["Warnings"] = [
                        {"code": w.code, "message": w.message, "data": dict(w.data)}
                        for w in route.warnings
                    ]

                # Note: Filtering based on description (like in the PowerShell script)
                # is NOT applied here. The full list is returned.
                # Filtering can be done downstream using the 'Description' field.
                # Example filter condition:
                # if not ("Default local route" in info["Description"] or \
                #         "Auto generated route via peering" in info["Description"]):
                #     inventory.append(info)
                # else:
                #     logger.debug(f"Skipping default/peering route: {info['Name']}")

                inventory.append(info) # Append all routes by default

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing routes or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process VPC routes: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            # Ensure the client is closed
            if client:
                client.transport.close()

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished VPC route fetch. Found {len(inventory)} routes.")
        return inventory
