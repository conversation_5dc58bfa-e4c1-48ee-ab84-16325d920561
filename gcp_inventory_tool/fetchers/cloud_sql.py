# --- File: gcp_inventory_tool/fetchers/cloud_sql.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.api_core import exceptions as api_exceptions
# Use googleapiclient for the SQL Admin API v1beta4
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

class CloudSqlInstanceFetcher(ServiceFetcher):
    """
    Fetches Google Cloud SQL instance details for a project.
    """
    SERVICE_NAME = "cloud_sql" # Unique key for this service type

    def _format_backup_config(self, config: Optional[Dict]) -> Optional[str]:
        """Formats backup configuration into a string similar to PowerShell."""
        if not config:
            return None
        parts = []
        if 'enabled' in config:
            parts.append(f"Enabled: {config['enabled']}")
        if config.get('backupRetentionSettings'):
            brs = config['backupRetentionSettings']
            parts.append(f"RetainBackups: {brs.get('retainedBackups')}")
            parts.append(f"RetentionUnit: {brs.get('retentionUnit')}")
        if 'binaryLogEnabled' in config:
            parts.append(f"BinaryLogging: {config['binaryLogEnabled']}")
        if 'location' in config:
             parts.append(f"Location: {config['location']}")
        if 'startTime' in config:
             parts.append(f"StartTime: {config['startTime']}")
        if 'transactionLogRetentionDays' in config:
            parts.append(f"TsLogRetentionDays: {config['transactionLogRetentionDays']}")
        return " ; ".join(parts) if parts else None

    def _format_ip_addresses(self, addresses: Optional[List[Dict]]) -> Tuple[List[str], List[str]]:
        """Formats IP addresses and types into separate lists."""
        ips = []
        types = []
        if addresses:
            for addr in addresses:
                ips.append(addr.get('ipAddress', ''))
                types.append(addr.get('type', ''))
        return ips, types

    def _format_auth_networks(self, networks: Optional[List[Dict]]) -> List[str]:
         """Formats authorized networks into a list of strings."""
         formatted = []
         if networks:
             for net in networks:
                 name = net.get('name', '')
                 value = net.get('value', '')
                 formatted.append(f"{name} : {value}")
         return formatted

    def _format_maintenance_window(self, window: Optional[Dict]) -> Optional[str]:
        """Formats maintenance window."""
        if not window:
            return None
        day = window.get('day')
        hour = window.get('hour')
        return f"Day: {day} Hour: {hour}" if day is not None and hour is not None else None


    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud SQL instances in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud SQL instance fetch...")
        inventory = []
        service = None

        try:
            # Build the SQL Admin service object
            service = build('sqladmin', 'v1beta4', credentials=credentials, cache_discovery=False)

            # Prepare the request to list instances
            request = service.instances().list(project=project_id)

            # The list API for SQL Admin doesn't use pagination tokens in the same way.
            # It returns all items in the 'items' list.
            response = request.execute()
            instances = response.get('items', [])
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(instances)} Cloud SQL instances...")

            for instance in instances:
                settings = instance.get('settings', {})
                ip_config = settings.get('ipConfiguration', {})

                ips, ip_types = self._format_ip_addresses(instance.get('ipAddresses'))

                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": instance.get('name'),
                    "Type": instance.get('databaseVersion'), # e.g., MYSQL_8_0, POSTGRES_15
                    "Location": instance.get('region'),
                    "InstanceSize": settings.get('tier'),
                    "PrimaryZone": instance.get('gceZone'),
                    "SecondaryZone": instance.get('secondaryGceZone'),
                    "State": instance.get('state'), # RUNNABLE, SUSPENDED, PENDING_CREATE etc.
                    "BackendType": instance.get('backendType'), # FIRST_GEN, SECOND_GEN, EXTERNAL
                    "ConnectionName": instance.get('connectionName'),
                    "FailoverReplicaAvailable": instance.get('failoverReplica', {}).get('available'),
                    "IPAddress": ips, # List of IPs
                    "IPType": ip_types, # List of corresponding types (PRIMARY, PRIVATE, OUTGOING)
                    "ServiceAccount": instance.get('serviceAccountEmailAddress'),
                    "ActivationPolicy": settings.get('activationPolicy'), # ALWAYS, NEVER, ON_DEMAND
                    "AvailabilityType": settings.get('availabilityType'), # ZONAL, REGIONAL
                    "BackupConfiguration": self._format_backup_config(settings.get('backupConfiguration')),
                    "DiskSizeGB": settings.get('dataDiskSizeGb'),
                    "DiskType": settings.get('dataDiskType'), # PD_SSD, PD_HDD
                    "AuthorizedNetworks": self._format_auth_networks(ip_config.get('authorizedNetworks')),
                    "Ipv4Enabled": ip_config.get('ipv4Enabled'),
                    "PrivateNetwork": get_resource_name(ip_config.get('privateNetwork')), # Extract name
                    "LocationPreference": settings.get('locationPreference', {}).get('zone'),
                    "MaintenanceWindow": self._format_maintenance_window(settings.get('maintenanceWindow')),
                    "PricingPlan": settings.get('pricingPlan'), # PER_USE or PACKAGE
                    "ReplicationType": settings.get('replicationType'), # SYNCHRONOUS or ASYNCHRONOUS
                    "StorageAutoResize": settings.get('storageAutoResize'),
                    "StorageAutoResizeLimit": settings.get('storageAutoResizeLimit'),
                    "service": self.SERVICE_NAME
                }
                inventory.append(info)

        except HttpError as e:
             logger.error(f"[{project_id}][{self.SERVICE_NAME}] HttpError accessing SQL Admin API: {e}")
             # Check for common issues like API not enabled
             if "service not found" in str(e).lower() or "service has been disabled" in str(e).lower() or "enable the api" in str(e).lower():
                  logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud SQL Admin API might not be enabled for this project.")
             elif e.resp.status == 403:
                  logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Cloud SQL instances. Ensure necessary roles granted (e.g., 'Cloud SQL Viewer').")
             return []
        except api_exceptions.Forbidden as e:
            # This might be caught if the initial API build fails
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Cloud SQL instances. Ensure API is enabled and necessary roles granted (e.g., 'Cloud SQL Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Cloud SQL Instances: {e}", exc_info=True)
            return [] # Fail gracefully
        # No explicit client.close() needed for googleapiclient discovery client

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud SQL instance fetch. Found {len(inventory)} instances.")
        return inventory
