# --- File: gcp_inventory_tool/fetchers/pubsub_topic.py ---
import logging
from typing import List, Dict, Any
from google.oauth2.credentials import Credentials
from google.cloud import pubsub_v1
from google.api_core import exceptions as api_exceptions

from ..utils.duration_to_second import duration_to_seconds_str
from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class PubSubTopicFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Pub/Sub topic details for a project.
    Maps details similarly to the provided PowerShell script's output.
    """
    SERVICE_NAME = "pubsub_topic" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Pub/Sub topics in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Pub/Sub topic fetch...")
        inventory = []
        project_path = f"projects/{project_id}"

        # Use a context manager for the client to ensure it's always closed properly.
        try:
            with pubsub_v1.PublisherClient(credentials=credentials) as publisher_client:
                # Add a timeout for robustness, preventing DeadlineExceeded errors on large projects.
                topic_pager = publisher_client.list_topics(
                    request={"project": project_path},
                    timeout=240.0 # Set a generous timeout (e.g., 4 minutes)
                )

                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing topics...")

                # This logic is efficient as it iterates over full Topic objects.
                for topic in topic_pager:
                    info = {
                        "ProjectID": project_id,
                        "Name": get_resource_name(topic.name),
                        "MessageRetentionDuration": duration_to_seconds_str(topic.message_retention_duration),
                        "Schema": None,
                        "Encoding": None,
                        "Labels": dict(topic.labels) if topic.labels else {}, # Ensure it's a dict
                        "service": self.SERVICE_NAME
                    }

                    # Schema Settings
                    if topic.schema_settings and topic.schema_settings.schema:
                        info["Schema"] = get_resource_name(topic.schema_settings.schema)
                        info["Encoding"] = pubsub_v1.types.Encoding(topic.schema_settings.encoding).name

                    inventory.append(info)

        except api_exceptions.DeadlineExceeded as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Timed out while listing topics. The timeout is set to 240 seconds. This may indicate an issue with the Pub/Sub API or an extremely large number of topics. Details: {e}")
            return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing topics. Ensure 'Pub/Sub API' is enabled and the service account has 'Pub/Sub Viewer' role. Details: {e}")
            return []
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Pub/Sub API might not be enabled or project not found. Details: {e}")
             return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Pub/Sub topics: {e}", exc_info=True)
            return []

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Pub/Sub topic fetch. Found {len(inventory)} topics.")
        return inventory