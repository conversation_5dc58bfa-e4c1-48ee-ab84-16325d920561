# --- File: gcp_inventory_tool/fetchers/cloud_build_trigger.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud.devtools import cloudbuild_v1
from google.api_core import exceptions as api_exceptions

from ..utils.duration_to_second import duration_to_seconds_str
from ..utils.resource_name import get_resource_name
from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, trigger name from a full trigger resource name
def _parse_trigger_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and trigger name from a trigger resource name."""
    # Format: projects/{project}/locations/{location}/triggers/{trigger_id}
    # Note: Triggers listed at project level might implicitly be global or regional.
    # The location field within the trigger object might be more reliable if present.
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'triggers':
            project_id = parts[1]
            location_id = parts[3] # Often 'global' when listed at project level
            trigger_id = parts[5]
            return project_id, location_id, trigger_id
        elif len(parts) == 4 and parts[0] == 'projects' and parts[2] == 'triggers': # Older format?
             project_id = parts[1]
             trigger_id = parts[3]
             return project_id, "global", trigger_id # Assume global if location missing
        else:
            logger.warning(f"Unrecognized trigger name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing trigger name '{name}': {e}")
        return None


class CloudBuildTriggerFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Build Trigger details for a project.
    """
    SERVICE_NAME = "cloud_build_trigger" # Unique key for this service type

    def _extract_source_details(self, trigger: cloudbuild_v1.types.BuildTrigger) -> Dict[str, Any]:
        """Extracts details about the trigger source."""
        source_info = {"type": "Unknown"}
        if trigger.github:
            source_info["type"] = "GITHUB"
            source_info["owner"] = trigger.github.owner
            source_info["name"] = trigger.github.name
            source_info["push_branch"] = trigger.github.push.branch if trigger.github.push else None
            source_info["pr_branch"] = trigger.github.pull_request.branch if trigger.github.pull_request else None
        elif trigger.git_file_source:
            source_info["type"] = "GIT_FILE_SOURCE" # e.g., Cloud Source Repositories
            source_info["uri"] = trigger.git_file_source.uri
            source_info["repo_type"] = str(trigger.git_file_source.repo_type)
            source_info["revision"] = trigger.git_file_source.revision
            source_info["path"] = trigger.git_file_source.path
        elif trigger.trigger_template: # Older field for repo source?
             source_info["type"] = "REPO_SOURCE" # (Legacy?)
             source_info["repo_name"] = trigger.trigger_template.repo_name
             source_info["branch_name"] = trigger.trigger_template.branch_name
             source_info["tag_name"] = trigger.trigger_template.tag_name
             source_info["commit_sha"] = trigger.trigger_template.commit_sha
        # Add other source types like BitbucketServerTriggerConfig if needed
        return source_info

    def _extract_build_details(self, build_def: Optional[cloudbuild_v1.types.Build]) -> Dict[str, Any]:
         """Extracts key details from the inline build definition."""
         if not build_def:
             return {}
         return {
             "steps": [{"name": step.name, "args": list(step.args)} for step in build_def.steps],
             "images": list(build_def.images) if build_def.images else [],
             "timeout": duration_to_seconds_str(build_def.timeout),
             "tags": list(build_def.tags) if build_def.tags else [],
             "logs_bucket": build_def.logs_bucket,
             "queue_ttl": duration_to_seconds_str(build_def.queue_ttl),
             # Add options like logging, pool etc. if needed
         }


    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud Build Triggers in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud Build Trigger fetch...")
        inventory = []
        client = cloudbuild_v1.CloudBuildClient(credentials=credentials)

        try:
            # List triggers for the project. Location defaults to global if not specified.
            # Use location='-' to attempt listing across all regions, though triggers
            # might primarily be global or associated with the source repo location.
            # Let's stick to project_id only first, as per gcloud default.
            request = cloudbuild_v1.ListBuildTriggersRequest(project_id=project_id)
            triggers = client.list_build_triggers(request=request)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Cloud Build Triggers...")

            for trigger in triggers:
                parsed_name_parts = _parse_trigger_name(trigger.name)
                # Use location from parsed name, default to 'global' if parsing fails or location is '-'
                location = "global"
                short_name = trigger.name # Fallback
                if parsed_name_parts:
                    location = parsed_name_parts[1] if parsed_name_parts[1] != '-' else "global"
                    short_name = parsed_name_parts[2]

                # Prepare info dictionary
                info = {
                    "ProjectID": project_id,
                    "Name": short_name, # Trigger display name or ID if display name is empty
                    "ID": trigger.id, # Unique trigger ID
                    "Location": location, # Usually 'global'
                    "Description": trigger.description,
                    "CreationDate": timestamp_to_iso(trigger.create_time),
                    "Disabled": trigger.disabled,
                    "Tags": list(trigger.tags) if trigger.tags else [],
                    "Substitutions": dict(trigger.substitutions) if trigger.substitutions else {},
                    "IgnoredFiles": list(trigger.ignored_files) if trigger.ignored_files else [],
                    "IncludedFiles": list(trigger.included_files) if trigger.included_files else [],
                    "Filename": trigger.filename, # Path to build config file (e.g., cloudbuild.yaml)
                    "ServiceAccount": get_resource_name(trigger.service_account), # Extract SA name
                    "Source": self._extract_source_details(trigger),
                    "BuildDefinition": None, # Populated below (inline or filename)
                    "Filter": trigger.filter, # Added field for event filtering
                    "service": self.SERVICE_NAME
                }

                # Check for inline build definition vs. filename reference
                if trigger.build:
                     info["BuildDefinition"] = {"type": "INLINE", "details": self._extract_build_details(trigger.build)}
                elif trigger.filename:
                     info["BuildDefinition"] = {"type": "FILE", "path": trigger.filename}
                else:
                     info["BuildDefinition"] = {"type": "UNKNOWN"}


                inventory.append(info)

        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud Build API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Cloud Build Triggers. Ensure API is enabled and necessary roles granted (e.g., 'Cloud Build Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Cloud Build Triggers: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud Build Trigger fetch. Found {len(inventory)} triggers.")
        return inventory
