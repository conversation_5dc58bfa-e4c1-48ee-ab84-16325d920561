# --- File: gcp_inventory_tool/fetchers/redis.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import redis_v1
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso
from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, instance name from a full Redis resource name
def _parse_redis_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and instance name from a Redis resource name."""
    # Format: projects/{project}/locations/{location}/instances/{instance_name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'instances':
            project_id = parts[1]
            location_id = parts[3]
            instance_name = parts[5]
            return project_id, location_id, instance_name
        else:
            logger.warning(f"Unrecognized Redis instance name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing Redis instance name '{name}': {e}")
        return None


class RedisFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Memorystore for Redis instance details for a project.
    Lists instances across all locations.
    """
    SERVICE_NAME = "redis" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Redis instances in the specified project across all locations.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Redis instance fetch...")
        inventory = []
        client = redis_v1.CloudRedisClient(credentials=credentials)
        # Parent path to list instances across all locations
        parent = f"projects/{project_id}/locations/-"

        try:
            instances = client.list_instances(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Redis instances across locations...")

            for instance in instances:
                parsed_name_parts = _parse_redis_name(instance.name)
                location = parsed_name_parts[1] if parsed_name_parts else instance.location_id # Use location_id field
                short_name = parsed_name_parts[2] if parsed_name_parts else instance.name # Fallback

                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": short_name,
                    "CreationDate": timestamp_to_iso(instance.create_time),
                    "State": str(instance.state), # READY, CREATING, DELETING etc.
                    "Location": location, # Use instance.location_id or parsed name
                    "Tier": str(instance.tier), # BASIC, STANDARD_HA
                    "Host": instance.host,
                    "MemorySizeGB": instance.memory_size_gb,
                    "Nodes": len(instance.nodes) if instance.nodes else 0, # Count nodes if present
                    "Network": get_resource_name(instance.authorized_network), # Parse network name
                    "ServiceAccount": instance.persistence_iam_identity,
                    "Port": instance.port,
                    "Version": instance.redis_version,
                    "ReservedIpRange": instance.reserved_ip_range,
                    "TransitEncryptionMode": str(instance.transit_encryption_mode), # SERVER_AUTHENTICATION or DISABLED
                    "Labels": dict(instance.labels) if instance.labels else {}, # Added field
                    "MaintenanceSchedule": str(instance.maintenance_schedule), # Added field
                    "ReadReplicasMode": str(instance.read_replicas_mode), # Added field
                    "ReplicaCount": instance.replica_count, # Added field (for STANDARD_HA)
                    "service": self.SERVICE_NAME
                }
                inventory.append(info)

        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Redis API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Redis instances. Ensure API is enabled and necessary roles granted (e.g., 'Redis Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Redis instances: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Redis instance fetch. Found {len(inventory)} instances.")
        return inventory
