# --- File: gcp_inventory_tool/fetchers/image.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class ImageFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Compute Image details (custom images) for a project.
    """
    SERVICE_NAME = "image" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all custom Compute Images in the specified project.
        Filters out standard images provided by Google or other projects.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Compute Image fetch...")
        inventory = []
        client = compute_v1.ImagesClient(credentials=credentials)
        # Filter string to find images belonging to the target project
        # This mimics the --no-standard-images behavior by checking the self_link
        project_filter_string = f"/projects/{project_id}/"

        try:
            # List all images globally available to the project
            # The API returns all images (public, shared, custom) by default.
            # We need to filter client-side based on the project ID in the self_link.
            images = client.list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing images (filtering for project)...")

            for image in images:
                # Filter for images belonging to the specified project_id
                if project_filter_string in image.self_link:
                    # Prepare info dictionary based on PowerShell script's fields
                    info = {
                        "ProjectId": project_id, # Project where the image resource resides
                        "Name": image.name,
                        "Region": "global", # Custom images are global resources
                        "Description": image.description,
                        "Family": image.family,
                        "DiskSizeGb": image.disk_size_gb,
                        "SourceDisk": get_resource_name(image.source_disk),
                        "SourceImage": get_resource_name(image.source_image),
                        "SourceType": str(image.source_type), # RAW, VHD, VMDK etc.
                        "Status": str(image.status), # READY, FAILED, PENDING, DELETING
                        "Labels": dict(image.labels) if image.labels else {},
                        "Licenses": [get_resource_name(lic) for lic in image.licenses] if image.licenses else [],
                        "CreationTimestamp": image.creation_timestamp, # Added field
                        "Id": str(image.id), # Added ID
                        "service": self.SERVICE_NAME
                    }
                    inventory.append(info)
                # else:
                    # logger.debug(f"Skipping image not belonging to project {project_id}: {image.name} ({image.self_link})")


        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing images or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Viewer). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Compute Images: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Compute Image fetch. Found {len(inventory)} custom images.")
        return inventory
