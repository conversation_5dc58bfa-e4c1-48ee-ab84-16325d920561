# --- File: gcp_inventory_tool/fetchers/interconnect_attachment.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class InterconnectAttachmentFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Interconnect Attachment (VLAN Attachment) details for a project.
    Lists attachments across all regions.
    """
    SERVICE_NAME = "interconnect_attachment" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Interconnect Attachments in the specified project across all regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Interconnect Attachment fetch...")
        inventory = []
        client = compute_v1.InterconnectAttachmentsClient(credentials=credentials)

        try:
            # Use aggregated list to get attachments from all regions
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing interconnect attachments across regions...")

            # The response is an iterator of tuples (region, response_object)
            for region, response in agg_list:
                if response.interconnect_attachments:
                    region_name = get_resource_name(region)
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.interconnect_attachments)} attachments in region {region_name}...")

                    for attachment in response.interconnect_attachments:
                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": attachment.name,
                            "Location": region_name,
                            "EdgeAvailabilityDomain": str(attachment.edge_availability_domain),
                            "Type": str(attachment.type_), # DEDICATED, PARTNER, PARTNER_PROVIDER
                            "Interconnect": get_resource_name(attachment.interconnect),
                            "Partner": None, # Populated below if PARTNER type
                            "Router": get_resource_name(attachment.router),
                            "RemoteService": None, # Not directly available? Partner ASN is separate.
                            "Description": attachment.description,
                            "State": str(attachment.state), # ACTIVE, PENDING_PARTNER etc.
                            "Bandwidth": str(attachment.bandwidth), # BPS_50M, BPS_10G etc.
                            "MTU": attachment.mtu,
                            "Encryption": str(attachment.encryption), # NONE, IPSEC
                            "StackType": str(attachment.stack_type), # IPV4_ONLY, IPV4_IPV6
                            "AdminEnabled": attachment.admin_enabled,
                            "CloudRouterIpAddress": attachment.cloud_router_ip_address,
                            "CustomerRouterIpAddress": attachment.customer_router_ip_address,
                            "PartnerASN": None, # Populated below if PARTNER type
                            "DataPlaneVersion": attachment.dataplane_version,
                            "VlanTag8021q": attachment.vlan_tag8021q, # Added field
                            "CreationTimestamp": attachment.creation_timestamp, # Added field
                            "Id": str(attachment.id), # Added ID
                            "service": self.SERVICE_NAME
                        }

                        # Populate Partner specific info
                        if attachment.type_ == compute_v1.InterconnectAttachment.Type.PARTNER:
                             # Partner name might be in interconnect name for PARTNER type in PS script logic
                             # Replicating that heuristic here, though API has partnerAsn directly
                             if attachment.interconnect:
                                 info["Partner"] = get_resource_name(attachment.interconnect) # Approximation
                             # Get Partner ASN if available (more reliable)
                             info["PartnerASN"] = attachment.partner_asn

                        # Adjust Interconnect field based on type as per PS script logic
                        if attachment.type_ == compute_v1.InterconnectAttachment.Type.PARTNER and attachment.partner_metadata:
                             info["Interconnect"] = attachment.partner_metadata.interconnect_name # Use metadata if available for PARTNER
                             # Also extract partner name from metadata if available
                             info["Partner"] = attachment.partner_metadata.partner_name or info["Partner"]


                        inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Interconnect Attachments or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Interconnect Attachments: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Interconnect Attachment fetch. Found {len(inventory)} attachments.")
        return inventory
