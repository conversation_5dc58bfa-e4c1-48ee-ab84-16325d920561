# # # --- File: gcp_inventory_tool/fetchers/cloud_scheduler_job.py ---
# # import logging
# # from typing import List, Dict, Any, Optional, Tuple
# # from google.oauth2.credentials import Credentials
# # from google.cloud import scheduler_v1
# # from google.api_core import exceptions as api_exceptions

# # from ..utils.timestamp_to_iso import timestamp_to_iso
# # from ..utils.resource_name import get_resource_name
# # from ..core.base_fetcher import ServiceFetcher

# # logger = logging.getLogger('gcp_inventory')

# # # Helper function to parse project, location, job name from a full Scheduler resource name
# # def _parse_scheduler_name(name: str) -> Optional[Tuple[str, str, str]]:
# #     """Parses project, location, and job name from a Scheduler resource name."""
# #     # Format: projects/{project}/locations/{location}/jobs/{job_name}
# #     if not isinstance(name, str):
# #         logger.warning(f"Invalid name type for parsing: {type(name)}")
# #         return None
# #     parts = name.split('/')
# #     try:
# #         if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'jobs':
# #             project_id = parts[1]
# #             location_id = parts[3]
# #             job_name = parts[5]
# #             return project_id, location_id, job_name
# #         else:
# #             logger.warning(f"Unrecognized Scheduler job name format for parsing: {name}")
# #             return None
# #     except (ValueError, IndexError) as e:
# #         logger.error(f"Error parsing Scheduler job name '{name}': {e}")
# #         return None

# # class CloudSchedulerJobFetcher(ServiceFetcher):
# #     """
# #     Fetches Google Cloud Scheduler job details for a project.
# #     Lists jobs across all locations.
# #     """
# #     SERVICE_NAME = "cloud_scheduler_job" # Unique key for this service type

# #     def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
# #         """
# #         Fetches details for all Cloud Scheduler jobs in the specified project across all locations.
# #         """
# #         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud Scheduler job fetch...")
# #         inventory = []
# #         client = scheduler_v1.CloudSchedulerClient(credentials=credentials)
# #         # Parent path to list jobs across all locations
# #         parent = f"projects/{project_id}/locations/-"

# #         try:
# #             jobs = client.list_jobs(parent=parent)
# #             logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Cloud Scheduler jobs across locations...")

# #             for job in jobs:
# #                 parsed_name_parts = _parse_scheduler_name(job.name)
# #                 location = parsed_name_parts[1] if parsed_name_parts else "Unknown"
# #                 short_name = parsed_name_parts[2] if parsed_name_parts else job.name # Fallback

# #                 # Prepare info dictionary based on PowerShell script's fields
# #                 info = {
# #                     "ProjectID": project_id,
# #                     "Name": short_name,
# #                     "Description": job.description,
# #                     "LastAttemptTime": timestamp_to_iso(job.status.last_attempt_time) if job.status else None,
# #                     "UserUpdateTime": timestamp_to_iso(job.user_update_time),
# #                     "State": str(job.state), # ENABLED, PAUSED, DISABLED, UPDATE_FAILED
# #                     "Location": location,
# #                     "Timezone": job.time_zone,
# #                     "Schedule": job.schedule,
# #                     "TargetType": None, # Populated below
# #                     "PubSubTargetTopic": None, # Populated below
# #                     "HttpTargetUri": None, # Populated below
# #                     "HttpMethod": None, # Populated below
# #                     "AppEngineTargetService": None, # Populated below
# #                     "AppEngineTargetVersion": None, # Populated below
# #                     "AppEngineRouting": None, # Populated below
# #                     "AttemptDeadline": str(job.attempt_deadline) if job.attempt_deadline else None, # Added field
# #                     "RetryMaxAttempts": job.retry_config.max_retry_duration.seconds if job.retry_config and job.retry_config.max_retry_duration else None, # Added field (approx)
# #                     "RetryMinBackoff": str(job.retry_config.min_backoff_duration) if job.retry_config else None, # Added field
# #                     "RetryMaxBackoff": str(job.retry_config.max_backoff_duration) if job.retry_config else None, # Added field
# #                     "RetryMaxDoublings": job.retry_config.max_doublings if job.retry_config else None, # Added field
# #                     "service": self.SERVICE_NAME
# #                 }

# #                 # Determine Target Type and details
# #                 if job.pubsub_target:
# #                     info["TargetType"] = "PUBSUB"
# #                     info["PubSubTargetTopic"] = get_resource_name(job.pubsub_target.topic_name)
# #                 elif job.app_engine_http_target:
# #                     info["TargetType"] = "APP_ENGINE_HTTP"
# #                     info["HttpMethod"] = str(job.app_engine_http_target.http_method)
# #                     if job.app_engine_http_target.app_engine_routing:
# #                          ae_routing = job.app_engine_http_target.app_engine_routing
# #                          info["AppEngineTargetService"] = ae_routing.service
# #                          info["AppEngineTargetVersion"] = ae_routing.version
# #                          # Instance omitted for brevity, add if needed: ae_routing.instance
# #                          info["AppEngineRouting"] = f"service={ae_routing.service}, version={ae_routing.version}"
# #                 elif job.http_target:
# #                      info["TargetType"] = "HTTP"
# #                      info["HttpTargetUri"] = job.http_target.uri
# #                      info["HttpMethod"] = str(job.http_target.http_method)
# #                      # Add oauth/oidc token info if needed

# #                 inventory.append(info)

# #         except api_exceptions.NotFound as e:
# #              # This might indicate the API isn't enabled
# #              logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud Scheduler API might not be enabled or project not found. Details: {e}")
# #              return []
# #         except api_exceptions.Forbidden as e:
# #             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Cloud Scheduler jobs. Ensure API is enabled and necessary roles granted (e.g., 'Cloud Scheduler Job Viewer'). Details: {e}")
# #             return [] # Return empty list on permission errors
# #         except Exception as e:
# #             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Cloud Scheduler jobs: {e}", exc_info=True)
# #             return [] # Fail gracefully
# #         finally:
# #             try:
# #                 client.transport.close()
# #             except Exception:
# #                 pass

# #         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud Scheduler job fetch. Found {len(inventory)} jobs.")
# #         return inventory

import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import scheduler_v1
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso
from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, job name from a full Scheduler resource name
def _parse_scheduler_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and job name from a Scheduler resource name."""
    # Format: projects/{project}/locations/{location}/jobs/{job_name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'jobs':
            project_id = parts[1]
            location_id = parts[3]
            job_name = parts[5]
            return project_id, location_id, job_name
        else:
            logger.warning(f"Unrecognized Scheduler job name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing Scheduler job name '{name}': {e}")
        return None

class CloudSchedulerJobFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Scheduler job details for a project.
    Lists jobs across all locations.
    """
    SERVICE_NAME = "cloud_scheduler_job" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Cloud Scheduler jobs in the specified project across all locations.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Cloud Scheduler job fetch...")
        inventory = []
        client = scheduler_v1.CloudSchedulerClient(credentials=credentials)
        
        try:
            # Use a predefined list of common GCP regions instead of trying to fetch them
            # This avoids the issue with operations_client that was causing the first error
            locations = ['us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2', 
                        'europe-west1', 'europe-west2', 'europe-west3', 'asia-east1', 
                        'asia-northeast1', 'asia-south1', 'australia-southeast1']
            logger.info(f"[{project_id}][{self.SERVICE_NAME}] Using location list: {locations}")
            
            # Now iterate through each location to get jobs
            for location in locations:
                location_parent = f"projects/{project_id}/locations/{location}"
                try:
                    location_jobs = client.list_jobs(parent=location_parent)
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Cloud Scheduler jobs in location {location}...")
                    
                    for job in location_jobs:
                        parsed_name_parts = _parse_scheduler_name(job.name)
                        job_location = parsed_name_parts[1] if parsed_name_parts else location
                        short_name = parsed_name_parts[2] if parsed_name_parts else job.name # Fallback

                        # Handle the last_attempt_time carefully to avoid AttributeError
                        last_attempt_time = None
                        if hasattr(job, 'status') and job.status:
                            if hasattr(job.status, 'last_attempt_time'):
                                last_attempt_time = timestamp_to_iso(job.status.last_attempt_time)

                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": short_name,
                            "Description": job.description,
                            "LastAttemptTime": last_attempt_time,
                            "UserUpdateTime": timestamp_to_iso(job.user_update_time) if hasattr(job, 'user_update_time') else None,
                            "State": str(job.state) if hasattr(job, 'state') else None, # ENABLED, PAUSED, DISABLED, UPDATE_FAILED
                            "Location": job_location,
                            "Timezone": job.time_zone if hasattr(job, 'time_zone') else None,
                            "Schedule": job.schedule if hasattr(job, 'schedule') else None,
                            "TargetType": None, # Populated below
                            "PubSubTargetTopic": None, # Populated below
                            "HttpTargetUri": None, # Populated below
                            "HttpMethod": None, # Populated below
                            "AppEngineTargetService": None, # Populated below
                            "AppEngineTargetVersion": None, # Populated below
                            "AppEngineRouting": None, # Populated below
                            "AttemptDeadline": str(job.attempt_deadline) if hasattr(job, 'attempt_deadline') and job.attempt_deadline else None,
                            "RetryMaxAttempts": None,
                            "RetryMinBackoff": None,
                            "RetryMaxBackoff": None,
                            "RetryMaxDoublings": None,
                            "service": self.SERVICE_NAME
                        }

                        # Safely populate retry configuration
                        if hasattr(job, 'retry_config') and job.retry_config:
                            retry_config = job.retry_config
                            if hasattr(retry_config, 'max_retry_duration') and retry_config.max_retry_duration:
                                info["RetryMaxAttempts"] = retry_config.max_retry_duration.seconds
                            if hasattr(retry_config, 'min_backoff_duration'):
                                info["RetryMinBackoff"] = str(retry_config.min_backoff_duration)
                            if hasattr(retry_config, 'max_backoff_duration'):
                                info["RetryMaxBackoff"] = str(retry_config.max_backoff_duration)
                            if hasattr(retry_config, 'max_doublings'):
                                info["RetryMaxDoublings"] = retry_config.max_doublings

                        # Determine Target Type and details with safe attribute access
                        if hasattr(job, 'pubsub_target') and job.pubsub_target:
                            info["TargetType"] = "PUBSUB"
                            if hasattr(job.pubsub_target, 'topic_name'):
                                info["PubSubTargetTopic"] = get_resource_name(job.pubsub_target.topic_name)
                        elif hasattr(job, 'app_engine_http_target') and job.app_engine_http_target:
                            info["TargetType"] = "APP_ENGINE_HTTP"
                            if hasattr(job.app_engine_http_target, 'http_method'):
                                info["HttpMethod"] = str(job.app_engine_http_target.http_method)
                            if hasattr(job.app_engine_http_target, 'app_engine_routing') and job.app_engine_http_target.app_engine_routing:
                                ae_routing = job.app_engine_http_target.app_engine_routing
                                if hasattr(ae_routing, 'service'):
                                    info["AppEngineTargetService"] = ae_routing.service
                                if hasattr(ae_routing, 'version'):
                                    info["AppEngineTargetVersion"] = ae_routing.version
                                # Build routing string only if values exist
                                routing_parts = []
                                if hasattr(ae_routing, 'service') and ae_routing.service:
                                    routing_parts.append(f"service={ae_routing.service}")
                                if hasattr(ae_routing, 'version') and ae_routing.version:
                                    routing_parts.append(f"version={ae_routing.version}")
                                if routing_parts:
                                    info["AppEngineRouting"] = ", ".join(routing_parts)
                        elif hasattr(job, 'http_target') and job.http_target:
                            info["TargetType"] = "HTTP"
                            if hasattr(job.http_target, 'uri'):
                                info["HttpTargetUri"] = job.http_target.uri
                            if hasattr(job.http_target, 'http_method'):
                                info["HttpMethod"] = str(job.http_target.http_method)

                        inventory.append(info)
                
                except api_exceptions.NotFound as e:
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] No Cloud Scheduler jobs found in location {location}")
                    continue
                except api_exceptions.Forbidden as e:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Cloud Scheduler jobs in location {location}")
                    continue
                except Exception as e:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error listing Cloud Scheduler jobs in location {location}: {str(e)}")
                    continue

        except api_exceptions.NotFound as e:
            # This might indicate the API isn't enabled
            logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Cloud Scheduler API might not be enabled or project not found. Details: {e}")
            return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Cloud Scheduler jobs. Ensure API is enabled and necessary roles granted (e.g., 'Cloud Scheduler Job Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Cloud Scheduler jobs: {str(e)}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Cloud Scheduler job fetch. Found {len(inventory)} jobs.")
        return inventory
