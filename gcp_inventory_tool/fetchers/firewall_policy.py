# --- File: gcp_inventory_tool/fetchers/firewall_policy.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class FirewallPolicyFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Network Firewall Policies and their Rules for a project.
    Returns a list where each item represents a rule, including parent policy info.
    """
    SERVICE_NAME = "firewall_policy" # Unique key for this service type

    def _format_secure_tags(self, tags: Optional[List[compute_v1.FirewallPolicyRuleSecureTag]]) -> List[Dict[str, str]]:
        """Formats secure tags into a list of dictionaries."""
        if not tags:
            return []
        return [{"name": tag.name, "state": str(tag.state)} for tag in tags]

    def _format_layer4_configs(self, configs: Optional[List[compute_v1.FirewallPolicyRuleMatcherLayer4Config]]) -> List[Dict[str, Any]]:
         """Formats Layer4 configs into a list of dictionaries."""
         if not configs:
             return []
         return [{"ip_protocol": cfg.ip_protocol, "ports": list(cfg.ports)} for cfg in configs]


    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Network Firewall Policies and their rules in the project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Network Firewall Policy fetch...")
        inventory = []
        client = compute_v1.NetworkFirewallPoliciesClient(credentials=credentials)

        try:
            # List all firewall policies in the project
            policies = client.list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing policies...")

            for policy_header in policies:
                try:
                    # Get full details for each policy to access rules and associations
                    policy = client.get(project=project_id, firewall_policy=policy_header.name)
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing policy '{policy.name}'...")

                    policy_associations = []
                    if policy.associations:
                        policy_associations = [{"name": assoc.name, "attachment_target": get_resource_name(assoc.attachment_target)}
                                               for assoc in policy.associations]

                    # Iterate through rules within the policy
                    if not policy.rules:
                         logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Policy '{policy.name}' has no rules.")
                         # Optionally create an entry for the policy itself if no rules exist
                         # policy_info = { ... policy details ... , "Rules": [] }
                         # inventory.append(policy_info)
                         continue # Skip if no rules and we only report rules

                    for rule in policy.rules:
                        # Prepare info dictionary for each RULE, including parent policy info
                        rule_info = {
                            "ProjectID": project_id,
                            "PolicyName": policy.name,
                            "PolicyId": policy.id,
                            "PolicyCreationDate": policy.creation_timestamp,
                            "PolicyDescription": policy.description,
                            "PolicyRuleTupleCount": policy.rule_tuple_count,
                            "PolicyAssociations": policy_associations, # List of associated network names/targets
                            # --- Rule Specific Fields ---
                            "RulePriority": rule.priority,
                            "RuleAction": rule.action,
                            "RuleDirection": str(rule.direction), # INGRESS or EGRESS
                            "RuleDescription": rule.description,
                            "RuleTupleCount": rule.rule_tuple_count,
                            "RuleLogging": rule.enable_logging,
                            "RuleDisabled": rule.disabled,
                            "RuleTargetSecureTags": self._format_secure_tags(rule.target_secure_tags),
                            "RuleDestIpRanges": list(rule.match.dest_ip_ranges) if rule.match else [],
                            "RuleLayer4Configs": self._format_layer4_configs(rule.match.layer4_configs if rule.match else None),
                            "RuleSrcIpRanges": list(rule.match.src_ip_ranges) if rule.match else [], # Added SrcIpRanges
                            "RuleSrcFqdns": list(rule.match.src_fqdns) if rule.match else [],
                            "RuleSrcRegionCodes": list(rule.match.src_region_codes) if rule.match else [],
                            "RuleSrcSecureTags": self._format_secure_tags(rule.match.src_secure_tags if rule.match else None),
                            "RuleSrcThreatIntelligences": list(rule.match.src_threat_intelligences) if rule.match else [],
                            "RuleSecurityProfileGroup": get_resource_name(rule.security_profile_group),
                            "RuleTlsInspect": rule.tls_inspect,
                            "service": self.SERVICE_NAME
                        }
                        inventory.append(rule_info)

                except api_exceptions.Forbidden as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied getting details for policy '{policy_header.name}': {e}")
                    # Continue to next policy
                except api_exceptions.NotFound as e:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Policy '{policy_header.name}' not found during get details. Might have been deleted after listing.")
                    # Continue to next policy
                except Exception as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to get details or process rules for policy '{policy_header.name}': {e}", exc_info=True)
                    # Continue to next policy


        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing firewall policies or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Security Admin/Viewer). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Network Firewall Policies: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Network Firewall Policy fetch. Found {len(inventory)} rules across policies.")
        return inventory
