# --- File: gcp_inventory_tool/fetchers/alert_policy.py ---
import logging
import re # For parsing filter strings if needed, though direct access is often better
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import monitoring_v3
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso # For handling proto types
from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Regex patterns to extract info from filter strings (similar to PS script)
# Use with caution, API structure might be more reliable if available
RESOURCE_TYPE_RE = re.compile(r'resource\.type\s*=\s*"(.*?)"')
METRIC_TYPE_RE = re.compile(r'metric\.type\s*=\s*"(.*?)"')


class AlertPolicyFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Monitoring Alert Policy details for a project.
    Includes associated notification channel details.
    """
    SERVICE_NAME = "alert_policy" # Unique key for this service type

    def _get_notification_channels(self, client: monitoring_v3.NotificationChannelServiceClient, project_name: str) -> Dict[str, Dict[str, Any]]:
        """Fetches all notification channels and returns a dict keyed by channel name."""
        channels_dict = {}
        try:
            request = monitoring_v3.ListNotificationChannelsRequest(name=project_name)
            all_channels = client.list_notification_channels(request=request)
            for channel in all_channels:
                channel_info = {
                    "name": channel.name,
                    "type": channel.type_,
                    "displayName": channel.display_name,
                    "description": channel.description,
                    "labels": dict(channel.labels) if channel.labels else {},
                    "user_labels": dict(channel.user_labels) if channel.user_labels else {},
                    "enabled": channel.enabled if hasattr(channel, 'enabled') else None, # Get boolean value
                    "verification_status": str(channel.verification_status)
                }
                channels_dict[channel.name] = channel_info
            logger.debug(f"[{project_name.split('/')[1]}][{self.SERVICE_NAME}] Fetched {len(channels_dict)} notification channels.")
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_name.split('/')[1]}][{self.SERVICE_NAME}] Permission denied listing notification channels: {e}")
        except Exception as e:
            logger.error(f"[{project_name.split('/')[1]}][{self.SERVICE_NAME}] Failed to list notification channels: {e}", exc_info=True)
        return channels_dict

    def _format_condition(self, condition: monitoring_v3.types.AlertPolicy.Condition) -> str:
         """Formats a condition into a string similar to PowerShell."""
         parts = []
         name = condition.display_name
         parts.append(f"Name: {name}")

         # Extract details based on condition type
         # Focusing on condition_threshold as per PS script
         if condition.condition_threshold:
             ct = condition.condition_threshold
             filter_str = ct.filter
             parts.append(f"Filter: {filter_str}") # Include raw filter

             # Attempt to parse resource/metric type using regex (less reliable than structured data if available)
             resource_match = RESOURCE_TYPE_RE.search(filter_str)
             metric_match = METRIC_TYPE_RE.search(filter_str)
             if resource_match:
                 parts.append(f"ResourceType: {resource_match.group(1)}")
             if metric_match:
                 parts.append(f"MetricType: {metric_match.group(1)}")

             # Threshold value
             if ct.threshold_value is not None: # Check for None explicitly
                  parts.append(f"ThresholdValue: {ct.threshold_value}")
             # Add other fields like duration, comparison, aggregations if needed
             parts.append(f"Comparison: {str(ct.comparison)}")
             parts.append(f"Duration: {str(ct.duration)}")

         elif condition.condition_absent:
              # Handle metric absence conditions if needed
              parts.append("Type: MetricAbsence")
              parts.append(f"Filter: {condition.condition_absent.filter}")
              parts.append(f"Duration: {str(condition.condition_absent.duration)}")
         elif condition.condition_matched_log:
              # Handle log match conditions if needed
              parts.append("Type: LogMatch")
              parts.append(f"Filter: {condition.condition_matched_log.filter}")
         # Add other condition types (e.g., monitoring_service_level_objective) if necessary

         return " ; ".join(parts) + " ; " # Add trailing semicolon pair like PS script


    def _format_notification_channels(self, channel_names: List[str], all_channels_dict: Dict[str, Dict[str, Any]]) -> str:
        """Formats notification channel details into a string similar to PowerShell."""
        final_channels_str = ""
        for rchannel_name in channel_names:
            channel_details = all_channels_dict.get(rchannel_name)
            if channel_details:
                 labels_str = " ; ".join([f"{k} : {v}" for k, v in channel_details.get("labels", {}).items()])
                 # Note: PS script included user_labels in the same 'Labels' string, combining here
                 user_labels_str = " ; ".join([f"{k} : {v}" for k, v in channel_details.get("user_labels", {}).items()])
                 if labels_str and user_labels_str:
                     labels_combined = f"{labels_str} ; {user_labels_str}"
                 else:
                     labels_combined = labels_str or user_labels_str

                 final_channels_str += f"Name: {channel_details.get('displayName', rchannel_name)} ; " # Use display name if available
                 final_channels_str += f"Type: {channel_details.get('type')} ; "
                 final_channels_str += f"Enabled: {channel_details.get('enabled')} ; "
                 final_channels_str += f"Labels: {labels_combined} ; ; " # Trailing semicolons like PS script
            else:
                 final_channels_str += f"Name: {rchannel_name} (Details not found) ; ; "
        return final_channels_str.strip() # Remove trailing space


    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Alert Policies in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Alert Policy fetch...")
        inventory = []
        policy_client = monitoring_v3.AlertPolicyServiceClient(credentials=credentials)
        channel_client = monitoring_v3.NotificationChannelServiceClient(credentials=credentials)
        project_name = f"projects/{project_id}"

        # --- Step 1: Fetch all Notification Channels for lookup ---
        all_channels = self._get_notification_channels(channel_client, project_name)
        # ---------------------------------------------------------

        try:
            policies = policy_client.list_alert_policies(name=project_name)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing alert policies...")

            for policy in policies:
                # Prepare info dictionary based on PowerShell script's fields
                info = {
                    "ProjectID": project_id,
                    "Name": policy.display_name,
                    "PolicyID": policy.name.split('/')[-1], # Added policy ID
                    "Enabled": policy.enabled if hasattr(policy, 'enabled') else None, # Get boolean value
                    "Conditions": "", # Populated below
                    "NotificationChannels": "", # Populated below
                    "CreationRecordUser": policy.creation_record.mutated_by if policy.creation_record else None, # Added
                    "CreationRecordTime": timestamp_to_iso(policy.creation_record.mutate_time) if policy.creation_record else None, # Added
                    "MutationRecordUser": policy.mutation_record.mutated_by if policy.mutation_record else None, # Added
                    "MutationRecordTime": timestamp_to_iso(policy.mutation_record.mutate_time) if policy.mutation_record else None, # Added
                    "Combiner": str(policy.combiner), # AND, OR, AND_WITH_MATCHING_RESOURCE
                    "service": self.SERVICE_NAME
                }

                # Format Conditions
                if policy.conditions:
                     info["Conditions"] = " ".join([self._format_condition(cond) for cond in policy.conditions])

                # Format Notification Channels
                if policy.notification_channels:
                     info["NotificationChannels"] = self._format_notification_channels(
                         list(policy.notification_channels), # Pass the list of channel names
                         all_channels # Pass the lookup dictionary
                     )

                inventory.append(info)

        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Monitoring API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Alert Policies. Ensure API is enabled and necessary roles granted (e.g., 'Monitoring Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Alert Policies: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            # Ensure clients are closed
            if policy_client:
                policy_client.transport.close()
            if channel_client:
                channel_client.transport.close()

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Alert Policy fetch. Found {len(inventory)} policies.")
        return inventory
