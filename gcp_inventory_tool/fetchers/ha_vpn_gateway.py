# --- File: gcp_inventory_tool/fetchers/ha_vpn_gateway.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class HaVpnGatewayFetcher(ServiceFetcher):
    """
    Fetches Google Cloud HA VPN Gateway (compute.vpnGateways) details for a project.
    """
    SERVICE_NAME = "ha_vpn_gateway" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all HA VPN Gateways in the specified project across all regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting HA VPN Gateway fetch...")
        inventory = []
        client = compute_v1.VpnGatewaysClient(credentials=credentials)

        try:
            # Use aggregated list to get gateways from all regions
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing HA VPN gateways across regions...")

            # The response is an iterator of tuples (region, response_object)
            for region, response in agg_list:
                if response.vpn_gateways:
                    region_name = get_resource_name(region)
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.vpn_gateways)} gateways in region {region_name}...")

                    for gateway in response.vpn_gateways:
                        # Extract IP addresses from interfaces
                        ip_list = []
                        if gateway.vpn_interfaces:
                            for interface in gateway.vpn_interfaces:
                                if interface.ip_address:
                                    ip_list.append(interface.ip_address)

                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectId": project_id,
                            "Name": gateway.name,
                            "IPList": ip_list, # List of IPs from interfaces
                            "Region": region_name,
                            "Network": get_resource_name(gateway.network),
                            "CreationTimestamp": gateway.creation_timestamp,
                            "Labels": dict(gateway.labels) if gateway.labels else {},
                            "Id": str(gateway.id), # Added ID
                            "StackType": str(gateway.stack_type), # Added field (IPV4_ONLY, IPV4_IPV6)
                            "service": self.SERVICE_NAME
                        }
                        inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing HA VPN gateways or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process HA VPN Gateways: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished HA VPN Gateway fetch. Found {len(inventory)} gateways.")
        return inventory
