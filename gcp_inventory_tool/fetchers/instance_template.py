# import logging
# from typing import List, Dict, Any, Optional
# from google.oauth2.credentials import Credentials
# from google.cloud import compute_v1
# from google.api_core import exceptions as api_exceptions

# from ..utils.resource_name import get_resource_name
# from ..core.base_fetcher import ServiceFetcher

# logger = logging.getLogger('gcp_inventory')


# class InstanceGroupManagerFetcher(ServiceFetcher):
#     """
#     Fetches Google Cloud Instance Group Manager (Managed Instance Group) details.
#     """
#     SERVICE_NAME = "instance_group_manager" # Unique key for this service type

#     def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
#         """
#         Fetches details for all Instance Group Managers in the specified project across all zones/regions.
#         """
#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Instance Group Manager fetch...")
#         inventory = []
#         client = compute_v1.InstanceGroupManagersClient(credentials=credentials)
        
#         # Create autoscaler client to check which instance groups are autoscaled
#         autoscaler_client = compute_v1.AutoscalersClient(credentials=credentials)

#         try:
#             # Get all autoscalers to determine which instance groups are autoscaled
#             autoscaled_igms = set()
#             try:
#                 # Get all autoscalers across all zones (aggregated list)
#                 autoscalers_request = autoscaler_client.aggregated_list(project=project_id)
                
#                 for _, response in autoscalers_request:
#                     if response.autoscalers:
#                         for autoscaler in response.autoscalers:
#                             # Add the target (which is an IGM) to our set of autoscaled IGMs
#                             if autoscaler.target:
#                                 autoscaled_igms.add(autoscaler.target)
                
#                 logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Found {len(autoscaled_igms)} autoscaled instance groups")
#             except Exception as e:
#                 logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Failed to fetch autoscalers: {e}")
#                 # Continue with the rest of the code - we'll just mark all as not autoscaled

#             # Use aggregated list to get managers from all zones and regions
#             agg_list = client.aggregated_list(project=project_id)
#             logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing managers across zones/regions...")

#             # The response is an iterator of tuples (scope, response_object)
#             # Scope is like "zones/us-central1-a" or "regions/us-central1"
#             for scope, response in agg_list:
#                 managers = []
#                 if response.instance_group_managers:
#                     managers = response.instance_group_managers

#                 if managers:
#                     scope_name = get_resource_name(scope) # Zone or Region name
#                     is_regional = "regions" in scope.lower()
#                     logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(managers)} managers in scope {scope_name}...")

#                     for igm in managers:
#                         # Prepare info dictionary based on PowerShell script's fields
#                         info = {
#                             "ProjectID": project_id,
#                             "Name": igm.name,
#                             "Location": scope_name,
#                             "ScopeType": "REGIONAL" if is_regional else "ZONAL", # Added field
#                             "CreationDate": igm.creation_timestamp,
#                             "BaseInstanceName": igm.base_instance_name,
#                             "InstanceGroup": get_resource_name(igm.instance_group),
#                             "InstanceTemplate": get_resource_name(igm.instance_template),
#                             "Size": None, # Current size requires different API call (list_managed_instances)
#                             "TargetSize": igm.target_size,
#                             "Autoscaled": igm.self_link in autoscaled_igms, # Check if this IGM is in our autoscaled set
#                             "UpdatePolicyType": None,
#                             "ReplacementMethod": None,
#                             "MinimalAction": None,
#                             "MaxSurge": None,
#                             "MaxUnavailable": None,
#                             "Id": str(igm.id), # Added ID
#                             "service": self.SERVICE_NAME
#                         }

#                         # Process Update Policy
#                         if igm.update_policy:
#                             up = igm.update_policy
#                             info["UpdatePolicyType"] = str(up.type_) if up.type_ else None
#                             info["ReplacementMethod"] = str(up.replacement_method) if hasattr(up, 'replacement_method') and up.replacement_method else None # Newer field
#                             info["MinimalAction"] = str(up.minimal_action) if up.minimal_action else None

#                             # Max Surge / Unavailable can be fixed or percent
#                             if up.max_surge:
#                                 info["MaxSurge"] = up.max_surge.fixed or up.max_surge.percent or up.max_surge.calculated # Calculated is often populated
#                             if up.max_unavailable:
#                                 info["MaxUnavailable"] = up.max_unavailable.fixed or up.max_unavailable.percent or up.max_unavailable.calculated

#                         inventory.append(info)

#         except api_exceptions.Forbidden as e:
#             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Instance Group Managers or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Viewer). Details: {e}")
#             return [] # Return empty list on permission errors
#         except api_exceptions.NotFound as e:
#              logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
#              return [] # API not enabled or project issue
#         except Exception as e:
#             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Instance Group Managers: {e}", exc_info=True)
#             return [] # Fail gracefully
#         finally:
#             try:
#                 # Close both clients
#                 client.transport.close()
#                 autoscaler_client.transport.close()
#             except Exception:
#                 pass

#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Instance Group Manager fetch. Found {len(inventory)} managers.")
#         return inventory

import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class InstanceTemplateFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Instance Template details.
    """
    SERVICE_NAME = "instance_template"  # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Instance Templates in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Instance Template fetch...")
        inventory = []
        client = compute_v1.InstanceTemplatesClient(credentials=credentials)

        try:
            # List all instance templates
            templates = client.list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing templates...")

            for template in templates:
                try:
                    props = template.properties
                    
                    # Base info for the template - using safer access with hasattr checks
                    info = {
                        "ProjectID": project_id,
                        "Name": template.name if hasattr(template, 'name') else "unknown",
                        "CreationDate": template.creation_timestamp if hasattr(template, 'creation_timestamp') else None,
                        "Id": str(template.id) if hasattr(template, 'id') else None,
                        "MachineType": get_resource_name(props.machine_type) if hasattr(props, 'machine_type') and props.machine_type else None,
                        "CanIpForward": props.can_ip_forward if hasattr(props, 'can_ip_forward') else False,
                        "Description": template.description if hasattr(template, 'description') else None,
                        "Status": template.status if hasattr(template, 'status') else None,
                        "DiskType": None,  # Will be populated below if disks exist
                        "DiskSizeGb": None,  # Will be populated below if disks exist
                        "AutoDelete": None,  # Will be populated below if disks exist
                        "ExternalIP": False,  # Default, will check network interfaces below
                        "InternalIP": None,  # Will be populated below if interfaces exist
                        "Network": None,  # Will be populated below if interfaces exist
                        "Subnet": None,  # Will be populated below if interfaces exist
                        "NetworkIP": None,  # Will be populated below if interfaces exist
                        "Tags": [],  # Will be populated below if tags exist
                        "Metadata": {},  # Will be populated below if metadata exists
                        "Labels": {},  # Will be populated below if labels exist
                        "GuestAccelerators": [],  # Will be populated below if accelerators exist
                        "GPUType": None,  # Will be populated below if accelerators exist
                        "GPUCount": None,  # Will be populated below if accelerators exist
                        "service": self.SERVICE_NAME
                    }

                    # Process disks (using first boot disk for info) - checking if attribute exists
                    if hasattr(props, 'disks') and props.disks:
                        try:
                            # Find the boot disk (or use the first disk)
                            boot_disk = next((disk for disk in props.disks if hasattr(disk, 'boot') and disk.boot), props.disks[0])
                            
                            if hasattr(boot_disk, 'initialize_params') and boot_disk.initialize_params:
                                params = boot_disk.initialize_params
                                if hasattr(params, 'disk_type') and params.disk_type:
                                    info["DiskType"] = get_resource_name(params.disk_type)
                                if hasattr(params, 'disk_size_gb'):
                                    info["DiskSizeGb"] = params.disk_size_gb
                            
                            if hasattr(boot_disk, 'auto_delete'):
                                info["AutoDelete"] = boot_disk.auto_delete
                        except (IndexError, AttributeError) as e:
                            logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error processing disk info for template {template.name}: {e}")

                    # Process network interfaces - checking if attribute exists
                    if hasattr(props, 'network_interfaces') and props.network_interfaces:
                        try:
                            nic = props.network_interfaces[0]  # Use first NIC for basic info
                            
                            # Network and Subnet
                            if hasattr(nic, 'network') and nic.network:
                                info["Network"] = get_resource_name(nic.network)
                            if hasattr(nic, 'subnetwork') and nic.subnetwork:
                                info["Subnet"] = get_resource_name(nic.subnetwork)
                            
                            # Check for internal IP - try both possible field names
                            if hasattr(nic, 'network_i_p'):
                                info["NetworkIP"] = nic.network_i_p
                            
                            # External IP - check if any access configs exist
                            if hasattr(nic, 'access_configs') and nic.access_configs:
                                info["ExternalIP"] = True
                        except (IndexError, AttributeError) as e:
                            logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error processing network info for template {template.name}: {e}")

                    # Process tags - checking if attribute exists
                    if hasattr(props, 'tags') and props.tags and hasattr(props.tags, 'items'):
                        info["Tags"] = list(props.tags.items)

                    # Process metadata - checking if attribute exists
                    if hasattr(props, 'metadata') and props.metadata and hasattr(props.metadata, 'items'):
                        try:
                            info["Metadata"] = {item.key: item.value for item in props.metadata.items if hasattr(item, 'key') and item.key}
                        except AttributeError as e:
                            logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error processing metadata for template {template.name}: {e}")

                    # Process labels - check both locations and use hasattr for safety
                    # Labels may be in the properties or directly on the template
                    try:
                        if hasattr(props, 'labels') and props.labels:
                            info["Labels"] = dict(props.labels)
                    except (AttributeError, TypeError) as e:
                        logger.debug(f"[{project_id}][{self.SERVICE_NAME}] No property labels for template {template.name}: {e}")
                        
                    # Process GPU/accelerators - checking if attribute exists
                    if hasattr(props, 'guest_accelerators') and props.guest_accelerators:
                        try:
                            for accel in props.guest_accelerators:
                                # Check if we have the required attributes
                                if hasattr(accel, 'accelerator_type') and hasattr(accel, 'accelerator_count'):
                                    # This is a list of accelerator types and counts
                                    info["GuestAccelerators"].append({
                                        "Type": get_resource_name(accel.accelerator_type),
                                        "Count": accel.accelerator_count
                                    })
                                    
                                    # Also add GPU info for first GPU found (for backwards compatibility)
                                    if "gpu" in accel.accelerator_type.lower() and info["GPUType"] is None:
                                        info["GPUType"] = get_resource_name(accel.accelerator_type)
                                        info["GPUCount"] = accel.accelerator_count
                        except AttributeError as e:
                            logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error processing accelerators for template {template.name}: {e}")

                    inventory.append(info)
                except Exception as e:
                    # Catch errors for individual templates so we can continue processing others
                    template_name = template.name if hasattr(template, 'name') else "unknown"
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Error processing template {template_name}: {e}")
                    continue

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Instance Templates or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., 'Compute Viewer'). Details: {e}")
            return []  # Return empty list on permission errors
        except api_exceptions.NotFound as e:
            logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
            return []  # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Instance Templates: {e}", exc_info=True)
            return []  # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Instance Template fetch. Found {len(inventory)} templates.")
        return inventory