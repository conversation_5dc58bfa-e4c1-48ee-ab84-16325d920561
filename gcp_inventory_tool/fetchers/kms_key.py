# --- File: gcp_inventory_tool/fetchers/kms_key.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import kms_v1
from google.api_core import exceptions as api_exceptions
from google.protobuf import duration_pb2, timestamp_pb2 # For handling proto types

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, keyring, key name from a full KMS resource name
def _parse_kms_name(name: str) -> Optional[Tuple[str, str, str, Optional[str]]]:
    """Parses project, location, keyring, and optionally key name from a KMS resource name."""
    # Format: projects/{p}/locations/{l}/keyRings/{kr}/cryptoKeys/{k} OR projects/{p}/locations/{l}/keyRings/{kr}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) >= 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'keyRings':
            project_id = parts[1]
            location_id = parts[3]
            keyring_name = parts[5]
            key_name = parts[7] if len(parts) == 8 and parts[6] == 'cryptoKeys' else None
            return project_id, location_id, keyring_name, key_name
        else:
            logger.warning(f"Unrecognized KMS resource name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing KMS resource name '{name}': {e}")
        return None

# Helper to convert protobuf Duration to string (e.g., "12345s")
def duration_to_seconds_str(duration: Optional[duration_pb2.Duration]) -> Optional[str]:
    """Converts a Protobuf Duration object to a string representing seconds."""
    if duration and (duration.seconds or duration.nanos):
         return str(duration.seconds) # KMS rotationPeriod is usually just seconds
    return None

# Helper to format timestamp proto
def timestamp_to_iso(ts: Optional[timestamp_pb2.Timestamp]) -> Optional[str]:
     """Converts a Protobuf Timestamp to an ISO 8601 string."""
     if ts:
         try:
             # Convert to datetime object first
             dt = ts.ToDatetime()
             # Ensure timezone information (usually UTC from API)
             if dt.tzinfo is None:
                 import datetime
                 dt = dt.replace(tzinfo=datetime.timezone.utc)
             return dt.isoformat()
         except Exception as e:
             logger.warning(f"Could not convert timestamp {ts} to ISO string: {e}")
             return str(ts) # Fallback to string representation
     return None


class KmsKeyFetcher(ServiceFetcher):
    """
    Fetches Google Cloud KMS Crypto Key details for a project.
    Lists key rings across all locations, then keys within each ring.
    """
    SERVICE_NAME = "kms_key" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all KMS Crypto Keys in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting KMS Key fetch...")
        inventory = []
        client = kms_v1.KeyManagementServiceClient(credentials=credentials)
        # Parent path to list key rings across all locations
        parent = f"projects/{project_id}/locations/-"

        try:
            # List all key rings in all accessible locations for the project
            key_rings = client.list_key_rings(parent=parent)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing key rings...")

            for key_ring in key_rings:
                key_ring_name = key_ring.name
                parsed_kr_parts = _parse_kms_name(key_ring_name)
                if not parsed_kr_parts:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Skipping key ring with unparseable name: {key_ring_name}")
                    continue
                _, kr_location, kr_short_name, _ = parsed_kr_parts
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Listing keys in keyring '{kr_short_name}' location '{kr_location}'...")

                try:
                    # List crypto keys within the current key ring
                    crypto_keys = client.list_crypto_keys(parent=key_ring_name)

                    for key in crypto_keys:
                        parsed_key_parts = _parse_kms_name(key.name)
                        if not parsed_key_parts:
                             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Skipping key with unparseable name: {key.name}")
                             continue
                        _, key_location, key_ring_short_name, key_short_name = parsed_key_parts

                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": key_short_name,
                            "CreationDate": timestamp_to_iso(key.create_time),
                            "Location": key_location,
                            "Keyring": key_ring_short_name,
                            "RotationPeriod": duration_to_seconds_str(key.rotation_period),
                            "Purpose": str(key.purpose), # ENCRYPT_DECRYPT, ASYMMETRIC_SIGN, etc.
                            "Algorithm": None, # Populated from primary version
                            "ProtectionLevel": None, # Populated from primary version
                            "State": None, # Populated from primary version
                            "NextRotationTime": timestamp_to_iso(key.next_rotation_time), # Added field
                            "Labels": dict(key.labels) if key.labels else {}, # Added field
                            "service": self.SERVICE_NAME
                        }

                        # Get details from the primary key version
                        if key.primary:
                            primary = key.primary
                            info["Algorithm"] = str(primary.algorithm)
                            info["ProtectionLevel"] = str(primary.protection_level)
                            info["State"] = str(primary.state) # ENABLED, DISABLED, DESTROYED, etc.

                        inventory.append(info)

                except api_exceptions.Forbidden as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing keys in keyring '{key_ring_name}': {e}")
                    # Continue to the next key ring
                except Exception as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list keys in keyring '{key_ring_name}': {e}", exc_info=True)
                    # Continue to the next key ring


        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing key rings or accessing KMS API. Ensure API is enabled and necessary roles granted (e.g., 'Cloud KMS Viewer'). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] KMS API might not be enabled or project not found. Details: {e}")
             return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process KMS Key Rings/Keys: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished KMS Key fetch. Found {len(inventory)} keys.")
        return inventory
