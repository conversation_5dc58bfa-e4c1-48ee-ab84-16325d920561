# --- File: gcp_inventory_tool/fetchers/subnet.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class SubnetFetcher(ServiceFetcher):
    """
    Fetches Google Cloud VPC Subnet details for a project.
    """
    SERVICE_NAME = "subnet" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all VPC Subnets in the specified project across all regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting VPC Subnet fetch...")
        inventory = []
        client = compute_v1.SubnetworksClient(credentials=credentials)

        try:
            # Use aggregated list to get subnets from all regions
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing subnets across regions...")

            # The response is an iterator of tuples (region, response_object)
            for region, response in agg_list:
                if response.subnetworks:
                    region_name = get_resource_name(region)
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.subnetworks)} subnets in region {region_name}...")
                    for subnet in response.subnetworks:
                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": subnet.name,
                            "Region": region_name, # Region derived from the aggregated list key
                            "Network": get_resource_name(subnet.network),
                            "Range": subnet.ip_cidr_range,
                            "PrivateIpGoogleAccess": subnet.private_ip_google_access,
                            "FlowLogs": subnet.enable_flow_logs,
                            "Id": str(subnet.id), # Added ID
                            "GatewayAddress": subnet.gateway_address, # Added field
                            "Purpose": str(subnet.purpose), # Added field (e.g., PRIVATE, INTERNAL_HTTPS_LOAD_BALANCER)
                            "Role": str(subnet.role), # Added field (e.g., ACTIVE, BACKUP)
                            "CreationDate": subnet.creation_timestamp, # Added field
                            "service": self.SERVICE_NAME
                        }
                        # Log configuration details if needed
                        # if subnet.log_config and subnet.log_config.enable:
                        #     info["FlowLogsConfig"] = {
                        #         "aggregation_interval": str(subnet.log_config.aggregation_interval),
                        #         "flow_sampling": subnet.log_config.flow_sampling,
                        #         "metadata": str(subnet.log_config.metadata)
                        #     }

                        inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing subnets or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Network Viewer). Details: {e}")
            return [] # Return empty list on permission errors for the service
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process VPC Subnets: {e}", exc_info=True)
            return [] # Fail gracefully for this service
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished VPC Subnet fetch. Found {len(inventory)} subnets.")
        return inventory
