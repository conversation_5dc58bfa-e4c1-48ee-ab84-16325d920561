# --- File: gcp_inventory_tool/fetchers/bigtable.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import bigtable_admin_v2
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


# Helper function to parse location/cluster name from cluster resource name
def _parse_bigtable_cluster_name(name: str) -> Optional[Tuple[str, str, str, str]]:
    """Parses project, instance, location, cluster name from a cluster resource name."""
    # Format: projects/{project}/instances/{instance}/clusters/{cluster}
    # Location is part of the cluster object itself, not the name usually.
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'instances' and parts[4] == 'clusters':
            project_id = parts[1]
            instance_name = parts[3]
            cluster_name = parts[5]
            # Location needs to be fetched from the cluster object itself
            return project_id, instance_name, None, cluster_name
        else:
            logger.warning(f"Unrecognized Bigtable cluster name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing Bigtable cluster name '{name}': {e}")
        return None

# Helper function to parse location from location ID string
def _parse_bigtable_location(location_id: str) -> Optional[str]:
     """Extracts the location name from projects/.../locations/{location}"""
     if not location_id or 'locations/' not in location_id:
         return location_id # Return as is if format unexpected
     try:
         return location_id.split('/')[-1]
     except Exception:
         return location_id


class BigtableFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Bigtable instance and cluster details for a project.
    Returns one item per cluster, including parent instance info.
    """
    SERVICE_NAME = "bigtable" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Bigtable instances and their clusters in the project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Bigtable instance & cluster fetch...")
        inventory = []
        instance_client = bigtable_admin_v2.BigtableInstanceAdminClient(credentials=credentials)
        parent = f"projects/{project_id}"

        try:
            # List instances first
            response = instance_client.list_instances(parent=parent)
            instances = response.instances # Instances list directly from response
            failed_locations = response.failed_locations # Locations that couldn't be reached

            if failed_locations:
                 logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Failed to list instances from locations: {failed_locations}")

            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(instances)} Bigtable instances...")

            for instance in instances:
                instance_name = instance.display_name # Use display name as per PS script
                instance_id = instance.name.split('/')[-1] # Get the actual instance ID
                instance_parent = f"{parent}/instances/{instance_id}"
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Listing clusters for instance '{instance_name}' ({instance_id})...")

                try:
                    # List clusters for the current instance
                    cluster_response = instance_client.list_clusters(parent=instance_parent)
                    clusters = cluster_response.clusters
                    failed_cluster_locations = cluster_response.failed_locations

                    if failed_cluster_locations:
                        logger.warning(f"[{project_id}][{instance_name}] Failed to list clusters from locations: {failed_cluster_locations}")

                    if not clusters:
                         logger.debug(f"[{project_id}][{instance_name}] No clusters found for this instance.")
                         # Optionally add an entry for the instance itself if it has no clusters
                         # info = { "ProjectID": project_id, "Name": instance_name, ... instance details ..., "Clusters": [] }
                         # inventory.append(info)
                         continue # Move to next instance if no clusters

                    for cluster in clusters:
                        parsed_cluster_parts = _parse_bigtable_cluster_name(cluster.name)
                        cluster_short_name = parsed_cluster_parts[3] if parsed_cluster_parts else cluster.name

                        # Prepare info dictionary per cluster, including instance info
                        info = {
                            "ProjectID": project_id,
                            "Name": instance_name, # Instance Display Name
                            "InstanceId": instance_id, # Added instance ID
                            "InstanceCreationDate": timestamp_to_iso(instance.create_time),
                            "InstanceType": str(instance.type_), # PRODUCTION, DEVELOPMENT
                            "InstanceState": str(instance.state), # READY, CREATING etc.
                            "InstanceLabels": dict(instance.labels) if instance.labels else {}, # Added field
                            # Cluster Details
                            "ClusterName": cluster_short_name,
                            "Location": _parse_bigtable_location(cluster.location),
                            "ClusterState": str(cluster.state), # READY, CREATING, RESIZING etc.
                            "ClusterServeNodes": cluster.serve_nodes,
                            "ClusterDefaultStorageType": str(cluster.default_storage_type), # SSD, HDD
                            "ClusterEncryptionType": str(cluster.encryption_config.kms_key_name) if cluster.encryption_config else "GOOGLE_DEFAULT_ENCRYPTION", # Added field
                            "service": self.SERVICE_NAME
                        }
                        inventory.append(info)

                except api_exceptions.Forbidden as e:
                    logger.error(f"[{project_id}][{instance_name}] Permission denied listing clusters: {e}")
                except Exception as e:
                    logger.error(f"[{project_id}][{instance_name}] Failed to list or process clusters: {e}", exc_info=True)


        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Bigtable instances. Ensure API is enabled and necessary roles granted (e.g., 'Bigtable Reader'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Bigtable instances: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            # Ensure the client is closed
            if instance_client:
                instance_client.transport.close()

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Bigtable instance/cluster fetch. Found {len(inventory)} clusters.")
        return inventory
