# --- File: gcp_inventory_tool/fetchers/pubsub.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import pubsub_v1
from google.api_core import exceptions as api_exceptions

from ..utils.duration_to_second import duration_to_seconds_str
from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


# Helper function to parse project and name from a resource path
def _parse_pubsub_resource_path(path: str) -> Optional[Tuple[str, str]]:
    """Parses project and name from a Pub/Sub resource path."""
    # Format: projects/{project}/resource_type/{name}
    if not isinstance(path, str):
        logger.warning(f"Invalid path type for parsing: {type(path)}")
        return None
    parts = path.split('/')
    try:
        if len(parts) == 4 and parts[0] == 'projects' and parts[2] in ['topics', 'subscriptions', 'schemas', 'snapshots']:
            project_id = parts[1]
            resource_name = parts[3]
            return project_id, resource_name
        else:
            logger.warning(f"Unrecognized resource path format for parsing: {path}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing resource path '{path}': {e}")
        return None



class PubSubFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Pub/Sub subscription details for a project.
    Maps details similarly to the provided PowerShell script's output.
    """
    SERVICE_NAME = "pubsub" # Must match the key in config.yaml

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Pub/Sub subscriptions in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Pub/Sub subscription fetch...")
        inventory = []
        project_path = f"projects/{project_id}"

        # Use a context manager for the client to ensure it's always closed properly.
        try:
            with pubsub_v1.SubscriberClient(credentials=credentials) as subscriber_client:
                # The list_subscriptions method returns a pager that yields FULL subscription objects.
                # This avoids the N+1 problem of getting details for each subscription individually.
                # We also add a timeout for robustness against network issues.
                subscription_pager = subscriber_client.list_subscriptions(
                    request={"project": project_path},
                    timeout=240.0 # Set a generous timeout (e.g., 4 minutes)
                )

                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Iterating through subscriptions...")
                
                # Iterate directly over the full subscription objects from the pager.
                for subscription in subscription_pager:
                    try:
                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": get_resource_name(subscription.name),
                            "State": str(subscription.state) if subscription.state else None,
                            "AckDeadlineSeconds": subscription.ack_deadline_seconds,
                            "MessageRetentionDuration": duration_to_seconds_str(subscription.message_retention_duration),
                            "ExactlyOnceDelivery": subscription.enable_exactly_once_delivery,
                            "MessageOrdering": subscription.enable_message_ordering,
                            "Attributes": None,
                            "Endpoint": None,
                            "Topic": None,
                            "TopicProject": None,
                            "DeadLetterTopic": None,
                            "DeadLetterTopicProject": None,
                            "DeliveryAttempts": None,
                            "MaxBackoff": None,
                            "MinBackoff": None,
                            "TTL": None,
                            "service": self.SERVICE_NAME
                        }

                        # Push Configuration
                        if subscription.push_config:
                            info["Endpoint"] = subscription.push_config.push_endpoint
                            info["Attributes"] = dict(subscription.push_config.attributes) if subscription.push_config.attributes else None

                        # Topic Info
                        if subscription.topic:
                            topic_project, topic_name = _parse_pubsub_resource_path(subscription.topic) or (None, None)
                            info["Topic"] = topic_name
                            info["TopicProject"] = topic_project

                        # Dead Letter Policy
                        if subscription.dead_letter_policy:
                            info["DeliveryAttempts"] = subscription.dead_letter_policy.max_delivery_attempts
                            if subscription.dead_letter_policy.dead_letter_topic:
                                dl_topic_project, dl_topic_name = _parse_pubsub_resource_path(subscription.dead_letter_policy.dead_letter_topic) or (None, None)
                                info["DeadLetterTopic"] = dl_topic_name
                                info["DeadLetterTopicProject"] = dl_topic_project

                        # Retry Policy
                        if subscription.retry_policy:
                            info["MinBackoff"] = duration_to_seconds_str(subscription.retry_policy.minimum_backoff)
                            info["MaxBackoff"] = duration_to_seconds_str(subscription.retry_policy.maximum_backoff)

                        # Expiration Policy
                        if subscription.expiration_policy:
                            info["TTL"] = duration_to_seconds_str(subscription.expiration_policy.ttl)

                        inventory.append(info)

                    except Exception as e:
                        # This error is now for processing a single subscription from the list, not fetching it.
                        logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to process details for subscription {subscription.name}: {e}", exc_info=True)

        except api_exceptions.DeadlineExceeded as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Timed out while listing subscriptions. The timeout is set to 240 seconds. This may indicate an issue with the Pub/Sub API or extreme numbers of subscriptions. Details: {e}")
            return [] # Fail gracefully
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing subscriptions. Ensure 'Pub/Sub API' is enabled and the service account has 'Pub/Sub Viewer' role. Details: {e}")
            return []
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Pub/Sub API might not be enabled or project not found. Details: {e}")
             return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] An unexpected error occurred while fetching Pub/Sub subscriptions: {e}", exc_info=True)
            return []

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Pub/Sub subscription fetch. Found details for {len(inventory)} subscriptions.")
        return inventory