# --- File: gcp_inventory_tool/fetchers/security_policy.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

from ..utils.resource_name import get_resource_name
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class SecurityPolicyFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Security Policy details for a project.
    Includes nested rule information. Security Policies can be global or regional.
    """
    SERVICE_NAME = "security_policy" # Unique key for this service type

    def _format_match_config(self, config: Optional[compute_v1.SecurityPolicyRuleMatcherConfig]) -> Optional[Dict]:
        """Formats the match configuration."""
        if not config:
            return None
        return {"src_ip_ranges": list(config.src_ip_ranges) if config.src_ip_ranges else []}

    def _format_match_expr(self, expr: Optional[compute_v1.Expr]) -> Optional[Dict]:
         """Formats the match expression."""
         if not expr:
             return None
         return {"expression": expr.expression, "title": expr.title, "description": expr.description, "location": expr.location}


    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Security Policies in the specified project across all regions and global.
        Returns one item per policy, with rules nested.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Security Policy fetch...")
        inventory = []
        # Security policies use a different client than network firewall policies
        client = compute_v1.SecurityPoliciesClient(credentials=credentials)

        try:
            # Use aggregated list to get policies from all regions and global
            agg_list = client.aggregated_list(project=project_id)
            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing security policies across regions/global...")

            # The response is an iterator of tuples (scope, response_object)
            for scope, response in agg_list:
                if response.security_policies:
                    scope_name = get_resource_name(scope) # Region name or 'global'
                    is_global = "global" in scope.lower()
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing {len(response.security_policies)} policies in scope {scope_name}...")

                    for policy in response.security_policies:
                        # Prepare info dictionary for the policy
                        info = {
                            "ProjectID": project_id,
                            "Name": policy.name,
                            "Description": policy.description,
                            "CreationDate": policy.creation_timestamp,
                            "Type": str(policy.type_), # CLOUD_ARMOR, CLOUD_ARMOR_EDGE, etc.
                            "Location": scope_name if scope_name else ("global" if is_global else "Unknown"),
                            "Fingerprint": policy.fingerprint, # Added field
                            "Rules": [], # Populated below
                            "Id": str(policy.id), # Added ID
                            "Labels": dict(policy.labels) if policy.labels else {}, # Added field
                            "service": self.SERVICE_NAME
                        }

                        # Process Rules
                        if policy.rules:
                            for rule in policy.rules:
                                rule_info = {
                                    "Priority": rule.priority,
                                    "Action": rule.action,
                                    "Description": rule.description,
                                    "Kind": rule.kind, # Seems to be always compute#securityPolicyRule based on API doc
                                    "Preview": rule.preview,
                                    # Match data (simplified, expand if needed)
                                    "MatchConfig": self._format_match_config(rule.match.config) if rule.match else None,
                                    "MatchExpr": self._format_match_expr(rule.match.expr) if rule.match else None,
                                    "MatchVersionedExpr": str(rule.match.versioned_expr) if rule.match else None,
                                    # Rate Limit Options (if applicable)
                                    "RateLimitThresholdCount": rule.rate_limit_options.rate_limit_threshold.count if rule.rate_limit_options and rule.rate_limit_options.rate_limit_threshold else None,
                                    "RateLimitThresholdIntervalSec": rule.rate_limit_options.rate_limit_threshold.interval_sec if rule.rate_limit_options and rule.rate_limit_options.rate_limit_threshold else None,
                                    "RateLimitConformAction": str(rule.rate_limit_options.conform_action) if rule.rate_limit_options else None,
                                    "RateLimitExceedAction": str(rule.rate_limit_options.exceed_action) if rule.rate_limit_options else None,
                                    # Add other fields like redirect_options, header_action if needed
                                }
                                info["Rules"].append(rule_info)

                        inventory.append(info)

        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing security policies or accessing Compute API. Ensure API is enabled and necessary roles granted (e.g., Compute Security Admin/Viewer). Details: {e}")
            return [] # Return empty list on permission errors
        except api_exceptions.NotFound as e:
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Compute API might not be enabled or project not found. Details: {e}")
             return [] # API not enabled or project issue
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Security Policies: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Security Policy fetch. Found {len(inventory)} policies.")
        return inventory
