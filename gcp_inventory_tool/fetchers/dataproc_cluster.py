# # --- File: gcp_inventory_tool/fetchers/dataproc_cluster.py ---
# import logging
# from typing import List, Dict, Any, Optional
# from google.oauth2.credentials import Credentials
# from google.cloud import dataproc_v1
# from google.api_core import exceptions as api_exceptions

# from ..utils.get_regions import get_available_regions # For handling proto types
# from ..utils.timestamp_to_iso import timestamp_to_iso
# from ..core.base_fetcher import ServiceFetcher

# logger = logging.getLogger('gcp_inventory')


# class DataprocClusterFetcher(ServiceFetcher):
#     """
#     Fetches Google Cloud Dataproc cluster details for a project.
#     Lists clusters across dynamically fetched available regions.
#     """
#     SERVICE_NAME = "dataproc_cluster" # Unique key for this service type

#     def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
#         """
#         Fetches details for all Dataproc clusters in the specified project across available regions.
#         """
#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Dataproc cluster fetch...")
#         inventory = []
#         client = None # Initialize client to None
#         regions_to_check = [] # Initialize regions list

#         try: # Outer try block starts here
#             client = dataproc_v1.ClusterControllerClient(credentials=credentials)

#             # --- Dynamically fetch regions ---
#             regions_to_check = get_available_regions(project_id, credentials)
#             if not regions_to_check:
#                  logger.warning(f"[{project_id}][{self.SERVICE_NAME}] No regions found or fetched. Skipping Dataproc cluster check.")
#                  # Need to close client here if regions fail
#                  if client:
#                      client.transport.close()
#                  return []
#             # ---------------------------------

#             for region in regions_to_check:
#                 # Indentation level 1 inside the try block
#                 logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Checking region {region} for Dataproc clusters...")
#                 try: # Inner try for processing a specific region
#                     request = dataproc_v1.ListClustersRequest(project_id=project_id, region=region)
#                     # Set a reasonable page size to avoid potential memory issues with very large lists
#                     # The iterator handles pagination automatically.
#                     clusters = client.list_clusters(request=request, page_size=100)

#                     for cluster in clusters:
#                         # Indentation level 2 inside the try block
#                         # Prepare info dictionary based on PowerShell script's fields
#                         info = {
#                             "ProjectID": cluster.project_id,
#                             "Name": cluster.cluster_name,
#                             "ClusterUuid": cluster.cluster_uuid, # Added field
#                             "Labels": dict(cluster.labels) if cluster.labels else {},
#                             "HDFSMetrics": {}, # Populated below
#                             "YarnMetrics": {}, # Populated below
#                             "Status": str(cluster.status.state), # RUNNING, ERROR, CREATING etc.
#                             "StatusDetail": cluster.status.detail, # Added field
#                             "StateStartTime": timestamp_to_iso(cluster.status.state_start_time),
#                             "ConfigBucket": None,
#                             "TempBucket": None,
#                             "EncryptionConfig": None, # Dict {gce_pd_kms_key_name}
#                             "EndpointConfig": None, # Dict {http_ports, enable_http_port_access}
#                             "GceClusterConfig": None, # Dict with network, zone, etc.
#                             "MasterConfig": None, # Dict with vm details
#                             "WorkerConfig": None, # Added field
#                             "SecondaryWorkerConfig": None, # Added field
#                             "SecurityConfig": None, # Dict with kerberos config
#                             "SoftwareConfig": None, # Dict with image version, properties etc.
#                             "Region": region, # Region being iterated
#                             "service": self.SERVICE_NAME
#                         }

#                         # Populate Metrics
#                         if cluster.metrics:
#                             info["HDFSMetrics"] = dict(cluster.metrics.hdfs_metrics) if cluster.metrics.hdfs_metrics else {}
#                             info["YarnMetrics"] = dict(cluster.metrics.yarn_metrics) if cluster.metrics.yarn_metrics else {}

#                         # Populate Config details
#                         if cluster.config:
#                             config = cluster.config
#                             info["ConfigBucket"] = config.config_bucket
#                             info["TempBucket"] = config.temp_bucket # Corrected field name from PS script
#                             if config.encryption_config:
#                                 info["EncryptionConfig"] = {"gce_pd_kms_key_name": config.encryption_config.gce_pd_kms_key_name}
#                             if config.endpoint_config:
#                                  info["EndpointConfig"] = {
#                                      "http_ports": dict(config.endpoint_config.http_ports),
#                                      "enable_http_port_access": config.endpoint_config.enable_http_port_access
#                                  }
#                             if config.gce_cluster_config:
#                                  # Extract key fields, object can be large
#                                  gcc = config.gce_cluster_config
#                                  info["GceClusterConfig"] = {
#                                      "network_uri": gcc.network_uri,
#                                      "subnetwork_uri": gcc.subnetwork_uri,
#                                      "internal_ip_only": gcc.internal_ip_only,
#                                      "zone_uri": gcc.zone_uri,
#                                      "service_account": gcc.service_account,
#                                      "tags": list(gcc.tags),
#                                      "metadata": dict(gcc.metadata)
#                                  }
#                             if config.master_config:
#                                  mc = config.master_config
#                                  info["MasterConfig"] = {
#                                      "num_instances": mc.num_instances,
#                                      "machine_type_uri": mc.machine_type_uri,
#                                      "disk_type": mc.disk_config.boot_disk_type,
#                                      "disk_size_gb": mc.disk_config.boot_disk_size_gb,
#                                      "image_uri": mc.image_uri,
#                                      # Add accelerators if needed
#                                  }
#                             if config.worker_config: # Added
#                                  wc = config.worker_config
#                                  info["WorkerConfig"] = {
#                                      "num_instances": wc.num_instances,
#                                      "machine_type_uri": wc.machine_type_uri,
#                                      "disk_type": wc.disk_config.boot_disk_type,
#                                      "disk_size_gb": wc.disk_config.boot_disk_size_gb,
#                                      "image_uri": wc.image_uri,
#                                  }
#                             if config.secondary_worker_config: # Added
#                                  swc = config.secondary_worker_config
#                                  info["SecondaryWorkerConfig"] = {
#                                      "num_instances": swc.num_instances,
#                                      "machine_type_uri": swc.machine_type_uri,
#                                      "disk_type": swc.disk_config.boot_disk_type,
#                                      "disk_size_gb": swc.disk_config.boot_disk_size_gb,
#                                      "image_uri": swc.image_uri,
#                                  }
#                             if config.security_config and config.security_config.kerberos_config: # Added
#                                  info["SecurityConfig"] = {"kerberos_enabled": True} # Simplified
#                             if config.software_config: # Added
#                                  sc = config.software_config
#                                  info["SoftwareConfig"] = {
#                                      "image_version": sc.image_version,
#                                      "properties": dict(sc.properties),
#                                      "optional_components": [str(comp) for comp in sc.optional_components]
#                                  }

#                         inventory.append(info)

#                 # Correctly indented except blocks for the inner try (region processing)
#                 except api_exceptions.NotFound as e:
#                      logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Dataproc API/Region not found or enabled for region '{region}'. Details: {e}")
#                 except api_exceptions.Forbidden as e:
#                     logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Dataproc clusters in region '{region}'. Details: {e}")
#                 except Exception as e:
#                     logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Dataproc clusters in region '{region}': {e}", exc_info=True)
#                 # Continue to the next region even if one fails

#         # Correctly indented except blocks for the outer try (initial setup/client creation)
#         except Exception as e:
#              logger.error(f"[{project_id}][{self.SERVICE_NAME}] Unexpected error during Dataproc fetch setup or region iteration: {e}", exc_info=True)
#              # No need to explicitly return here, finally block will execute
#              # and function will return inventory (which might be empty)
#         # Correctly indented finally block for the outer try
#         finally:
#             # Ensure the client is closed if it was successfully initialized
#             if client:
#                 try:
#                     client.transport.close()
#                     logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Dataproc client transport closed.")
#                 except Exception as close_e:
#                      logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error closing Dataproc client transport: {close_e}")

#         logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Dataproc cluster fetch. Found {len(inventory)} clusters across checked regions.")
#         return inventory

# --- File: gcp_inventory_tool/fetchers/dataproc_cluster.py ---
import logging
from typing import List, Dict, Any, Optional

from google.oauth2.credentials import Credentials
from google.cloud import dataproc_v1
from google.api_core.client_options import ClientOptions # Added for regional endpoints
from google.cloud.dataproc_v1.types import ClusterStatus, Component # Added for enum names
from google.api_core import exceptions as api_exceptions

from ..utils.get_regions import get_available_regions
from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')


class DataprocClusterFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Dataproc cluster details for a project.
    Lists clusters across dynamically fetched available regions, using per-region clients.
    """
    SERVICE_NAME = "dataproc_cluster" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Dataproc clusters in the specified project across available regions.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Dataproc cluster fetch...")
        inventory = []
        # Client initialization moved inside the regional loop

        regions_to_check = []

        try: # Outer try block for fetching regions and overall iteration
            # --- Dynamically fetch regions ---
            regions_to_check = get_available_regions(project_id, credentials) # Kept original call signature
            if not regions_to_check:
                 logger.warning(f"[{project_id}][{self.SERVICE_NAME}] No regions found or fetched by get_available_regions. Skipping Dataproc cluster check.")
                 return [] # No regions to check
            logger.info(f"[{project_id}][{self.SERVICE_NAME}] Will check Dataproc clusters in regions: {regions_to_check}")
            # ---------------------------------

            for region in regions_to_check:
                regional_client: Optional[dataproc_v1.ClusterControllerClient] = None # Initialize for this region's finally block
                logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Checking region {region} for Dataproc clusters...")
                try: # Inner try for processing a specific region
                    api_endpoint = f"{region}-dataproc.googleapis.com"
                    client_options = ClientOptions(api_endpoint=api_endpoint)
                    
                    regional_client = dataproc_v1.ClusterControllerClient(
                        credentials=credentials,
                        client_options=client_options
                    )

                   
                      # MODIFICATION: Set page_size within the ListClustersRequest object
                    request = dataproc_v1.ListClustersRequest(
                        project_id=project_id,
                        region=region,
                        page_size=100  # Set page_size in the request
                    )
                    clusters = regional_client.list_clusters(request=request)
                    for cluster in clusters:
                        status_name = "UNKNOWN_STATE"
                        if cluster.status and hasattr(cluster.status, 'state'):
                            try:
                                status_name = ClusterStatus.State(cluster.status.state).name
                            except ValueError:
                                logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Unknown status state value '{cluster.status.state}' for cluster {cluster.cluster_name} in {region}.")
                                status_name = str(cluster.status.state) # Fallback

                        info = {
                            "ProjectID": cluster.project_id,
                            "Name": cluster.cluster_name,
                            "FullName": cluster.name, # Storing full resource name
                            "ClusterUuid": cluster.cluster_uuid,
                            "Labels": dict(cluster.labels) if cluster.labels else {},
                            "HDFSMetrics": {},
                            "YarnMetrics": {},
                            "Status": status_name,
                            "StatusDetail": cluster.status.detail if cluster.status else None,
                            "StateStartTime": timestamp_to_iso(cluster.status.state_start_time) if cluster.status else None,
                            "ConfigBucket": None,
                            "TempBucket": None,
                            "EncryptionConfig": None,
                            "EndpointConfig": None,
                            "GceClusterConfig": None,
                            "MasterConfig": None,
                            "WorkerConfig": None,
                            "SecondaryWorkerConfig": None,
                            "SecurityConfig": None,
                            "SoftwareConfig": None,
                            "Region": region,
                            "service": self.SERVICE_NAME
                        }

                        if cluster.metrics:
                            info["HDFSMetrics"] = dict(cluster.metrics.hdfs_metrics) if cluster.metrics.hdfs_metrics else {}
                            info["YarnMetrics"] = dict(cluster.metrics.yarn_metrics) if cluster.metrics.yarn_metrics else {}

                        if cluster.config:
                            config = cluster.config
                            info["ConfigBucket"] = config.config_bucket
                            info["TempBucket"] = config.temp_bucket
                            if config.encryption_config:
                                info["EncryptionConfig"] = {"gce_pd_kms_key_name": config.encryption_config.gce_pd_kms_key_name}
                            if config.endpoint_config:
                                 info["EndpointConfig"] = {
                                     "http_ports": dict(config.endpoint_config.http_ports),
                                     "enable_http_port_access": config.endpoint_config.enable_http_port_access
                                 }
                            if config.gce_cluster_config:
                                 gcc = config.gce_cluster_config
                                 info["GceClusterConfig"] = {
                                     "network_uri": gcc.network_uri,
                                     "subnetwork_uri": gcc.subnetwork_uri,
                                     "internal_ip_only": gcc.internal_ip_only,
                                     "zone_uri": gcc.zone_uri,
                                     "service_account": gcc.service_account,
                                     "tags": list(gcc.tags),
                                     "metadata": dict(gcc.metadata)
                                 }
                            
                            def _get_instance_config_details(instance_config):
                                if not instance_config: return None
                                disk_details = {}
                                if instance_config.disk_config: # Check if disk_config exists
                                    disk_details["disk_type"] = instance_config.disk_config.boot_disk_type
                                    disk_details["disk_size_gb"] = instance_config.disk_config.boot_disk_size_gb
                                    # Add other disk_config fields if needed, e.g., num_local_ssds
                                return {
                                     "num_instances": instance_config.num_instances,
                                     "machine_type_uri": instance_config.machine_type_uri,
                                     "image_uri": instance_config.image_uri,
                                     **disk_details # Merge disk details
                                }

                            info["MasterConfig"] = _get_instance_config_details(config.master_config)
                            info["WorkerConfig"] = _get_instance_config_details(config.worker_config)
                            info["SecondaryWorkerConfig"] = _get_instance_config_details(config.secondary_worker_config)
                            
                            if config.security_config and config.security_config.kerberos_config:
                                 info["SecurityConfig"] = {"kerberos_enabled": config.security_config.kerberos_config.enable_kerberos}
                            if config.software_config:
                                 sc = config.software_config
                                 optional_components_names = []
                                 if sc.optional_components:
                                     for comp_val in sc.optional_components:
                                         try:
                                             optional_components_names.append(Component(comp_val).name)
                                         except ValueError:
                                             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Unknown component value '{comp_val}' for cluster {cluster.cluster_name} in {region}.")
                                             optional_components_names.append(str(comp_val)) # Fallback to raw string
                                 info["SoftwareConfig"] = {
                                     "image_version": sc.image_version,
                                     "properties": dict(sc.properties),
                                     "optional_components": optional_components_names
                                 }
                        inventory.append(info)

                except api_exceptions.NotFound as e:
                     logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Dataproc API/Region not found or no clusters in region '{region}'. Details: {e}")
                except api_exceptions.Forbidden as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Dataproc clusters in region '{region}'. Details: {e}")
                except api_exceptions.InvalidArgument as e: # Catch specific error from past tracebacks
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Invalid argument for Dataproc in region '{region}'. This may be an API configuration or endpoint issue. Details: {e}", exc_info=True)
                except Exception as e:
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Dataproc clusters in region '{region}': {e}", exc_info=True)
                finally:
                    if regional_client:
                        try:
                            if hasattr(regional_client, 'close'):
                                regional_client.close()
                            elif hasattr(regional_client, 'transport') and hasattr(regional_client.transport, 'close'): # Fallback for some client types
                                regional_client.transport.close()
                            logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Dataproc regional client for {region} closed.")
                        except Exception as close_e:
                             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Error closing Dataproc regional client for {region}: {close_e}")
                # Continue to the next region

        except Exception as e: # Outer try-except for issues like get_available_regions failing
             logger.error(f"[{project_id}][{self.SERVICE_NAME}] Unexpected error during Dataproc fetch setup or region iteration: {e}", exc_info=True)
        # No outer finally block for client closure as it's handled per-region

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Dataproc cluster fetch. Found {len(inventory)} clusters across {len(regions_to_check)} checked regions.")
        return inventory