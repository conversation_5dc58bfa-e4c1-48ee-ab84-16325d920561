# --- File: gcp_inventory_tool/fetchers/artifact_registry_repo.py ---
import logging
from typing import List, Dict, Any, Optional, Tuple
from google.oauth2.credentials import Credentials
from google.cloud import artifactregistry_v1
from google.api_core import exceptions as api_exceptions

from ..utils.timestamp_to_iso import timestamp_to_iso
from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

# Helper function to parse project, location, repo name from a full Artifact Registry resource name
def _parse_artifact_repo_name(name: str) -> Optional[Tuple[str, str, str]]:
    """Parses project, location, and repository name from an Artifact Registry resource name."""
    # Format: projects/{project}/locations/{location}/repositories/{repo_name}
    if not isinstance(name, str):
        logger.warning(f"Invalid name type for parsing: {type(name)}")
        return None
    parts = name.split('/')
    try:
        if len(parts) == 6 and parts[0] == 'projects' and parts[2] == 'locations' and parts[4] == 'repositories':
            project_id = parts[1]
            location_id = parts[3]
            repo_name = parts[5]
            return project_id, location_id, repo_name
        else:
            logger.warning(f"Unrecognized Artifact Registry repository name format for parsing: {name}")
            return None
    except (ValueError, IndexError) as e:
        logger.error(f"Error parsing Artifact Registry repository name '{name}': {e}")
        return None


class ArtifactRegistryRepositoryFetcher(ServiceFetcher):
    """
    Fetches Google Cloud Artifact Registry repository details for a project.
    Lists repositories across all locations.
    """
    SERVICE_NAME = "artifact_registry_repo" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all Artifact Registry repositories in the specified project across all locations.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting Artifact Registry repository fetch...")
        inventory = []
        client = artifactregistry_v1.ArtifactRegistryClient(credentials=credentials)

        # Use a predefined list of common GCP regions instead of locations/-
        locations = ['us-central1', 'us-east1', 'us-east4', 'us-west1', 'us-west2',
                    'europe-west1', 'europe-west2', 'europe-west3', 'europe-west4', 'europe-west6',
                    'asia-east1', 'asia-northeast1', 'asia-south1', 'asia-southeast1',
                    'australia-southeast1', 'northamerica-northeast1', 'southamerica-east1']

        try:
            for location in locations:
                try:
                    parent = f"projects/{project_id}/locations/{location}"
                    repositories = client.list_repositories(parent=parent)
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing Artifact Registry repositories in location {location}...")

                    for repo in repositories:
                        parsed_name_parts = _parse_artifact_repo_name(repo.name)
                        repo_location = parsed_name_parts[1] if parsed_name_parts else location
                        short_name = parsed_name_parts[2] if parsed_name_parts else repo.name # Fallback

                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": project_id,
                            "Name": short_name,
                            "Location": repo_location,
                            "CreationDate": timestamp_to_iso(repo.create_time),
                            "UpdationDate": timestamp_to_iso(repo.update_time),
                            "Labels": dict(repo.labels) if repo.labels else {},
                            "Format": str(repo.format_), # DOCKER, MAVEN, NPM etc.
                            "Description": repo.description, # Added field
                            "KmsKeyName": repo.kms_key_name, # Added field
                            "SizeBytes": repo.size_bytes, # Added field
                            "service": self.SERVICE_NAME
                        }
                        inventory.append(info)

                except api_exceptions.NotFound:
                    # This location might not have Artifact Registry enabled or no repos
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] No repositories found in location {location}")
                    continue
                except api_exceptions.Forbidden:
                    # Permission denied for this specific location
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Permission denied for location {location}")
                    continue
                except Exception as e:
                    logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Failed to list repositories in location {location}: {e}")
                    continue

        except api_exceptions.NotFound as e:
             # This might indicate the API isn't enabled
             logger.warning(f"[{project_id}][{self.SERVICE_NAME}] Artifact Registry API might not be enabled or project not found. Details: {e}")
             return []
        except api_exceptions.Forbidden as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing Artifact Registry repositories. Ensure API is enabled and necessary roles granted (e.g., 'Artifact Registry Reader'). Details: {e}")
            return [] # Return empty list on permission errors
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process Artifact Registry repositories: {e}", exc_info=True)
            return [] # Fail gracefully
        finally:
            try:
                client.transport.close()
            except Exception:
                pass

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished Artifact Registry repository fetch. Found {len(inventory)} repositories.")
        return inventory
