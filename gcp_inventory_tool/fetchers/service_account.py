# --- File: gcp_inventory_tool/fetchers/service_account.py ---
import logging
from typing import List, Dict, Any, Optional
from google.oauth2.credentials import Credentials
from google.api_core import exceptions as api_exceptions
# Use googleapiclient for APIs not fully covered by dedicated libraries like iam.serviceAccounts.list
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from ..core.base_fetcher import ServiceFetcher

logger = logging.getLogger('gcp_inventory')

class ServiceAccountFetcher(ServiceFetcher):
    """
    Fetches Google Cloud IAM Service Account details for a project.
    """
    SERVICE_NAME = "service_account" # Unique key for this service type

    def fetch_resources(self, project_id: str, credentials: Credentials) -> List[Dict[str, Any]]:
        """
        Fetches details for all IAM Service Accounts in the specified project.
        """
        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Starting IAM Service Account fetch...")
        inventory = []
        service = None # Initialize service to None

        try:
            # Build the IAM service object using the discovery client
            service = build('iam', 'v1', credentials=credentials, cache_discovery=False) # Disable cache for potential updates

            # Prepare the request to list service accounts
            request = service.projects().serviceAccounts().list(name=f'projects/{project_id}')

            # Execute the request page by page
            while request is not None:
                try:
                    response = request.execute()
                    accounts = response.get('accounts', [])
                    logger.debug(f"[{project_id}][{self.SERVICE_NAME}] Processing page with {len(accounts)} service accounts...")

                    for sa in accounts:
                        # Prepare info dictionary based on PowerShell script's fields
                        info = {
                            "ProjectID": sa.get('projectId', project_id), # Use projectId from response if available
                            "Name": sa.get('displayName'),
                            "Email": sa.get('email'),
                            "Description": sa.get('description'),
                            "Disabled": sa.get('disabled', False), # Default to False if not present
                            "Etag": sa.get('etag'),
                            "Oauth2ClientId": sa.get('oauth2ClientId'), # Added field
                            "UniqueId": sa.get('uniqueId'), # Added field
                            "service": self.SERVICE_NAME
                        }
                        inventory.append(info)

                    # Get the next page token
                    request = service.projects().serviceAccounts().list_next(previous_request=request, previous_response=response)

                except HttpError as e:
                    # Handle errors during page execution (e.g., transient issues)
                    logger.error(f"[{project_id}][{self.SERVICE_NAME}] HttpError during service account page fetch: {e}", exc_info=True)
                    # Decide whether to break or continue (breaking for now)
                    break
                except Exception as page_e:
                     logger.error(f"[{project_id}][{self.SERVICE_NAME}] Error processing service account page: {page_e}", exc_info=True)
                     break


        except api_exceptions.Forbidden as e:
            # This might be caught if the initial API build/call fails
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Permission denied listing service accounts or accessing IAM API. Ensure API is enabled and necessary roles granted (e.g., 'Service Account Viewer', 'iam.serviceAccounts.list' permission). Details: {e}")
            return [] # Return empty list on permission errors
        except HttpError as e:
             # Handle errors during initial API call setup or first page
             logger.error(f"[{project_id}][{self.SERVICE_NAME}] HttpError accessing IAM API: {e}")
             # Check for common issues like API not enabled
             if "service not found" in str(e).lower() or "service has been disabled" in str(e).lower():
                  logger.warning(f"[{project_id}][{self.SERVICE_NAME}] IAM API might not be enabled for this project.")
             return []
        except Exception as e:
            logger.error(f"[{project_id}][{self.SERVICE_NAME}] Failed to list or process IAM Service Accounts: {e}", exc_info=True)
            return [] # Fail gracefully for other errors
        # No explicit client.close() needed for googleapiclient discovery client

        logger.info(f"[{project_id}][{self.SERVICE_NAME}] Finished IAM Service Account fetch. Found {len(inventory)} service accounts.")
        return inventory
