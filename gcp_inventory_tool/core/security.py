"""
Security and compliance module for GCP Inventory Tool.
Handles data sanitization, audit logging, and security best practices.
"""
import logging
import hashlib
import re
import json
import time
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from pathlib import Path
import threading
from datetime import datetime, timezone

logger = logging.getLogger(__name__)


@dataclass
class AuditEvent:
    """Represents an audit event."""
    timestamp: str
    event_type: str
    user_id: str
    project_id: str
    service: str
    action: str
    resource_count: int
    success: bool
    error_message: Optional[str] = None
    duration_ms: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None


class SecurityManager:
    """Manages security aspects of the inventory tool."""
    
    def __init__(self, config: 'InventoryConfig'):
        self.config = config
        self.audit_events: List[AuditEvent] = []
        self.audit_lock = threading.Lock()
        
        # Compile regex patterns for sensitive data detection
        self.sensitive_patterns = self._compile_sensitive_patterns()
        
        # Initialize audit logging
        if config.compliance.enable_audit_trail:
            self._setup_audit_logging()
    
    def _compile_sensitive_patterns(self) -> Dict[str, re.Pattern]:
        """Compile regex patterns for detecting sensitive data."""
        patterns = {
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'ip_address': re.compile(r'\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b'),
            'phone': re.compile(r'\b\d{3}-\d{3}-\d{4}\b|\b\(\d{3}\)\s*\d{3}-\d{4}\b'),
            'ssn': re.compile(r'\b\d{3}-\d{2}-\d{4}\b'),
            'credit_card': re.compile(r'\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b'),
            'api_key': re.compile(r'[A-Za-z0-9]{32,}'),
            'jwt_token': re.compile(r'eyJ[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*'),
            'gcp_service_account_key': re.compile(r'"type":\s*"service_account"'),
        }
        return patterns
    
    def _setup_audit_logging(self) -> None:
        """Setup audit logging infrastructure."""
        audit_path = Path(self.config.compliance.audit_log_path)
        audit_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create audit logger
        self.audit_logger = logging.getLogger('gcp_inventory.audit')
        self.audit_logger.setLevel(logging.INFO)
        
        # Create file handler for audit logs
        audit_handler = logging.FileHandler(audit_path)
        audit_formatter = logging.Formatter(
            '%(asctime)s - AUDIT - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S UTC'
        )
        audit_handler.setFormatter(audit_formatter)
        self.audit_logger.addHandler(audit_handler)
    
    def sanitize_data(self, data: Any, field_name: str = "") -> Any:
        """
        Sanitize data by removing or masking sensitive information.
        
        Args:
            data: Data to sanitize
            field_name: Name of the field (for context)
            
        Returns:
            Sanitized data
        """
        if not self.config.security.mask_sensitive_data:
            return data
        
        if isinstance(data, str):
            return self._sanitize_string(data, field_name)
        elif isinstance(data, dict):
            return self._sanitize_dict(data)
        elif isinstance(data, list):
            return [self.sanitize_data(item, field_name) for item in data]
        else:
            return data
    
    def _sanitize_string(self, text: str, field_name: str) -> str:
        """Sanitize string data."""
        # Check if field name indicates sensitive data
        if any(sensitive_field in field_name.lower() 
               for sensitive_field in self.config.security.sensitive_fields):
            return self._mask_string(text)
        
        # Check for sensitive patterns
        sanitized = text
        for pattern_name, pattern in self.sensitive_patterns.items():
            if pattern.search(sanitized):
                if pattern_name in ['email', 'ip_address']:
                    # Partial masking for some types
                    sanitized = pattern.sub(lambda m: self._partial_mask(m.group()), sanitized)
                else:
                    # Full masking for highly sensitive data
                    sanitized = pattern.sub('[REDACTED]', sanitized)
        
        return sanitized
    
    def _sanitize_dict(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Sanitize dictionary data."""
        sanitized = {}
        for key, value in data.items():
            sanitized[key] = self.sanitize_data(value, key)
        return sanitized
    
    def _mask_string(self, text: str) -> str:
        """Mask a string with asterisks."""
        if len(text) <= 4:
            return '*' * len(text)
        return text[:2] + '*' * (len(text) - 4) + text[-2:]
    
    def _partial_mask(self, text: str) -> str:
        """Partially mask sensitive data (e.g., email, IP)."""
        if '@' in text:  # Email
            local, domain = text.split('@', 1)
            return f"{local[:2]}***@{domain}"
        elif '.' in text and text.replace('.', '').isdigit():  # IP address
            parts = text.split('.')
            return f"{parts[0]}.{parts[1]}.***.***.***"
        return self._mask_string(text)
    
    def log_audit_event(self, event_type: str, user_id: str, project_id: str, 
                       service: str, action: str, resource_count: int, 
                       success: bool, error_message: Optional[str] = None,
                       duration_ms: Optional[int] = None,
                       metadata: Optional[Dict[str, Any]] = None) -> None:
        """Log an audit event."""
        if not self.config.compliance.enable_audit_trail:
            return
        
        event = AuditEvent(
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type=event_type,
            user_id=user_id,
            project_id=project_id,
            service=service,
            action=action,
            resource_count=resource_count,
            success=success,
            error_message=error_message,
            duration_ms=duration_ms,
            metadata=metadata
        )
        
        with self.audit_lock:
            self.audit_events.append(event)
            
            # Log to audit file
            if hasattr(self, 'audit_logger'):
                audit_data = {
                    'timestamp': event.timestamp,
                    'event_type': event.event_type,
                    'user_id': event.user_id,
                    'project_id': event.project_id,
                    'service': event.service,
                    'action': event.action,
                    'resource_count': event.resource_count,
                    'success': event.success,
                    'error_message': event.error_message,
                    'duration_ms': event.duration_ms,
                    'metadata': event.metadata
                }
                self.audit_logger.info(json.dumps(audit_data))
    
    def validate_project_access(self, project_id: str, credentials: Any) -> bool:
        """
        Validate that the credentials have appropriate access to the project.
        
        Args:
            project_id: GCP project ID
            credentials: GCP credentials
            
        Returns:
            True if access is valid, False otherwise
        """
        try:
            # This is a placeholder for actual project access validation
            # In a real implementation, you would check IAM permissions
            logger.debug(f"Validating access to project: {project_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to validate project access for {project_id}: {e}")
            return False
    
    def generate_security_report(self) -> Dict[str, Any]:
        """Generate a security report based on audit events."""
        with self.audit_lock:
            total_events = len(self.audit_events)
            successful_events = sum(1 for event in self.audit_events if event.success)
            failed_events = total_events - successful_events
            
            # Group by service
            service_stats = {}
            for event in self.audit_events:
                if event.service not in service_stats:
                    service_stats[event.service] = {'total': 0, 'success': 0, 'failed': 0}
                service_stats[event.service]['total'] += 1
                if event.success:
                    service_stats[event.service]['success'] += 1
                else:
                    service_stats[event.service]['failed'] += 1
            
            return {
                'total_events': total_events,
                'successful_events': successful_events,
                'failed_events': failed_events,
                'success_rate': successful_events / total_events if total_events > 0 else 0,
                'service_statistics': service_stats,
                'report_generated_at': datetime.now(timezone.utc).isoformat()
            }
    
    def check_data_classification(self, data: Dict[str, Any]) -> Dict[str, str]:
        """
        Classify data based on sensitivity levels.
        
        Args:
            data: Data to classify
            
        Returns:
            Dictionary mapping field names to classification levels
        """
        if not self.config.compliance.enable_data_classification:
            return {}
        
        classifications = {}
        
        def classify_recursive(obj: Any, path: str = "") -> None:
            if isinstance(obj, dict):
                for key, value in obj.items():
                    current_path = f"{path}.{key}" if path else key
                    
                    # Check field name for sensitivity
                    if any(sensitive in key.lower() 
                           for sensitive in self.config.security.sensitive_fields):
                        classifications[current_path] = "SENSITIVE"
                    elif isinstance(value, str) and self._contains_pii(value):
                        classifications[current_path] = "PII"
                    else:
                        classifications[current_path] = "PUBLIC"
                    
                    classify_recursive(value, current_path)
            elif isinstance(obj, list):
                for i, item in enumerate(obj):
                    classify_recursive(item, f"{path}[{i}]")
        
        classify_recursive(data)
        return classifications
    
    def _contains_pii(self, text: str) -> bool:
        """Check if text contains personally identifiable information."""
        if not self.config.compliance.enable_pii_detection:
            return False
        
        pii_patterns = ['email', 'phone', 'ssn', 'credit_card']
        for pattern_name in pii_patterns:
            if pattern_name in self.sensitive_patterns:
                if self.sensitive_patterns[pattern_name].search(text):
                    return True
        return False
    
    def cleanup_old_audit_events(self, retention_days: Optional[int] = None) -> int:
        """
        Clean up old audit events based on retention policy.
        
        Args:
            retention_days: Number of days to retain events (uses config if not provided)
            
        Returns:
            Number of events removed
        """
        if retention_days is None:
            retention_days = self.config.compliance.data_retention_days
        
        cutoff_time = time.time() - (retention_days * 24 * 60 * 60)
        
        with self.audit_lock:
            initial_count = len(self.audit_events)
            self.audit_events = [
                event for event in self.audit_events
                if datetime.fromisoformat(event.timestamp.replace('Z', '+00:00')).timestamp() > cutoff_time
            ]
            removed_count = initial_count - len(self.audit_events)
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} old audit events")
            
            return removed_count
