"""
Production-ready application orchestrator for GCP Inventory Tool.
Integrates all components with proper lifecycle management.
"""
import logging
import signal
import sys
import time
import threading
from typing import Dict, List, Any, Optional
from contextlib import contextmanager
from pathlib import Path

from .settings import InventoryConfig
from .config import load_production_config
from .security import SecurityManager
from .metrics import PerformanceMonitor
from .resilience import ResilienceManager, GracefulDegradation
from .health import HealthChecker, HealthMonitor, HealthStatus
from ..utils.logging_config import setup_logging
from ..core.credentials import get_credentials

logger = logging.getLogger(__name__)


class InventoryApplication:
    """Main application class that orchestrates all components."""
    
    def __init__(self, config_path: Optional[str] = None):
        """Initialize the application with configuration."""
        # Load configuration
        self.config = load_production_config(config_path)
        
        # Setup logging
        self._setup_logging()
        
        # Initialize core components
        self.security_manager = SecurityManager(self.config)
        self.performance_monitor = PerformanceMonitor(self.config)
        self.resilience_manager = ResilienceManager(self.config)
        self.graceful_degradation = GracefulDegradation(self.config)
        self.health_checker = HealthChecker(self.config)
        self.health_monitor = HealthMonitor(self.config, self.health_checker)
        
        # Application state
        self.is_running = False
        self.shutdown_event = threading.Event()
        self.credentials_cache = {}
        
        # Setup signal handlers for graceful shutdown
        self._setup_signal_handlers()
        
        # Setup health monitoring alerts
        self._setup_health_alerts()
        
        logger.info("GCP Inventory Application initialized successfully")
    
    def _setup_logging(self) -> None:
        """Setup production logging configuration."""
        log_level = getattr(logging, self.config.monitoring.log_level.upper())
        
        # Setup structured logging if enabled
        if self.config.monitoring.enable_structured_logging:
            import json
            
            class StructuredFormatter(logging.Formatter):
                def format(self, record):
                    log_entry = {
                        'timestamp': self.formatTime(record),
                        'level': record.levelname,
                        'logger': record.name,
                        'message': record.getMessage(),
                        'module': record.module,
                        'function': record.funcName,
                        'line': record.lineno
                    }
                    
                    if record.exc_info:
                        log_entry['exception'] = self.formatException(record.exc_info)
                    
                    return json.dumps(log_entry)
            
            # Configure root logger
            root_logger = logging.getLogger()
            root_logger.setLevel(log_level)
            
            # Remove existing handlers
            for handler in root_logger.handlers[:]:
                root_logger.removeHandler(handler)
            
            # Add console handler
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(StructuredFormatter())
            root_logger.addHandler(console_handler)
            
            # Add file handler if configured
            if hasattr(self.config, 'logging') and getattr(self.config.logging, 'enable_file_logging', False):
                log_file = Path(self.config.logging.log_file)
                log_file.parent.mkdir(parents=True, exist_ok=True)
                
                file_handler = logging.FileHandler(log_file)
                file_handler.setFormatter(StructuredFormatter())
                root_logger.addHandler(file_handler)
        
        logger.info(f"Logging configured with level: {self.config.monitoring.log_level}")
    
    def _setup_signal_handlers(self) -> None:
        """Setup signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, initiating graceful shutdown...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def _setup_health_alerts(self) -> None:
        """Setup health monitoring alerts."""
        def health_alert_callback(status: HealthStatus, summary: Dict[str, Any]):
            if status == HealthStatus.UNHEALTHY:
                logger.critical(f"System health is UNHEALTHY: {summary}")
                # Here you could integrate with external alerting systems
            elif status == HealthStatus.DEGRADED:
                logger.warning(f"System health is DEGRADED: {summary}")
        
        self.health_monitor.add_alert_callback(health_alert_callback)
    
    def startup(self) -> None:
        """Start the application and all its components."""
        if self.is_running:
            logger.warning("Application is already running")
            return
        
        logger.info("Starting GCP Inventory Application...")
        
        try:
            # Start health monitoring
            if self.config.monitoring.enable_health_checks:
                self.health_monitor.start_monitoring(
                    interval=self.config.monitoring.health_check_interval
                )
            
            # Perform initial health check
            initial_health = self.health_checker.run_all_checks()
            overall_health = self.health_checker.get_overall_health()
            
            if overall_health == HealthStatus.UNHEALTHY:
                logger.error("Initial health check failed, application may not function properly")
                for check_name, result in initial_health.items():
                    if result.status == HealthStatus.UNHEALTHY:
                        logger.error(f"Health check '{check_name}' failed: {result.message}")
            
            self.is_running = True
            logger.info("GCP Inventory Application started successfully")
            
        except Exception as e:
            logger.error(f"Failed to start application: {e}")
            raise
    
    def shutdown(self) -> None:
        """Gracefully shutdown the application."""
        if not self.is_running:
            return
        
        logger.info("Shutting down GCP Inventory Application...")
        
        # Signal shutdown to all components
        self.shutdown_event.set()
        
        # Stop health monitoring
        self.health_monitor.stop_monitoring()
        
        # Stop performance monitoring
        self.performance_monitor.stop()
        
        # Generate final reports
        self._generate_shutdown_reports()
        
        self.is_running = False
        logger.info("GCP Inventory Application shutdown complete")
    
    def _generate_shutdown_reports(self) -> None:
        """Generate reports during shutdown."""
        try:
            # Performance report
            perf_summary = self.performance_monitor.get_performance_summary()
            logger.info(f"Final performance summary: {perf_summary}")
            
            # Security report
            security_report = self.security_manager.generate_security_report()
            logger.info(f"Security report: {security_report}")
            
            # Health trends
            health_trends = self.health_monitor.get_health_trends(hours=1)
            logger.info(f"Health trends: {health_trends}")
            
        except Exception as e:
            logger.error(f"Error generating shutdown reports: {e}")
    
    def get_credentials(self, project_id: str) -> Any:
        """Get cached credentials for a project."""
        if project_id not in self.credentials_cache:
            try:
                credentials, _ = get_credentials()
                
                # Validate project access
                if not self.security_manager.validate_project_access(project_id, credentials):
                    raise ValueError(f"Invalid access to project: {project_id}")
                
                self.credentials_cache[project_id] = credentials
                logger.debug(f"Cached credentials for project: {project_id}")
                
            except Exception as e:
                logger.error(f"Failed to get credentials for project {project_id}: {e}")
                raise
        
        return self.credentials_cache[project_id]
    
    def run_inventory(self, projects: Optional[List[str]] = None, 
                     services: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Run the inventory collection process.
        
        Args:
            projects: List of project IDs to scan (uses config if not provided)
            services: List of services to scan (uses config if not provided)
            
        Returns:
            Dictionary containing inventory results and metadata
        """
        if not self.is_running:
            raise RuntimeError("Application is not running. Call startup() first.")
        
        # Use configuration defaults if not provided
        target_projects = projects or self.config.projects
        target_services = services or self.config.services.enabled_services
        
        if not target_projects:
            raise ValueError("No projects specified for inventory")
        
        logger.info(f"Starting inventory for {len(target_projects)} projects")
        
        # Record start time for metrics
        start_time = time.time()
        
        try:
            # Import here to avoid circular imports
            from .. import run_inventory_for_projects
            
            # Prepare inventory parameters
            inventory_params = {
                'projects': target_projects,
                'services': target_services,
                'config': self.config,
                'security_manager': self.security_manager,
                'performance_monitor': self.performance_monitor,
                'resilience_manager': self.resilience_manager
            }
            
            # Run inventory with monitoring
            with self._inventory_context():
                results = run_inventory_for_projects(**inventory_params)
            
            # Calculate execution time
            execution_time = time.time() - start_time
            
            # Log audit event
            self.security_manager.log_audit_event(
                event_type="inventory_execution",
                user_id="system",
                project_id=",".join(target_projects),
                service="inventory",
                action="run_inventory",
                resource_count=len(results.get('resources', [])),
                success=True,
                duration_ms=int(execution_time * 1000)
            )
            
            logger.info(f"Inventory completed successfully in {execution_time:.2f} seconds")
            
            return {
                'results': results,
                'metadata': {
                    'execution_time_seconds': execution_time,
                    'projects_scanned': len(target_projects),
                    'services_scanned': len(target_services) if target_services else 'all',
                    'resources_found': len(results.get('resources', [])),
                    'timestamp': time.time()
                }
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            
            # Log audit event for failure
            self.security_manager.log_audit_event(
                event_type="inventory_execution",
                user_id="system",
                project_id=",".join(target_projects),
                service="inventory",
                action="run_inventory",
                resource_count=0,
                success=False,
                error_message=str(e),
                duration_ms=int(execution_time * 1000)
            )
            
            logger.error(f"Inventory failed after {execution_time:.2f} seconds: {e}")
            raise
    
    @contextmanager
    def _inventory_context(self):
        """Context manager for inventory execution."""
        # Pre-execution setup
        logger.info("Entering inventory execution context")
        
        # Clear any previous degradation states
        self.graceful_degradation.clear_degradation()
        
        try:
            yield
        finally:
            # Post-execution cleanup
            logger.info("Exiting inventory execution context")
            
            # Clean up old audit events
            if self.config.compliance.enable_audit_trail:
                removed_count = self.security_manager.cleanup_old_audit_events()
                if removed_count > 0:
                    logger.info(f"Cleaned up {removed_count} old audit events")
    
    def get_application_status(self) -> Dict[str, Any]:
        """Get comprehensive application status."""
        health_summary = self.health_checker.get_health_summary()
        performance_summary = self.performance_monitor.get_performance_summary()
        resilience_stats = self.resilience_manager.get_resilience_stats()
        
        return {
            'application': {
                'is_running': self.is_running,
                'environment': self.config.environment,
                'uptime_seconds': time.time() - self.performance_monitor.start_time if self.is_running else 0
            },
            'health': health_summary,
            'performance': performance_summary,
            'resilience': resilience_stats,
            'configuration': {
                'projects_count': len(self.config.projects),
                'regions_count': len(self.config.regions),
                'max_workers': self.config.performance.max_workers,
                'enable_caching': self.config.performance.enable_caching
            }
        }


def create_application(config_path: Optional[str] = None) -> InventoryApplication:
    """Factory function to create and configure the application."""
    return InventoryApplication(config_path)


def run_application_with_lifecycle(config_path: Optional[str] = None) -> None:
    """Run the application with full lifecycle management."""
    app = create_application(config_path)
    
    try:
        app.startup()
        
        # Keep the application running
        while app.is_running and not app.shutdown_event.is_set():
            time.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error(f"Application error: {e}")
        raise
    finally:
        app.shutdown()
