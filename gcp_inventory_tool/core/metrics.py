"""
Performance monitoring and metrics collection for GCP Inventory Tool.
"""
import logging
import time
import threading
import psutil
import gc
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from datetime import datetime, timezone
import json

logger = logging.getLogger(__name__)


@dataclass
class MetricPoint:
    """Represents a single metric data point."""
    timestamp: float
    value: float
    labels: Dict[str, str] = field(default_factory=dict)


@dataclass
class ServiceMetrics:
    """Metrics for a specific service."""
    service_name: str
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    total_duration_ms: float = 0
    min_duration_ms: float = float('inf')
    max_duration_ms: float = 0
    resources_fetched: int = 0
    errors: List[str] = field(default_factory=list)
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate."""
        if self.total_requests == 0:
            return 0.0
        return self.successful_requests / self.total_requests
    
    @property
    def average_duration_ms(self) -> float:
        """Calculate average duration."""
        if self.total_requests == 0:
            return 0.0
        return self.total_duration_ms / self.total_requests


class PerformanceMonitor:
    """Monitors performance metrics and system resources."""
    
    def __init__(self, config: 'InventoryConfig'):
        self.config = config
        self.metrics: Dict[str, ServiceMetrics] = {}
        self.system_metrics: deque = deque(maxlen=1000)  # Keep last 1000 data points
        self.custom_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.metrics_lock = threading.Lock()
        
        # Performance tracking
        self.start_time = time.time()
        self.active_operations: Dict[str, float] = {}
        
        # System monitoring
        self.enable_system_monitoring = config.performance.enable_memory_monitoring
        self.system_monitor_thread = None
        self.stop_monitoring = threading.Event()
        
        if self.enable_system_monitoring:
            self._start_system_monitoring()
    
    def _start_system_monitoring(self) -> None:
        """Start system resource monitoring in a separate thread."""
        def monitor_system():
            while not self.stop_monitoring.wait(10):  # Check every 10 seconds
                try:
                    cpu_percent = psutil.cpu_percent(interval=1)
                    memory = psutil.virtual_memory()
                    disk = psutil.disk_usage('/')
                    
                    system_data = {
                        'timestamp': time.time(),
                        'cpu_percent': cpu_percent,
                        'memory_percent': memory.percent,
                        'memory_used_mb': memory.used / (1024 * 1024),
                        'memory_available_mb': memory.available / (1024 * 1024),
                        'disk_percent': disk.percent,
                        'disk_used_gb': disk.used / (1024 * 1024 * 1024),
                        'disk_free_gb': disk.free / (1024 * 1024 * 1024)
                    }
                    
                    with self.metrics_lock:
                        self.system_metrics.append(system_data)
                    
                    # Check memory usage against limits
                    memory_used_mb = memory.used / (1024 * 1024)
                    if memory_used_mb > self.config.performance.max_memory_usage_mb:
                        logger.warning(f"Memory usage ({memory_used_mb:.1f} MB) exceeds limit "
                                     f"({self.config.performance.max_memory_usage_mb} MB)")
                        # Trigger garbage collection
                        gc.collect()
                
                except Exception as e:
                    logger.error(f"Error monitoring system resources: {e}")
        
        self.system_monitor_thread = threading.Thread(target=monitor_system, daemon=True)
        self.system_monitor_thread.start()
        logger.info("Started system resource monitoring")
    
    def start_operation(self, operation_id: str, service_name: str) -> None:
        """Start tracking an operation."""
        with self.metrics_lock:
            self.active_operations[operation_id] = time.time()
            
            if service_name not in self.metrics:
                self.metrics[service_name] = ServiceMetrics(service_name=service_name)
            
            if self.metrics[service_name].start_time is None:
                self.metrics[service_name].start_time = time.time()
    
    def end_operation(self, operation_id: str, service_name: str, 
                     success: bool, resource_count: int = 0, 
                     error_message: Optional[str] = None) -> float:
        """
        End tracking an operation and record metrics.
        
        Returns:
            Duration in milliseconds
        """
        end_time = time.time()
        
        with self.metrics_lock:
            start_time = self.active_operations.pop(operation_id, end_time)
            duration_ms = (end_time - start_time) * 1000
            
            if service_name not in self.metrics:
                self.metrics[service_name] = ServiceMetrics(service_name=service_name)
            
            service_metrics = self.metrics[service_name]
            service_metrics.total_requests += 1
            service_metrics.total_duration_ms += duration_ms
            service_metrics.min_duration_ms = min(service_metrics.min_duration_ms, duration_ms)
            service_metrics.max_duration_ms = max(service_metrics.max_duration_ms, duration_ms)
            service_metrics.resources_fetched += resource_count
            service_metrics.end_time = end_time
            
            if success:
                service_metrics.successful_requests += 1
            else:
                service_metrics.failed_requests += 1
                if error_message:
                    service_metrics.errors.append(error_message)
            
            return duration_ms
    
    def record_custom_metric(self, metric_name: str, value: float, 
                           labels: Optional[Dict[str, str]] = None) -> None:
        """Record a custom metric."""
        metric_point = MetricPoint(
            timestamp=time.time(),
            value=value,
            labels=labels or {}
        )
        
        with self.metrics_lock:
            self.custom_metrics[metric_name].append(metric_point)
    
    def get_service_metrics(self, service_name: Optional[str] = None) -> Dict[str, ServiceMetrics]:
        """Get metrics for a specific service or all services."""
        with self.metrics_lock:
            if service_name:
                return {service_name: self.metrics.get(service_name, ServiceMetrics(service_name))}
            return self.metrics.copy()
    
    def get_system_metrics(self, last_n: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get system metrics."""
        with self.metrics_lock:
            metrics_list = list(self.system_metrics)
            if last_n:
                return metrics_list[-last_n:]
            return metrics_list
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a comprehensive performance summary."""
        with self.metrics_lock:
            total_runtime = time.time() - self.start_time
            
            # Aggregate service metrics
            total_requests = sum(m.total_requests for m in self.metrics.values())
            total_successful = sum(m.successful_requests for m in self.metrics.values())
            total_resources = sum(m.resources_fetched for m in self.metrics.values())
            
            # Calculate overall success rate
            overall_success_rate = total_successful / total_requests if total_requests > 0 else 0
            
            # Get current system metrics
            current_system = {}
            if self.system_metrics:
                latest_system = self.system_metrics[-1]
                current_system = {
                    'cpu_percent': latest_system.get('cpu_percent', 0),
                    'memory_percent': latest_system.get('memory_percent', 0),
                    'memory_used_mb': latest_system.get('memory_used_mb', 0),
                    'disk_percent': latest_system.get('disk_percent', 0)
                }
            
            # Service performance breakdown
            service_breakdown = {}
            for service_name, metrics in self.metrics.items():
                service_breakdown[service_name] = {
                    'total_requests': metrics.total_requests,
                    'success_rate': metrics.success_rate,
                    'average_duration_ms': metrics.average_duration_ms,
                    'resources_fetched': metrics.resources_fetched,
                    'error_count': len(metrics.errors)
                }
            
            return {
                'runtime_seconds': total_runtime,
                'total_requests': total_requests,
                'total_successful_requests': total_successful,
                'overall_success_rate': overall_success_rate,
                'total_resources_fetched': total_resources,
                'resources_per_second': total_resources / total_runtime if total_runtime > 0 else 0,
                'active_operations': len(self.active_operations),
                'current_system_metrics': current_system,
                'service_breakdown': service_breakdown,
                'summary_generated_at': datetime.now(timezone.utc).isoformat()
            }
    
    def get_slow_operations(self, threshold_ms: float = 5000) -> List[Dict[str, Any]]:
        """Get operations that took longer than the threshold."""
        slow_ops = []
        
        with self.metrics_lock:
            for service_name, metrics in self.metrics.items():
                if metrics.max_duration_ms > threshold_ms:
                    slow_ops.append({
                        'service_name': service_name,
                        'max_duration_ms': metrics.max_duration_ms,
                        'average_duration_ms': metrics.average_duration_ms,
                        'total_requests': metrics.total_requests
                    })
        
        return sorted(slow_ops, key=lambda x: x['max_duration_ms'], reverse=True)
    
    def export_metrics_json(self) -> str:
        """Export all metrics as JSON."""
        with self.metrics_lock:
            export_data = {
                'performance_summary': self.get_performance_summary(),
                'service_metrics': {
                    name: {
                        'service_name': metrics.service_name,
                        'total_requests': metrics.total_requests,
                        'successful_requests': metrics.successful_requests,
                        'failed_requests': metrics.failed_requests,
                        'success_rate': metrics.success_rate,
                        'total_duration_ms': metrics.total_duration_ms,
                        'average_duration_ms': metrics.average_duration_ms,
                        'min_duration_ms': metrics.min_duration_ms if metrics.min_duration_ms != float('inf') else 0,
                        'max_duration_ms': metrics.max_duration_ms,
                        'resources_fetched': metrics.resources_fetched,
                        'error_count': len(metrics.errors),
                        'errors': metrics.errors[-10:]  # Last 10 errors
                    }
                    for name, metrics in self.metrics.items()
                },
                'system_metrics': list(self.system_metrics)[-100:],  # Last 100 data points
                'custom_metrics': {
                    name: [
                        {
                            'timestamp': point.timestamp,
                            'value': point.value,
                            'labels': point.labels
                        }
                        for point in points
                    ]
                    for name, points in self.custom_metrics.items()
                }
            }
            
            return json.dumps(export_data, indent=2, default=str)
    
    def reset_metrics(self) -> None:
        """Reset all metrics."""
        with self.metrics_lock:
            self.metrics.clear()
            self.system_metrics.clear()
            self.custom_metrics.clear()
            self.active_operations.clear()
            self.start_time = time.time()
        
        logger.info("All metrics have been reset")
    
    def stop(self) -> None:
        """Stop the performance monitor."""
        if self.system_monitor_thread:
            self.stop_monitoring.set()
            self.system_monitor_thread.join(timeout=5)
            logger.info("Stopped system resource monitoring")


class PerformanceProfiler:
    """Context manager for profiling operations."""
    
    def __init__(self, monitor: PerformanceMonitor, operation_id: str, service_name: str):
        self.monitor = monitor
        self.operation_id = operation_id
        self.service_name = service_name
        self.resource_count = 0
        self.error_message = None
        self.success = True
    
    def __enter__(self):
        self.monitor.start_operation(self.operation_id, self.service_name)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.success = False
            self.error_message = str(exc_val)
        
        duration_ms = self.monitor.end_operation(
            self.operation_id, 
            self.service_name, 
            self.success, 
            self.resource_count, 
            self.error_message
        )
        
        logger.debug(f"Operation {self.operation_id} completed in {duration_ms:.2f}ms "
                    f"(success: {self.success}, resources: {self.resource_count})")
    
    def add_resources(self, count: int) -> None:
        """Add to the resource count."""
        self.resource_count += count
    
    def set_error(self, error_message: str) -> None:
        """Set an error for this operation."""
        self.success = False
        self.error_message = error_message
