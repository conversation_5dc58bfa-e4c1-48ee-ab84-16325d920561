"""
Health check and monitoring system for GCP Inventory Tool.
"""
import logging
import time
import threading
import json
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timezone
import psutil
from google.oauth2.credentials import Credentials
from google.cloud import compute_v1
from google.api_core import exceptions as api_exceptions

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """Health check status values."""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check."""
    name: str
    status: HealthStatus
    message: str
    timestamp: float
    duration_ms: float
    metadata: Optional[Dict[str, Any]] = None


class HealthChecker:
    """Performs various health checks for the system."""
    
    def __init__(self, config: 'InventoryConfig'):
        self.config = config
        self.last_results: Dict[str, HealthCheckResult] = {}
        self.lock = threading.Lock()
    
    def check_system_resources(self) -> HealthCheckResult:
        """Check system resource health."""
        start_time = time.time()
        
        try:
            # Get system metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            # Determine health status
            status = HealthStatus.HEALTHY
            issues = []
            
            # Check CPU usage
            if cpu_percent > 90:
                status = HealthStatus.UNHEALTHY
                issues.append(f"High CPU usage: {cpu_percent:.1f}%")
            elif cpu_percent > 70:
                status = HealthStatus.DEGRADED
                issues.append(f"Elevated CPU usage: {cpu_percent:.1f}%")
            
            # Check memory usage
            if memory.percent > 95:
                status = HealthStatus.UNHEALTHY
                issues.append(f"Critical memory usage: {memory.percent:.1f}%")
            elif memory.percent > 80:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                issues.append(f"High memory usage: {memory.percent:.1f}%")
            
            # Check disk usage
            if disk.percent > 95:
                status = HealthStatus.UNHEALTHY
                issues.append(f"Critical disk usage: {disk.percent:.1f}%")
            elif disk.percent > 85:
                if status == HealthStatus.HEALTHY:
                    status = HealthStatus.DEGRADED
                issues.append(f"High disk usage: {disk.percent:.1f}%")
            
            message = "System resources healthy" if not issues else "; ".join(issues)
            
            metadata = {
                'cpu_percent': cpu_percent,
                'memory_percent': memory.percent,
                'memory_used_gb': memory.used / (1024**3),
                'memory_total_gb': memory.total / (1024**3),
                'disk_percent': disk.percent,
                'disk_used_gb': disk.used / (1024**3),
                'disk_total_gb': disk.total / (1024**3)
            }
            
        except Exception as e:
            status = HealthStatus.UNKNOWN
            message = f"Failed to check system resources: {e}"
            metadata = {'error': str(e)}
        
        duration_ms = (time.time() - start_time) * 1000
        
        return HealthCheckResult(
            name="system_resources",
            status=status,
            message=message,
            timestamp=time.time(),
            duration_ms=duration_ms,
            metadata=metadata
        )
    
    def check_gcp_connectivity(self, project_id: str, credentials: Credentials) -> HealthCheckResult:
        """Check connectivity to GCP APIs."""
        start_time = time.time()
        
        try:
            # Try to list regions as a basic connectivity test
            client = compute_v1.RegionsClient(credentials=credentials)
            request = compute_v1.ListRegionsRequest(project=project_id)
            
            # Set a short timeout for health check
            regions = list(client.list(request=request, timeout=10))
            
            status = HealthStatus.HEALTHY
            message = f"GCP connectivity healthy - {len(regions)} regions accessible"
            metadata = {
                'project_id': project_id,
                'regions_count': len(regions),
                'test_type': 'compute_regions_list'
            }
            
        except api_exceptions.Forbidden as e:
            status = HealthStatus.DEGRADED
            message = f"GCP connectivity degraded - permission issues: {e}"
            metadata = {'error': str(e), 'error_type': 'permission'}
            
        except api_exceptions.DeadlineExceeded as e:
            status = HealthStatus.UNHEALTHY
            message = f"GCP connectivity unhealthy - timeout: {e}"
            metadata = {'error': str(e), 'error_type': 'timeout'}
            
        except Exception as e:
            status = HealthStatus.UNHEALTHY
            message = f"GCP connectivity unhealthy - {e}"
            metadata = {'error': str(e), 'error_type': 'unknown'}
        
        finally:
            try:
                if 'client' in locals():
                    client.transport.close()
            except Exception:
                pass
        
        duration_ms = (time.time() - start_time) * 1000
        
        return HealthCheckResult(
            name="gcp_connectivity",
            status=status,
            message=message,
            timestamp=time.time(),
            duration_ms=duration_ms,
            metadata=metadata
        )
    
    def check_configuration(self) -> HealthCheckResult:
        """Check configuration health."""
        start_time = time.time()
        
        try:
            # Validate configuration
            errors = self.config.validate()
            
            if not errors:
                status = HealthStatus.HEALTHY
                message = "Configuration is valid"
                metadata = {
                    'projects_count': len(self.config.projects),
                    'regions_count': len(self.config.regions),
                    'environment': self.config.environment
                }
            else:
                status = HealthStatus.UNHEALTHY
                message = f"Configuration errors: {'; '.join(errors)}"
                metadata = {'errors': errors}
                
        except Exception as e:
            status = HealthStatus.UNKNOWN
            message = f"Failed to validate configuration: {e}"
            metadata = {'error': str(e)}
        
        duration_ms = (time.time() - start_time) * 1000
        
        return HealthCheckResult(
            name="configuration",
            status=status,
            message=message,
            timestamp=time.time(),
            duration_ms=duration_ms,
            metadata=metadata
        )
    
    def check_dependencies(self) -> HealthCheckResult:
        """Check external dependencies."""
        start_time = time.time()
        
        try:
            # Check required Python packages
            required_packages = [
                'google-cloud-compute',
                'google-cloud-storage',
                'google-cloud-monitoring',
                'pandas',
                'openpyxl',
                'pyyaml'
            ]
            
            missing_packages = []
            for package in required_packages:
                try:
                    __import__(package.replace('-', '_'))
                except ImportError:
                    missing_packages.append(package)
            
            if not missing_packages:
                status = HealthStatus.HEALTHY
                message = "All dependencies available"
                metadata = {'required_packages': required_packages}
            else:
                status = HealthStatus.UNHEALTHY
                message = f"Missing dependencies: {', '.join(missing_packages)}"
                metadata = {
                    'missing_packages': missing_packages,
                    'required_packages': required_packages
                }
                
        except Exception as e:
            status = HealthStatus.UNKNOWN
            message = f"Failed to check dependencies: {e}"
            metadata = {'error': str(e)}
        
        duration_ms = (time.time() - start_time) * 1000
        
        return HealthCheckResult(
            name="dependencies",
            status=status,
            message=message,
            timestamp=time.time(),
            duration_ms=duration_ms,
            metadata=metadata
        )
    
    def run_all_checks(self, project_id: Optional[str] = None, 
                      credentials: Optional[Credentials] = None) -> Dict[str, HealthCheckResult]:
        """Run all health checks."""
        results = {}
        
        # Always run these checks
        results['system_resources'] = self.check_system_resources()
        results['configuration'] = self.check_configuration()
        results['dependencies'] = self.check_dependencies()
        
        # Run GCP connectivity check if credentials provided
        if project_id and credentials:
            results['gcp_connectivity'] = self.check_gcp_connectivity(project_id, credentials)
        
        # Store results
        with self.lock:
            self.last_results.update(results)
        
        return results
    
    def get_overall_health(self) -> HealthStatus:
        """Get overall system health status."""
        with self.lock:
            if not self.last_results:
                return HealthStatus.UNKNOWN
            
            statuses = [result.status for result in self.last_results.values()]
            
            if any(status == HealthStatus.UNHEALTHY for status in statuses):
                return HealthStatus.UNHEALTHY
            elif any(status == HealthStatus.DEGRADED for status in statuses):
                return HealthStatus.DEGRADED
            elif all(status == HealthStatus.HEALTHY for status in statuses):
                return HealthStatus.HEALTHY
            else:
                return HealthStatus.UNKNOWN
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get a summary of health check results."""
        with self.lock:
            overall_status = self.get_overall_health()
            
            summary = {
                'overall_status': overall_status.value,
                'timestamp': time.time(),
                'checks': {}
            }
            
            for name, result in self.last_results.items():
                summary['checks'][name] = {
                    'status': result.status.value,
                    'message': result.message,
                    'timestamp': result.timestamp,
                    'duration_ms': result.duration_ms
                }
            
            return summary


class HealthMonitor:
    """Continuous health monitoring with alerting."""
    
    def __init__(self, config: 'InventoryConfig', health_checker: HealthChecker):
        self.config = config
        self.health_checker = health_checker
        self.monitoring_thread = None
        self.stop_monitoring = threading.Event()
        self.alert_callbacks: List[Callable] = []
        
        # Health history
        self.health_history: List[Dict[str, Any]] = []
        self.max_history_size = 1000
        
    def add_alert_callback(self, callback: Callable[[HealthStatus, Dict[str, Any]], None]) -> None:
        """Add a callback for health status changes."""
        self.alert_callbacks.append(callback)
    
    def start_monitoring(self, interval: int = 60, project_id: Optional[str] = None,
                        credentials: Optional[Credentials] = None) -> None:
        """Start continuous health monitoring."""
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            logger.warning("Health monitoring is already running")
            return
        
        def monitor():
            logger.info(f"Started health monitoring with {interval}s interval")
            last_overall_status = HealthStatus.UNKNOWN
            
            while not self.stop_monitoring.wait(interval):
                try:
                    # Run health checks
                    results = self.health_checker.run_all_checks(project_id, credentials)
                    overall_status = self.health_checker.get_overall_health()
                    
                    # Store in history
                    health_summary = self.health_checker.get_health_summary()
                    self.health_history.append(health_summary)
                    
                    # Trim history if needed
                    if len(self.health_history) > self.max_history_size:
                        self.health_history = self.health_history[-self.max_history_size:]
                    
                    # Check for status changes and trigger alerts
                    if overall_status != last_overall_status:
                        logger.info(f"Health status changed: {last_overall_status.value} -> {overall_status.value}")
                        
                        for callback in self.alert_callbacks:
                            try:
                                callback(overall_status, health_summary)
                            except Exception as e:
                                logger.error(f"Error in health alert callback: {e}")
                        
                        last_overall_status = overall_status
                    
                except Exception as e:
                    logger.error(f"Error during health monitoring: {e}")
        
        self.monitoring_thread = threading.Thread(target=monitor, daemon=True)
        self.monitoring_thread.start()
    
    def stop_monitoring(self) -> None:
        """Stop health monitoring."""
        if self.monitoring_thread:
            self.stop_monitoring.set()
            self.monitoring_thread.join(timeout=5)
            logger.info("Stopped health monitoring")
    
    def get_health_trends(self, hours: int = 24) -> Dict[str, Any]:
        """Get health trends over the specified time period."""
        cutoff_time = time.time() - (hours * 3600)
        
        recent_history = [
            entry for entry in self.health_history
            if entry['timestamp'] > cutoff_time
        ]
        
        if not recent_history:
            return {'message': 'No health data available for the specified period'}
        
        # Calculate trends
        status_counts = {}
        check_performance = {}
        
        for entry in recent_history:
            overall_status = entry['overall_status']
            status_counts[overall_status] = status_counts.get(overall_status, 0) + 1
            
            for check_name, check_data in entry['checks'].items():
                if check_name not in check_performance:
                    check_performance[check_name] = {
                        'durations': [],
                        'status_counts': {}
                    }
                
                check_performance[check_name]['durations'].append(check_data['duration_ms'])
                status = check_data['status']
                check_performance[check_name]['status_counts'][status] = \
                    check_performance[check_name]['status_counts'].get(status, 0) + 1
        
        # Calculate averages
        for check_name, data in check_performance.items():
            durations = data['durations']
            data['avg_duration_ms'] = sum(durations) / len(durations)
            data['max_duration_ms'] = max(durations)
            data['min_duration_ms'] = min(durations)
        
        return {
            'period_hours': hours,
            'total_checks': len(recent_history),
            'overall_status_distribution': status_counts,
            'check_performance': check_performance,
            'first_check': recent_history[0]['timestamp'],
            'last_check': recent_history[-1]['timestamp']
        }
