"""
Production-ready configuration management with validation, security, and environment support.
"""
import os
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from pathlib import Path
import yaml
from google.oauth2.credentials import Credentials

logger = logging.getLogger(__name__)


@dataclass
class SecurityConfig:
    """Security-related configuration."""
    enable_audit_logging: bool = True
    max_concurrent_requests: int = 10
    request_timeout: int = 300
    retry_max_attempts: int = 3
    retry_backoff_factor: float = 2.0
    enable_rate_limiting: bool = True
    rate_limit_requests_per_minute: int = 100
    
    # Credential security
    credential_cache_ttl: int = 3600  # 1 hour
    enable_credential_rotation: bool = False
    
    # Data security
    mask_sensitive_data: bool = True
    sensitive_fields: List[str] = field(default_factory=lambda: [
        'password', 'secret', 'key', 'token', 'credential'
    ])


@dataclass
class PerformanceConfig:
    """Performance-related configuration."""
    max_workers: int = 5
    enable_caching: bool = True
    cache_ttl: int = 3600  # 1 hour
    batch_size: int = 100
    
    # Memory management
    max_memory_usage_mb: int = 2048
    enable_memory_monitoring: bool = True
    
    # Network optimization
    connection_pool_size: int = 10
    keep_alive_timeout: int = 30
    
    # Region optimization
    enable_dynamic_regions: bool = True
    region_cache_ttl: int = 3600
    max_regions_per_service: int = 50


@dataclass
class MonitoringConfig:
    """Monitoring and observability configuration."""
    enable_metrics: bool = True
    enable_tracing: bool = False
    enable_health_checks: bool = True
    
    # Logging
    log_level: str = "INFO"
    enable_structured_logging: bool = True
    log_format: str = "json"
    
    # Metrics
    metrics_port: int = 8080
    metrics_path: str = "/metrics"
    
    # Health checks
    health_check_interval: int = 60
    health_check_timeout: int = 10


@dataclass
class ComplianceConfig:
    """Compliance and governance configuration."""
    enable_data_classification: bool = True
    enable_pii_detection: bool = True
    data_retention_days: int = 90
    
    # Audit
    enable_audit_trail: bool = True
    audit_log_path: str = "/var/log/gcp-inventory/audit.log"
    
    # Privacy
    anonymize_user_data: bool = False
    redact_sensitive_fields: bool = True


@dataclass
class OutputConfig:
    """Output configuration."""
    format: str = "json"
    file: Optional[str] = None
    include_empty: bool = False
    compress_output: bool = False
    
    # Excel specific
    excel_max_rows_per_sheet: int = 1000000
    excel_enable_formatting: bool = True
    
    # JSON specific
    json_indent: int = 2
    json_sort_keys: bool = True
    
    # CSV specific
    csv_delimiter: str = ","
    csv_quote_char: str = '"'


@dataclass
class ServiceConfig:
    """Service-specific configuration."""
    enabled_services: List[str] = field(default_factory=list)
    disabled_services: List[str] = field(default_factory=list)
    service_timeouts: Dict[str, int] = field(default_factory=dict)
    service_retry_configs: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    
    # Region overrides per service
    service_regions: Dict[str, List[str]] = field(default_factory=dict)


@dataclass
class InventoryConfig:
    """Main configuration class."""
    # Core settings
    projects: List[str] = field(default_factory=list)
    regions: List[str] = field(default_factory=list)
    
    # Component configurations
    security: SecurityConfig = field(default_factory=SecurityConfig)
    performance: PerformanceConfig = field(default_factory=PerformanceConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    compliance: ComplianceConfig = field(default_factory=ComplianceConfig)
    output: OutputConfig = field(default_factory=OutputConfig)
    services: ServiceConfig = field(default_factory=ServiceConfig)
    
    # Environment
    environment: str = "production"
    debug: bool = False
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of errors."""
        errors = []
        
        # Validate projects
        if not self.projects:
            errors.append("At least one project must be specified")
        
        # Validate security settings
        if self.security.max_concurrent_requests < 1:
            errors.append("max_concurrent_requests must be at least 1")
        
        if self.security.request_timeout < 10:
            errors.append("request_timeout must be at least 10 seconds")
        
        # Validate performance settings
        if self.performance.max_workers < 1:
            errors.append("max_workers must be at least 1")
        
        if self.performance.max_workers > 20:
            errors.append("max_workers should not exceed 20 for stability")
        
        # Validate output settings
        valid_formats = ["json", "csv", "excel", "console"]
        if self.output.format not in valid_formats:
            errors.append(f"output format must be one of: {valid_formats}")
        
        # Validate log level
        valid_log_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.monitoring.log_level not in valid_log_levels:
            errors.append(f"log_level must be one of: {valid_log_levels}")
        
        return errors
    
    def apply_environment_overrides(self) -> None:
        """Apply environment-specific overrides."""
        env = self.environment.lower()
        
        if env == "development":
            self.debug = True
            self.monitoring.log_level = "DEBUG"
            self.security.enable_audit_logging = False
            self.performance.enable_caching = False
        elif env == "testing":
            self.monitoring.log_level = "INFO"
            self.security.max_concurrent_requests = 5
            self.performance.max_workers = 2
        elif env == "production":
            self.debug = False
            self.monitoring.log_level = "INFO"
            self.security.enable_audit_logging = True
            self.compliance.enable_audit_trail = True
    
    def get_service_regions(self, service_name: str) -> Optional[List[str]]:
        """Get regions for a specific service."""
        return self.services.service_regions.get(service_name, self.regions)
    
    def is_service_enabled(self, service_name: str) -> bool:
        """Check if a service is enabled."""
        if self.services.disabled_services and service_name in self.services.disabled_services:
            return False
        
        if self.services.enabled_services:
            return service_name in self.services.enabled_services
        
        return True  # Default to enabled if no explicit configuration


def load_config_from_file(config_path: str) -> Dict[str, Any]:
    """Load configuration from YAML file with validation."""
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Configuration file not found: {config_path}")
    
    try:
        with open(config_path, 'r') as f:
            config_data = yaml.safe_load(f)
        
        if config_data is None:
            return {}
        
        if not isinstance(config_data, dict):
            raise ValueError(f"Configuration file must contain a dictionary, got {type(config_data)}")
        
        return config_data
    
    except yaml.YAMLError as e:
        raise ValueError(f"Invalid YAML in configuration file: {e}")
    except Exception as e:
        raise RuntimeError(f"Failed to load configuration file: {e}")


def create_config_from_dict(config_dict: Dict[str, Any]) -> InventoryConfig:
    """Create InventoryConfig from dictionary with proper type conversion."""
    config = InventoryConfig()
    
    # Core settings
    if 'projects' in config_dict:
        config.projects = config_dict['projects']
    
    if 'regions' in config_dict:
        config.regions = config_dict['regions']
    
    if 'environment' in config_dict:
        config.environment = config_dict['environment']
    
    if 'debug' in config_dict:
        config.debug = config_dict['debug']
    
    # Load component configurations
    if 'security' in config_dict:
        _update_dataclass_from_dict(config.security, config_dict['security'])
    
    if 'performance' in config_dict:
        _update_dataclass_from_dict(config.performance, config_dict['performance'])
    
    if 'monitoring' in config_dict:
        _update_dataclass_from_dict(config.monitoring, config_dict['monitoring'])
    
    if 'compliance' in config_dict:
        _update_dataclass_from_dict(config.compliance, config_dict['compliance'])
    
    if 'output' in config_dict:
        _update_dataclass_from_dict(config.output, config_dict['output'])
    
    if 'services' in config_dict:
        _update_dataclass_from_dict(config.services, config_dict['services'])
    
    return config


def _update_dataclass_from_dict(dataclass_instance: Any, data_dict: Dict[str, Any]) -> None:
    """Update dataclass instance with values from dictionary."""
    for key, value in data_dict.items():
        if hasattr(dataclass_instance, key):
            setattr(dataclass_instance, key, value)
        else:
            logger.warning(f"Unknown configuration key: {key}")


def load_config_from_environment() -> Dict[str, Any]:
    """Load configuration from environment variables."""
    config = {}
    
    # Core settings
    if os.getenv('GCP_INVENTORY_PROJECTS'):
        config['projects'] = os.getenv('GCP_INVENTORY_PROJECTS').split(',')
    
    if os.getenv('GCP_INVENTORY_REGIONS'):
        config['regions'] = os.getenv('GCP_INVENTORY_REGIONS').split(',')
    
    if os.getenv('GCP_INVENTORY_ENVIRONMENT'):
        config['environment'] = os.getenv('GCP_INVENTORY_ENVIRONMENT')
    
    # Security settings
    security_config = {}
    if os.getenv('GCP_INVENTORY_MAX_CONCURRENT_REQUESTS'):
        security_config['max_concurrent_requests'] = int(os.getenv('GCP_INVENTORY_MAX_CONCURRENT_REQUESTS'))
    
    if os.getenv('GCP_INVENTORY_REQUEST_TIMEOUT'):
        security_config['request_timeout'] = int(os.getenv('GCP_INVENTORY_REQUEST_TIMEOUT'))
    
    if security_config:
        config['security'] = security_config
    
    # Performance settings
    performance_config = {}
    if os.getenv('GCP_INVENTORY_MAX_WORKERS'):
        performance_config['max_workers'] = int(os.getenv('GCP_INVENTORY_MAX_WORKERS'))
    
    if performance_config:
        config['performance'] = performance_config
    
    # Output settings
    output_config = {}
    if os.getenv('GCP_INVENTORY_OUTPUT_FORMAT'):
        output_config['format'] = os.getenv('GCP_INVENTORY_OUTPUT_FORMAT')
    
    if os.getenv('GCP_INVENTORY_OUTPUT_FILE'):
        output_config['file'] = os.getenv('GCP_INVENTORY_OUTPUT_FILE')
    
    if output_config:
        config['output'] = output_config
    
    return config
