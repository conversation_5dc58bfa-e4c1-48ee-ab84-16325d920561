"""
Error handling and resilience module for GCP Inventory Tool.
Provides retry logic, circuit breakers, and graceful degradation.
"""
import logging
import time
import random
from typing import Dict, List, Any, Optional, Callable, Type
from dataclasses import dataclass
from enum import Enum
import threading
from functools import wraps
from google.api_core import exceptions as api_exceptions

logger = logging.getLogger(__name__)


class CircuitState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, reject requests
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class RetryConfig:
    """Configuration for retry behavior."""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True
    
    # Specific exceptions to retry
    retryable_exceptions: List[Type[Exception]] = None
    
    def __post_init__(self):
        if self.retryable_exceptions is None:
            self.retryable_exceptions = [
                api_exceptions.DeadlineExceeded,
                api_exceptions.ServiceUnavailable,
                api_exceptions.InternalServerError,
                api_exceptions.TooManyRequests,
                ConnectionError,
                TimeoutError
            ]


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker."""
    failure_threshold: int = 5
    recovery_timeout: float = 60.0
    success_threshold: int = 3  # For half-open state
    timeout: float = 30.0


class CircuitBreaker:
    """Circuit breaker implementation for service resilience."""
    
    def __init__(self, name: str, config: CircuitBreakerConfig):
        self.name = name
        self.config = config
        self.state = CircuitState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time = 0
        self.lock = threading.Lock()
    
    def call(self, func: Callable, *args, **kwargs) -> Any:
        """Execute function with circuit breaker protection."""
        with self.lock:
            if self.state == CircuitState.OPEN:
                if time.time() - self.last_failure_time > self.config.recovery_timeout:
                    self.state = CircuitState.HALF_OPEN
                    self.success_count = 0
                    logger.info(f"Circuit breaker {self.name} transitioning to HALF_OPEN")
                else:
                    raise CircuitBreakerOpenError(f"Circuit breaker {self.name} is OPEN")
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except Exception as e:
            self._on_failure()
            raise
    
    def _on_success(self):
        """Handle successful operation."""
        with self.lock:
            if self.state == CircuitState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitState.CLOSED
                    self.failure_count = 0
                    logger.info(f"Circuit breaker {self.name} transitioning to CLOSED")
            elif self.state == CircuitState.CLOSED:
                self.failure_count = 0
    
    def _on_failure(self):
        """Handle failed operation."""
        with self.lock:
            self.failure_count += 1
            self.last_failure_time = time.time()
            
            if self.state == CircuitState.CLOSED:
                if self.failure_count >= self.config.failure_threshold:
                    self.state = CircuitState.OPEN
                    logger.warning(f"Circuit breaker {self.name} transitioning to OPEN")
            elif self.state == CircuitState.HALF_OPEN:
                self.state = CircuitState.OPEN
                logger.warning(f"Circuit breaker {self.name} transitioning back to OPEN")
    
    def get_state(self) -> Dict[str, Any]:
        """Get current circuit breaker state."""
        with self.lock:
            return {
                'name': self.name,
                'state': self.state.value,
                'failure_count': self.failure_count,
                'success_count': self.success_count,
                'last_failure_time': self.last_failure_time
            }


class CircuitBreakerOpenError(Exception):
    """Raised when circuit breaker is open."""
    pass


class ResilienceManager:
    """Manages retry logic and circuit breakers for services."""
    
    def __init__(self, config: 'InventoryConfig'):
        self.config = config
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}
        self.retry_configs: Dict[str, RetryConfig] = {}
        self.lock = threading.Lock()
        
        # Initialize default configurations
        self._setup_default_configs()
    
    def _setup_default_configs(self):
        """Setup default retry and circuit breaker configurations."""
        # Default retry config
        default_retry = RetryConfig(
            max_attempts=self.config.security.retry_max_attempts,
            base_delay=1.0,
            max_delay=30.0,
            exponential_base=self.config.security.retry_backoff_factor
        )
        
        # Service-specific retry configs
        self.retry_configs = {
            'default': default_retry,
            'compute': RetryConfig(max_attempts=5, base_delay=2.0),
            'storage': RetryConfig(max_attempts=3, base_delay=1.0),
            'artifact_registry_repo': RetryConfig(max_attempts=4, base_delay=1.5),
            'container_registry_repo': RetryConfig(max_attempts=4, base_delay=1.5),
        }
        
        # Default circuit breaker config
        default_cb_config = CircuitBreakerConfig(
            failure_threshold=5,
            recovery_timeout=60.0,
            success_threshold=3,
            timeout=self.config.security.request_timeout
        )
        
        # Initialize circuit breakers for common services
        common_services = [
            'compute', 'storage', 'gke_cluster', 'cloud_function',
            'artifact_registry_repo', 'container_registry_repo'
        ]
        
        for service in common_services:
            self.circuit_breakers[service] = CircuitBreaker(service, default_cb_config)
    
    def get_circuit_breaker(self, service_name: str) -> CircuitBreaker:
        """Get or create circuit breaker for a service."""
        with self.lock:
            if service_name not in self.circuit_breakers:
                config = CircuitBreakerConfig()
                self.circuit_breakers[service_name] = CircuitBreaker(service_name, config)
            return self.circuit_breakers[service_name]
    
    def get_retry_config(self, service_name: str) -> RetryConfig:
        """Get retry configuration for a service."""
        return self.retry_configs.get(service_name, self.retry_configs['default'])
    
    def execute_with_resilience(self, func: Callable, service_name: str, 
                              operation_name: str, *args, **kwargs) -> Any:
        """
        Execute function with full resilience (retry + circuit breaker).
        
        Args:
            func: Function to execute
            service_name: Name of the service
            operation_name: Name of the operation (for logging)
            *args, **kwargs: Arguments to pass to the function
            
        Returns:
            Result of the function execution
            
        Raises:
            Exception: If all retry attempts fail or circuit breaker is open
        """
        circuit_breaker = self.get_circuit_breaker(service_name)
        retry_config = self.get_retry_config(service_name)
        
        def resilient_func():
            return self._retry_with_backoff(
                func, retry_config, service_name, operation_name, *args, **kwargs
            )
        
        return circuit_breaker.call(resilient_func)
    
    def _retry_with_backoff(self, func: Callable, retry_config: RetryConfig,
                           service_name: str, operation_name: str, 
                           *args, **kwargs) -> Any:
        """Execute function with retry and exponential backoff."""
        last_exception = None
        
        for attempt in range(retry_config.max_attempts):
            try:
                result = func(*args, **kwargs)
                if attempt > 0:
                    logger.info(f"[{service_name}] {operation_name} succeeded on attempt {attempt + 1}")
                return result
                
            except Exception as e:
                last_exception = e
                
                # Check if exception is retryable
                if not self._is_retryable_exception(e, retry_config):
                    logger.error(f"[{service_name}] {operation_name} failed with non-retryable error: {e}")
                    raise
                
                # Don't sleep on the last attempt
                if attempt < retry_config.max_attempts - 1:
                    delay = self._calculate_delay(attempt, retry_config)
                    logger.warning(f"[{service_name}] {operation_name} failed on attempt {attempt + 1}, "
                                 f"retrying in {delay:.2f}s: {e}")
                    time.sleep(delay)
                else:
                    logger.error(f"[{service_name}] {operation_name} failed after {retry_config.max_attempts} attempts: {e}")
        
        raise last_exception
    
    def _is_retryable_exception(self, exception: Exception, retry_config: RetryConfig) -> bool:
        """Check if an exception is retryable."""
        return any(isinstance(exception, exc_type) for exc_type in retry_config.retryable_exceptions)
    
    def _calculate_delay(self, attempt: int, retry_config: RetryConfig) -> float:
        """Calculate delay for exponential backoff with jitter."""
        delay = retry_config.base_delay * (retry_config.exponential_base ** attempt)
        delay = min(delay, retry_config.max_delay)
        
        if retry_config.jitter:
            # Add random jitter (±25%)
            jitter_range = delay * 0.25
            delay += random.uniform(-jitter_range, jitter_range)
        
        return max(0, delay)
    
    def get_resilience_stats(self) -> Dict[str, Any]:
        """Get statistics about resilience components."""
        with self.lock:
            circuit_breaker_stats = {}
            for name, cb in self.circuit_breakers.items():
                circuit_breaker_stats[name] = cb.get_state()
            
            return {
                'circuit_breakers': circuit_breaker_stats,
                'retry_configs': {
                    name: {
                        'max_attempts': config.max_attempts,
                        'base_delay': config.base_delay,
                        'max_delay': config.max_delay
                    }
                    for name, config in self.retry_configs.items()
                }
            }
    
    def reset_circuit_breakers(self) -> None:
        """Reset all circuit breakers to closed state."""
        with self.lock:
            for cb in self.circuit_breakers.values():
                with cb.lock:
                    cb.state = CircuitState.CLOSED
                    cb.failure_count = 0
                    cb.success_count = 0
                    cb.last_failure_time = 0
        
        logger.info("All circuit breakers have been reset")


def with_resilience(service_name: str, operation_name: str = None):
    """
    Decorator to add resilience to a function.
    
    Args:
        service_name: Name of the service
        operation_name: Name of the operation (defaults to function name)
    """
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            # Get resilience manager from context or create a default one
            # This would typically be injected via dependency injection
            resilience_manager = getattr(wrapper, '_resilience_manager', None)
            if not resilience_manager:
                # Create a minimal resilience manager for standalone use
                from .settings import InventoryConfig
                config = InventoryConfig()
                resilience_manager = ResilienceManager(config)
            
            op_name = operation_name or func.__name__
            return resilience_manager.execute_with_resilience(
                func, service_name, op_name, *args, **kwargs
            )
        
        return wrapper
    return decorator


class GracefulDegradation:
    """Handles graceful degradation when services are unavailable."""
    
    def __init__(self, config: 'InventoryConfig'):
        self.config = config
        self.degraded_services: Dict[str, float] = {}  # service -> timestamp
        self.lock = threading.Lock()
    
    def mark_service_degraded(self, service_name: str) -> None:
        """Mark a service as degraded."""
        with self.lock:
            self.degraded_services[service_name] = time.time()
            logger.warning(f"Service {service_name} marked as degraded")
    
    def is_service_degraded(self, service_name: str, degradation_timeout: float = 300) -> bool:
        """Check if a service is currently degraded."""
        with self.lock:
            if service_name not in self.degraded_services:
                return False
            
            # Check if degradation has expired
            if time.time() - self.degraded_services[service_name] > degradation_timeout:
                del self.degraded_services[service_name]
                logger.info(f"Service {service_name} degradation expired")
                return False
            
            return True
    
    def get_degraded_services(self) -> List[str]:
        """Get list of currently degraded services."""
        with self.lock:
            return list(self.degraded_services.keys())
    
    def clear_degradation(self, service_name: Optional[str] = None) -> None:
        """Clear degradation for a specific service or all services."""
        with self.lock:
            if service_name:
                self.degraded_services.pop(service_name, None)
                logger.info(f"Cleared degradation for service {service_name}")
            else:
                self.degraded_services.clear()
                logger.info("Cleared degradation for all services")
